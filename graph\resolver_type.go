package graph

import (
	"goVwPlatformAPI/internal/database"
	"goVwPlatformAPI/internal/dashboard"
	"goVwPlatformAPI/internal/onboarding"
	"goVwPlatformAPI/internal/pagemanager"
	"goVwPlatformAPI/internal/subscription"
)

// Resolver contains all the dependencies needed by GraphQL resolvers.
// This struct is defined in a separate file to prevent gqlgen from overwriting it.
// DO NOT move this to resolver.go as gqlgen will regenerate that file.
type Resolver struct {
	DB                  *database.DB
	SubscriptionService *subscription.SubscriptionService
	PageManager         *pagemanager.EnhancedPageManager
	OnboardingService   *onboarding.OnboardingService
	DashboardService    *dashboard.DashboardService
}
