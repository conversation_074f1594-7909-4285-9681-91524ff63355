package graph

import (
	"goVwPlatformAPI/internal/database"
	"goVwPlatformAPI/internal/dashboard"
	"goVwPlatformAPI/internal/onboarding"
	"goVwPlatformAPI/internal/pagemanager"
	"goVwPlatformAPI/internal/subscription"
)

// Resolver is the root resolver struct that contains all the dependencies
// needed by the GraphQL resolvers. This struct is defined separately from
// the generated code so it won't be overwritten by gqlgen.
type Resolver struct {
	DB                  *database.DB
	SubscriptionService *subscription.SubscriptionService
	PageManager         *pagemanager.EnhancedPageManager
	OnboardingService   *onboarding.OnboardingService
	DashboardService    *dashboard.DashboardService
}
