package main

import (
	"fmt"
	"os"
	"strings"
)

func main() {
	// Read the resolver.go file
	resolverPath := "graph/resolver.go"
	content, err := os.ReadFile(resolverPath)
	if err != nil {
		fmt.Printf("Error reading %s: %v\n", resolverPath, err)
		os.Exit(1)
	}

	contentStr := string(content)

	// Replace empty Resolver struct with populated one
	emptyResolver := "type Resolver struct{}"
	populatedResolver := `type Resolver struct {
	DB                  *database.DB
	SubscriptionService *subscription.SubscriptionService
	PageManager         *pagemanager.EnhancedPageManager
	OnboardingService   *onboarding.OnboardingService
	DashboardService    *dashboard.DashboardService
}`

	// Check if we need to add imports
	requiredImports := []string{
		`"goVwPlatformAPI/internal/database"`,
		`"goVwPlatformAPI/internal/subscription"`,
		`"goVwPlatformAPI/internal/pagemanager"`,
		`"goVwPlatformAPI/internal/onboarding"`,
		`"goVwPlatformAPI/internal/dashboard"`,
	}

	// Only replace if it's currently empty
	if strings.Contains(contentStr, emptyResolver) {
		contentStr = strings.Replace(contentStr, emptyResolver, populatedResolver, 1)

		// Add missing imports
		for _, imp := range requiredImports {
			if !strings.Contains(contentStr, imp) {
				// Find the import block and add the import
				importBlock := "import ("
				if strings.Contains(contentStr, importBlock) {
					contentStr = strings.Replace(contentStr, importBlock, importBlock+"\n\t"+imp, 1)
				}
			}
		}

		// Write back to file
		err = os.WriteFile(resolverPath, []byte(contentStr), 0644)
		if err != nil {
			fmt.Printf("Error writing %s: %v\n", resolverPath, err)
			os.Exit(1)
		}

		fmt.Println("✅ Fixed Resolver struct with required fields and imports")
	} else {
		fmt.Println("ℹ️  Resolver struct already has fields or not found")
	}
}
