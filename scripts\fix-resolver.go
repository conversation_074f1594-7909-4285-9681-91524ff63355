package main

import (
	"fmt"
	"io/ioutil"
	"os"
	"strings"
)

func main() {
	// Read the resolver.go file
	resolverPath := "graph/resolver.go"
	content, err := ioutil.ReadFile(resolverPath)
	if err != nil {
		fmt.Printf("Error reading %s: %v\n", resolverPath, err)
		os.Exit(1)
	}

	contentStr := string(content)

	// Replace empty Resolver struct with populated one
	emptyResolver := "type Resolver struct{}"
	populatedResolver := `type Resolver struct {
	DB                  *database.DB
	SubscriptionService *subscription.SubscriptionService
	PageManager         *pagemanager.EnhancedPageManager
	OnboardingService   *onboarding.OnboardingService
	DashboardService    *dashboard.DashboardService
}`

	// Only replace if it's currently empty
	if strings.Contains(contentStr, emptyResolver) {
		contentStr = strings.Replace(contentStr, emptyResolver, populatedResolver, 1)
		
		// Write back to file
		err = ioutil.WriteFile(resolverPath, []byte(contentStr), 0644)
		if err != nil {
			fmt.Printf("Error writing %s: %v\n", resolverPath, err)
			os.Exit(1)
		}
		
		fmt.Println("✅ Fixed Resolver struct with required fields")
	} else {
		fmt.Println("ℹ️  Resolver struct already has fields or not found")
	}
}
