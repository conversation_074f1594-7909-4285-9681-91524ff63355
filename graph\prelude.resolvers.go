package graph

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.76

import (
	"context"
	"goVwPlatformAPI/graph/generated"

	"github.com/99designs/gqlgen/graphql/introspection"
)

// IsDeprecated is the resolver for the isDeprecated field.
func (r *__InputValueResolver) IsDeprecated(ctx context.Context, obj *introspection.InputValue) (bool, error) {
	panic("not implemented")
}

// DeprecationReason is the resolver for the deprecationReason field.
func (r *__InputValueResolver) DeprecationReason(ctx context.Context, obj *introspection.InputValue) (*string, error) {
	panic("not implemented")
}

// IsOneOf is the resolver for the isOneOf field.
func (r *__TypeResolver) IsOneOf(ctx context.Context, obj *introspection.Type) (*bool, error) {
	panic("not implemented")
}

// __InputValue returns generated.__InputValueResolver implementation.
func (r *Resolver) __InputValue() generated.__InputValueResolver { return &__InputValueResolver{r} }

// __Type returns generated.__TypeResolver implementation.
func (r *Resolver) __Type() generated.__TypeResolver { return &__TypeResolver{r} }

type __InputValueResolver struct{ *Resolver }
type __TypeResolver struct{ *Resolver }
