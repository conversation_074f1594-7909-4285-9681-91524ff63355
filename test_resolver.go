package main

// THIS CODE WILL BE UPDATED WITH SCHEMA CHANGES. PREVIOUS IMPLEMENTATION FOR SCHEMA CHANGES WILL BE KEPT IN THE COMMENT SECTION. IMPLEMENTATION FOR UNCHANGED SCHEMA WILL BE KEPT.

import (
	"context"

	"github.com/99designs/gqlgen/graphql/introspection"
)

type Resolver struct{}

// GenerateSiteAPIKey is the resolver for the generateSiteAPIKey field.
func (r *mutationResolver) GenerateSiteAPIKey(ctx context.Context, siteID string, permissions int) (*string, error) {
	panic("not implemented")
}

// Test is the resolver for the test field.
func (r *queryResolver) Test(ctx context.Context) (*string, error) {
	panic("not implemented")
}

// IsDeprecated is the resolver for the isDeprecated field.
func (r *__InputValueResolver) IsDeprecated(ctx context.Context, obj *introspection.InputValue) (bool, error) {
	panic("not implemented")
}

// DeprecationReason is the resolver for the deprecationReason field.
func (r *__InputValueResolver) DeprecationReason(ctx context.Context, obj *introspection.InputValue) (*string, error) {
	panic("not implemented")
}

// IsOneOf is the resolver for the isOneOf field.
func (r *__TypeResolver) IsOneOf(ctx context.Context, obj *introspection.Type) (*bool, error) {
	panic("not implemented")
}

// Mutation returns MutationResolver implementation.
func (r *Resolver) Mutation() MutationResolver { return &mutationResolver{r} }

// Query returns QueryResolver implementation.
func (r *Resolver) Query() QueryResolver { return &queryResolver{r} }

// __InputValue returns __InputValueResolver implementation.
func (r *Resolver) __InputValue() __InputValueResolver { return &__InputValueResolver{r} }

// __Type returns __TypeResolver implementation.
func (r *Resolver) __Type() __TypeResolver { return &__TypeResolver{r} }

type mutationResolver struct{ *Resolver }
type queryResolver struct{ *Resolver }
type __InputValueResolver struct{ *Resolver }
type __TypeResolver struct{ *Resolver }
