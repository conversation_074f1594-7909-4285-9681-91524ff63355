[{
	"resource": "/c:/Users/<USER>/Desktop/goVwPlatformAPI/cmd/api/main.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "CannotInferTypeArgs",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "CannotInferTypeArgs"
		}
	},
	"severity": 8,
	"message": "in call to lru.New, cannot infer T (declared at C:\\Users\\<USER>\\go\\pkg\\mod\\github.com\\99designs\\gqlgen@v0.17.76\\graphql\\handler\\lru\\lru.go:17:10)",
	"source": "compiler",
	"startLineNumber": 1297,
	"startColumn": 10,
	"endLineNumber": 1297,
	"endColumn": 22
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "ec.unmarshalInputJSON undefined (type *executionContext has no field or method unmarshalInputJSON)",
	"source": "compiler",
	"startLineNumber": 123074,
	"startColumn": 17,
	"endLineNumber": 123074,
	"endColumn": 35
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "ec._JSON undefined (type *executionContext has no field or method _JSON)",
	"source": "compiler",
	"startLineNumber": 123085,
	"startColumn": 12,
	"endLineNumber": 123085,
	"endColumn": 17
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "ec.unmarshalInputTime undefined (type *executionContext has no field or method unmarshalInputTime)",
	"source": "compiler",
	"startLineNumber": 124995,
	"startColumn": 17,
	"endLineNumber": 124995,
	"endColumn": 35
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "ec._Time undefined (type *executionContext has no field or method _Time)",
	"source": "compiler",
	"startLineNumber": 125000,
	"startColumn": 12,
	"endLineNumber": 125000,
	"endColumn": 17
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "ec.unmarshalInputJSON undefined (type *executionContext has no field or method unmarshalInputJSON)",
	"source": "compiler",
	"startLineNumber": 126237,
	"startColumn": 17,
	"endLineNumber": 126237,
	"endColumn": 35
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "ec._JSON undefined (type *executionContext has no field or method _JSON)",
	"source": "compiler",
	"startLineNumber": 126245,
	"startColumn": 12,
	"endLineNumber": 126245,
	"endColumn": 17
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "ec.unmarshalInputTime undefined (type *executionContext has no field or method unmarshalInputTime)",
	"source": "compiler",
	"startLineNumber": 126605,
	"startColumn": 17,
	"endLineNumber": 126605,
	"endColumn": 35
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "ec._Time undefined (type *executionContext has no field or method _Time)",
	"source": "compiler",
	"startLineNumber": 126613,
	"startColumn": 12,
	"endLineNumber": 126613,
	"endColumn": 17
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: crypto",
	"source": "compiler",
	"startLineNumber": 33,
	"startColumn": 6,
	"endLineNumber": 33,
	"endColumn": 12
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: jwt",
	"source": "compiler",
	"startLineNumber": 37,
	"startColumn": 11,
	"endLineNumber": 37,
	"endColumn": 14
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: jwt",
	"source": "compiler",
	"startLineNumber": 37,
	"startColumn": 29,
	"endLineNumber": 37,
	"endColumn": 32
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: jwt",
	"source": "compiler",
	"startLineNumber": 37,
	"startColumn": 53,
	"endLineNumber": 37,
	"endColumn": 56
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: jwt",
	"source": "compiler",
	"startLineNumber": 39,
	"startColumn": 14,
	"endLineNumber": 39,
	"endColumn": 17
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: os",
	"source": "compiler",
	"startLineNumber": 42,
	"startColumn": 48,
	"endLineNumber": 42,
	"endColumn": 50
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 55,
	"startColumn": 17,
	"endLineNumber": 55,
	"endColumn": 21
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 60,
	"startColumn": 6,
	"endLineNumber": 60,
	"endColumn": 10
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 84,
	"startColumn": 17,
	"endLineNumber": 84,
	"endColumn": 21
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 89,
	"startColumn": 6,
	"endLineNumber": 89,
	"endColumn": 10
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 98,
	"startColumn": 17,
	"endLineNumber": 98,
	"endColumn": 21
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 103,
	"startColumn": 6,
	"endLineNumber": 103,
	"endColumn": 10
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 122,
	"startColumn": 17,
	"endLineNumber": 122,
	"endColumn": 21
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 127,
	"startColumn": 6,
	"endLineNumber": 127,
	"endColumn": 10
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 146,
	"startColumn": 17,
	"endLineNumber": 146,
	"endColumn": 21
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 151,
	"startColumn": 6,
	"endLineNumber": 151,
	"endColumn": 10
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 170,
	"startColumn": 17,
	"endLineNumber": 170,
	"endColumn": 21
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 175,
	"startColumn": 6,
	"endLineNumber": 175,
	"endColumn": 10
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 194,
	"startColumn": 17,
	"endLineNumber": 194,
	"endColumn": 21
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 199,
	"startColumn": 6,
	"endLineNumber": 199,
	"endColumn": 10
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: crypto",
	"source": "compiler",
	"startLineNumber": 238,
	"startColumn": 23,
	"endLineNumber": 238,
	"endColumn": 29
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: jwt",
	"source": "compiler",
	"startLineNumber": 248,
	"startColumn": 11,
	"endLineNumber": 248,
	"endColumn": 14
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: jwt",
	"source": "compiler",
	"startLineNumber": 248,
	"startColumn": 29,
	"endLineNumber": 248,
	"endColumn": 32
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: jwt",
	"source": "compiler",
	"startLineNumber": 248,
	"startColumn": 53,
	"endLineNumber": 248,
	"endColumn": 56
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: jwt",
	"source": "compiler",
	"startLineNumber": 250,
	"startColumn": 14,
	"endLineNumber": 250,
	"endColumn": 17
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: os",
	"source": "compiler",
	"startLineNumber": 253,
	"startColumn": 48,
	"endLineNumber": 253,
	"endColumn": 50
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 266,
	"startColumn": 17,
	"endLineNumber": 266,
	"endColumn": 21
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 275,
	"startColumn": 17,
	"endLineNumber": 275,
	"endColumn": 21
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 294,
	"startColumn": 17,
	"endLineNumber": 294,
	"endColumn": 21
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 309,
	"startColumn": 17,
	"endLineNumber": 309,
	"endColumn": 21
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 319,
	"startColumn": 17,
	"endLineNumber": 319,
	"endColumn": 21
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 330,
	"startColumn": 17,
	"endLineNumber": 330,
	"endColumn": 21
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 350,
	"startColumn": 17,
	"endLineNumber": 350,
	"endColumn": 21
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 368,
	"startColumn": 17,
	"endLineNumber": 368,
	"endColumn": 21
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 386,
	"startColumn": 17,
	"endLineNumber": 386,
	"endColumn": 21
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 391,
	"startColumn": 6,
	"endLineNumber": 391,
	"endColumn": 10
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 400,
	"startColumn": 17,
	"endLineNumber": 400,
	"endColumn": 21
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 405,
	"startColumn": 6,
	"endLineNumber": 405,
	"endColumn": 10
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 414,
	"startColumn": 17,
	"endLineNumber": 414,
	"endColumn": 21
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 419,
	"startColumn": 6,
	"endLineNumber": 419,
	"endColumn": 10
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 428,
	"startColumn": 17,
	"endLineNumber": 428,
	"endColumn": 21
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 433,
	"startColumn": 6,
	"endLineNumber": 433,
	"endColumn": 10
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 447,
	"startColumn": 17,
	"endLineNumber": 447,
	"endColumn": 21
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 452,
	"startColumn": 6,
	"endLineNumber": 452,
	"endColumn": 10
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 615,
	"startColumn": 30,
	"endLineNumber": 615,
	"endColumn": 34
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 615,
	"startColumn": 53,
	"endLineNumber": 615,
	"endColumn": 57
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: sitemanager",
	"source": "compiler",
	"startLineNumber": 620,
	"startColumn": 17,
	"endLineNumber": 620,
	"endColumn": 28
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: sitemanager",
	"source": "compiler",
	"startLineNumber": 622,
	"startColumn": 18,
	"endLineNumber": 622,
	"endColumn": 29
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 674,
	"startColumn": 30,
	"endLineNumber": 674,
	"endColumn": 34
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 674,
	"startColumn": 53,
	"endLineNumber": 674,
	"endColumn": 57
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: sitemanager",
	"source": "compiler",
	"startLineNumber": 684,
	"startColumn": 17,
	"endLineNumber": 684,
	"endColumn": 28
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: sitemanager",
	"source": "compiler",
	"startLineNumber": 686,
	"startColumn": 18,
	"endLineNumber": 686,
	"endColumn": 29
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 742,
	"startColumn": 30,
	"endLineNumber": 742,
	"endColumn": 34
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 742,
	"startColumn": 53,
	"endLineNumber": 742,
	"endColumn": 57
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: sitemanager",
	"source": "compiler",
	"startLineNumber": 752,
	"startColumn": 17,
	"endLineNumber": 752,
	"endColumn": 28
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 777,
	"startColumn": 30,
	"endLineNumber": 777,
	"endColumn": 34
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 777,
	"startColumn": 53,
	"endLineNumber": 777,
	"endColumn": 57
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: pagemanager",
	"source": "compiler",
	"startLineNumber": 782,
	"startColumn": 17,
	"endLineNumber": 782,
	"endColumn": 28
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: pagemanager",
	"source": "compiler",
	"startLineNumber": 788,
	"startColumn": 18,
	"endLineNumber": 788,
	"endColumn": 29
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 832,
	"startColumn": 30,
	"endLineNumber": 832,
	"endColumn": 34
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 832,
	"startColumn": 53,
	"endLineNumber": 832,
	"endColumn": 57
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: pagemanager",
	"source": "compiler",
	"startLineNumber": 837,
	"startColumn": 25,
	"endLineNumber": 837,
	"endColumn": 36
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: pagemanager",
	"source": "compiler",
	"startLineNumber": 839,
	"startColumn": 18,
	"endLineNumber": 839,
	"endColumn": 29
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 902,
	"startColumn": 30,
	"endLineNumber": 902,
	"endColumn": 34
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 902,
	"startColumn": 53,
	"endLineNumber": 902,
	"endColumn": 57
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: pagemanager",
	"source": "compiler",
	"startLineNumber": 907,
	"startColumn": 25,
	"endLineNumber": 907,
	"endColumn": 36
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 922,
	"startColumn": 30,
	"endLineNumber": 922,
	"endColumn": 34
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 922,
	"startColumn": 53,
	"endLineNumber": 922,
	"endColumn": 57
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: pagemanager",
	"source": "compiler",
	"startLineNumber": 927,
	"startColumn": 17,
	"endLineNumber": 927,
	"endColumn": 28
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: pagemanager",
	"source": "compiler",
	"startLineNumber": 934,
	"startColumn": 80,
	"endLineNumber": 934,
	"endColumn": 91
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 960,
	"startColumn": 30,
	"endLineNumber": 960,
	"endColumn": 34
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 960,
	"startColumn": 53,
	"endLineNumber": 960,
	"endColumn": 57
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: sitemanager",
	"source": "compiler",
	"startLineNumber": 965,
	"startColumn": 17,
	"endLineNumber": 965,
	"endColumn": 28
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: sitemanager",
	"source": "compiler",
	"startLineNumber": 972,
	"startColumn": 18,
	"endLineNumber": 972,
	"endColumn": 29
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 1152,
	"startColumn": 30,
	"endLineNumber": 1152,
	"endColumn": 34
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 1152,
	"startColumn": 53,
	"endLineNumber": 1152,
	"endColumn": 57
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 1153,
	"startColumn": 13,
	"endLineNumber": 1153,
	"endColumn": 17
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: database",
	"source": "compiler",
	"startLineNumber": 1157,
	"startColumn": 19,
	"endLineNumber": 1157,
	"endColumn": 27
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 1186,
	"startColumn": 30,
	"endLineNumber": 1186,
	"endColumn": 34
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 1186,
	"startColumn": 53,
	"endLineNumber": 1186,
	"endColumn": 57
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 1187,
	"startColumn": 13,
	"endLineNumber": 1187,
	"endColumn": 17
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: database",
	"source": "compiler",
	"startLineNumber": 1191,
	"startColumn": 19,
	"endLineNumber": 1191,
	"endColumn": 27
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 1249,
	"startColumn": 30,
	"endLineNumber": 1249,
	"endColumn": 34
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 1249,
	"startColumn": 53,
	"endLineNumber": 1249,
	"endColumn": 57
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 1250,
	"startColumn": 13,
	"endLineNumber": 1250,
	"endColumn": 17
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 1255,
	"startColumn": 11,
	"endLineNumber": 1255,
	"endColumn": 15
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: database",
	"source": "compiler",
	"startLineNumber": 1259,
	"startColumn": 19,
	"endLineNumber": 1259,
	"endColumn": 27
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 1271,
	"startColumn": 30,
	"endLineNumber": 1271,
	"endColumn": 34
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 1271,
	"startColumn": 53,
	"endLineNumber": 1271,
	"endColumn": 57
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 1272,
	"startColumn": 13,
	"endLineNumber": 1272,
	"endColumn": 17
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: subscription",
	"source": "compiler",
	"startLineNumber": 1276,
	"startColumn": 25,
	"endLineNumber": 1276,
	"endColumn": 37
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 1320,
	"startColumn": 30,
	"endLineNumber": 1320,
	"endColumn": 34
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 1320,
	"startColumn": 53,
	"endLineNumber": 1320,
	"endColumn": 57
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 1361,
	"startColumn": 30,
	"endLineNumber": 1361,
	"endColumn": 34
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 1361,
	"startColumn": 53,
	"endLineNumber": 1361,
	"endColumn": 57
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: subscription",
	"source": "compiler",
	"startLineNumber": 1366,
	"startColumn": 25,
	"endLineNumber": 1366,
	"endColumn": 37
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 1382,
	"startColumn": 30,
	"endLineNumber": 1382,
	"endColumn": 34
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 1382,
	"startColumn": 53,
	"endLineNumber": 1382,
	"endColumn": 57
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: onboarding",
	"source": "compiler",
	"startLineNumber": 1387,
	"startColumn": 23,
	"endLineNumber": 1387,
	"endColumn": 33
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 1433,
	"startColumn": 30,
	"endLineNumber": 1433,
	"endColumn": 34
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 1433,
	"startColumn": 53,
	"endLineNumber": 1433,
	"endColumn": 57
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: onboarding",
	"source": "compiler",
	"startLineNumber": 1438,
	"startColumn": 23,
	"endLineNumber": 1438,
	"endColumn": 33
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: onboarding",
	"source": "compiler",
	"startLineNumber": 1440,
	"startColumn": 14,
	"endLineNumber": 1440,
	"endColumn": 24
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 1489,
	"startColumn": 30,
	"endLineNumber": 1489,
	"endColumn": 34
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 1489,
	"startColumn": 53,
	"endLineNumber": 1489,
	"endColumn": 57
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: onboarding",
	"source": "compiler",
	"startLineNumber": 1494,
	"startColumn": 23,
	"endLineNumber": 1494,
	"endColumn": 33
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 1640,
	"startColumn": 17,
	"endLineNumber": 1640,
	"endColumn": 21
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 1645,
	"startColumn": 6,
	"endLineNumber": 1645,
	"endColumn": 10
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 1695,
	"startColumn": 17,
	"endLineNumber": 1695,
	"endColumn": 21
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 1704,
	"startColumn": 17,
	"endLineNumber": 1704,
	"endColumn": 21
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 1756,
	"startColumn": 17,
	"endLineNumber": 1756,
	"endColumn": 21
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 1772,
	"startColumn": 30,
	"endLineNumber": 1772,
	"endColumn": 34
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 1772,
	"startColumn": 53,
	"endLineNumber": 1772,
	"endColumn": 57
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 1773,
	"startColumn": 13,
	"endLineNumber": 1773,
	"endColumn": 17
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: database",
	"source": "compiler",
	"startLineNumber": 1777,
	"startColumn": 19,
	"endLineNumber": 1777,
	"endColumn": 27
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 1822,
	"startColumn": 30,
	"endLineNumber": 1822,
	"endColumn": 34
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 1822,
	"startColumn": 53,
	"endLineNumber": 1822,
	"endColumn": 57
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 1827,
	"startColumn": 6,
	"endLineNumber": 1827,
	"endColumn": 10
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: database",
	"source": "compiler",
	"startLineNumber": 1831,
	"startColumn": 19,
	"endLineNumber": 1831,
	"endColumn": 27
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 1855,
	"startColumn": 30,
	"endLineNumber": 1855,
	"endColumn": 34
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 1855,
	"startColumn": 53,
	"endLineNumber": 1855,
	"endColumn": 57
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 1860,
	"startColumn": 6,
	"endLineNumber": 1860,
	"endColumn": 10
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: database",
	"source": "compiler",
	"startLineNumber": 1864,
	"startColumn": 19,
	"endLineNumber": 1864,
	"endColumn": 27
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 1881,
	"startColumn": 30,
	"endLineNumber": 1881,
	"endColumn": 34
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 1881,
	"startColumn": 53,
	"endLineNumber": 1881,
	"endColumn": 57
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 1886,
	"startColumn": 6,
	"endLineNumber": 1886,
	"endColumn": 10
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: database",
	"source": "compiler",
	"startLineNumber": 1890,
	"startColumn": 19,
	"endLineNumber": 1890,
	"endColumn": 27
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: subscription",
	"source": "compiler",
	"startLineNumber": 1904,
	"startColumn": 25,
	"endLineNumber": 1904,
	"endColumn": 37
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 1934,
	"startColumn": 30,
	"endLineNumber": 1934,
	"endColumn": 34
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 1934,
	"startColumn": 53,
	"endLineNumber": 1934,
	"endColumn": 57
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: subscription",
	"source": "compiler",
	"startLineNumber": 1939,
	"startColumn": 25,
	"endLineNumber": 1939,
	"endColumn": 37
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 1971,
	"startColumn": 30,
	"endLineNumber": 1971,
	"endColumn": 34
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 1971,
	"startColumn": 53,
	"endLineNumber": 1971,
	"endColumn": 57
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: subscription",
	"source": "compiler",
	"startLineNumber": 1976,
	"startColumn": 25,
	"endLineNumber": 1976,
	"endColumn": 37
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 2002,
	"startColumn": 30,
	"endLineNumber": 2002,
	"endColumn": 34
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 2002,
	"startColumn": 53,
	"endLineNumber": 2002,
	"endColumn": 57
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: onboarding",
	"source": "compiler",
	"startLineNumber": 2007,
	"startColumn": 23,
	"endLineNumber": 2007,
	"endColumn": 33
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 2041,
	"startColumn": 30,
	"endLineNumber": 2041,
	"endColumn": 34
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 2041,
	"startColumn": 53,
	"endLineNumber": 2041,
	"endColumn": 57
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 2072,
	"startColumn": 30,
	"endLineNumber": 2072,
	"endColumn": 34
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 2072,
	"startColumn": 53,
	"endLineNumber": 2072,
	"endColumn": 57
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: onboarding",
	"source": "compiler",
	"startLineNumber": 2077,
	"startColumn": 23,
	"endLineNumber": 2077,
	"endColumn": 33
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 2102,
	"startColumn": 30,
	"endLineNumber": 2102,
	"endColumn": 34
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 2102,
	"startColumn": 53,
	"endLineNumber": 2102,
	"endColumn": 57
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: dashboard",
	"source": "compiler",
	"startLineNumber": 2107,
	"startColumn": 22,
	"endLineNumber": 2107,
	"endColumn": 31
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 2139,
	"startColumn": 30,
	"endLineNumber": 2139,
	"endColumn": 34
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 2139,
	"startColumn": 53,
	"endLineNumber": 2139,
	"endColumn": 57
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: sitemanager",
	"source": "compiler",
	"startLineNumber": 2144,
	"startColumn": 17,
	"endLineNumber": 2144,
	"endColumn": 28
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 2180,
	"startColumn": 30,
	"endLineNumber": 2180,
	"endColumn": 34
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 2180,
	"startColumn": 53,
	"endLineNumber": 2180,
	"endColumn": 57
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: sitemanager",
	"source": "compiler",
	"startLineNumber": 2190,
	"startColumn": 17,
	"endLineNumber": 2190,
	"endColumn": 28
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 2259,
	"startColumn": 30,
	"endLineNumber": 2259,
	"endColumn": 34
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 2259,
	"startColumn": 53,
	"endLineNumber": 2259,
	"endColumn": 57
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: sitemanager",
	"source": "compiler",
	"startLineNumber": 2269,
	"startColumn": 17,
	"endLineNumber": 2269,
	"endColumn": 28
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 2312,
	"startColumn": 30,
	"endLineNumber": 2312,
	"endColumn": 34
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 2312,
	"startColumn": 53,
	"endLineNumber": 2312,
	"endColumn": 57
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: pagemanager",
	"source": "compiler",
	"startLineNumber": 2317,
	"startColumn": 25,
	"endLineNumber": 2317,
	"endColumn": 36
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 2355,
	"startColumn": 30,
	"endLineNumber": 2355,
	"endColumn": 34
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 2355,
	"startColumn": 53,
	"endLineNumber": 2355,
	"endColumn": 57
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: pagemanager",
	"source": "compiler",
	"startLineNumber": 2365,
	"startColumn": 25,
	"endLineNumber": 2365,
	"endColumn": 36
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 2412,
	"startColumn": 30,
	"endLineNumber": 2412,
	"endColumn": 34
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: auth",
	"source": "compiler",
	"startLineNumber": 2412,
	"startColumn": 53,
	"endLineNumber": 2412,
	"endColumn": 57
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: sitemanager",
	"source": "compiler",
	"startLineNumber": 2417,
	"startColumn": 17,
	"endLineNumber": 2417,
	"endColumn": 28
}]

gqlgen generate output 

$ go run github.com/99designs/gqlgen generate
validation failed: packages.Load: -: # goVwPlatformAPI/graph/generated
graph\generated\generated.go:123074:17: ec.unmarshalInputJSON undefined (type *executionContext has no field or method unmarshalInputJSON)
graph\generated\generated.go:123085:12: ec._JSON undefined (type *executionContext has no field or method _JSON)
graph\generated\generated.go:124995:17: ec.unmarshalInputTime undefined (type *executionContext has no field or method unmarshalInputTime)
graph\generated\generated.go:125000:12: ec._Time undefined (type *executionContext has no field or method _Time)
graph\generated\generated.go:126237:17: ec.unmarshalInputJSON undefined (type *executionContext has no field or method unmarshalInputJSON)
graph\generated\generated.go:126245:12: ec._JSON undefined (type *executionContext has no field or method _JSON)
graph\generated\generated.go:126605:17: ec.unmarshalInputTime undefined (type *executionContext has no field or method unmarshalInputTime)
graph\generated\generated.go:126613:12: ec._Time undefined (type *executionContext has no field or method _Time)
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\generated\generated.go:123074:17: ec.unmarshalInputJSON undefined (type *executionContext has no field or method unmarshalInputJSON)
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\generated\generated.go:123085:12: ec._JSON undefined (type *executionContext has no field or method _JSON)        
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\generated\generated.go:124995:17: ec.unmarshalInputTime undefined (type *executionContext has no field or method unmarshalInputTime)
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\generated\generated.go:125000:12: ec._Time undefined (type *executionContext has no field or method _Time)        
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\generated\generated.go:126237:17: ec.unmarshalInputJSON undefined (type *executionContext has no field or method unmarshalInputJSON)
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\generated\generated.go:126245:12: ec._JSON undefined (type *executionContext has no field or method _JSON)        
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\generated\generated.go:126605:17: ec.unmarshalInputTime undefined (type *executionContext has no field or method unmarshalInputTime)
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\generated\generated.go:126613:12: ec._Time undefined (type *executionContext has no field or method _Time)        
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:33:6: undefined: crypto
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:37:11: undefined: jwt
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:37:29: undefined: jwt
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:37:53: undefined: jwt
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:39:14: undefined: jwt
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:42:48: undefined: os
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:55:17: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:60:6: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:84:17: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:89:6: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:98:17: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:103:6: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:122:17: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:127:6: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:146:17: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:151:6: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:170:17: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:175:6: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:194:17: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:199:6: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:238:23: undefined: crypto
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:248:11: undefined: jwt
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:248:29: undefined: jwt
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:248:53: undefined: jwt
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:250:14: undefined: jwt
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:253:48: undefined: os
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:266:17: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:275:17: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:294:17: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:309:17: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:319:17: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:330:17: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:350:17: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:368:17: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:386:17: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:391:6: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:400:17: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:405:6: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:414:17: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:419:6: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:428:17: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:433:6: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:447:17: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:452:6: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:615:30: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:615:53: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:620:17: undefined: sitemanager
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:622:18: undefined: sitemanager
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:674:30: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:674:53: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:684:17: undefined: sitemanager
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:686:18: undefined: sitemanager
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:742:30: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:742:53: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:752:17: undefined: sitemanager
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:777:30: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:777:53: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:782:17: undefined: pagemanager
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:788:18: undefined: pagemanager
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:832:30: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:832:53: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:837:25: undefined: pagemanager
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:839:18: undefined: pagemanager
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:902:30: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:902:53: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:907:25: undefined: pagemanager
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:922:30: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:922:53: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:927:17: undefined: pagemanager
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:934:80: undefined: pagemanager
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:960:30: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:960:53: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:965:17: undefined: sitemanager
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:972:18: undefined: sitemanager
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1152:30: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1152:53: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1153:13: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1157:19: undefined: database
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1186:30: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1186:53: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1187:13: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1191:19: undefined: database
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1249:30: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1249:53: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1250:13: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1255:11: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1259:19: undefined: database
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1271:30: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1271:53: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1272:13: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1276:25: undefined: subscription
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1320:30: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1320:53: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1361:30: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1361:53: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1366:25: undefined: subscription
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1382:30: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1382:53: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1387:23: undefined: onboarding
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1433:30: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1433:53: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1438:23: undefined: onboarding
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1440:14: undefined: onboarding
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1489:30: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1489:53: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1494:23: undefined: onboarding
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1640:17: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1645:6: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1695:17: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1704:17: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1756:17: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1772:30: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1772:53: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1773:13: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1777:19: undefined: database
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1822:30: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1822:53: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1827:6: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1831:19: undefined: database
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1855:30: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1855:53: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1860:6: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1864:19: undefined: database
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1881:30: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1881:53: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1886:6: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1890:19: undefined: database
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1904:25: undefined: subscription
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1934:30: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1934:53: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1939:25: undefined: subscription
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1971:30: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1971:53: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:1976:25: undefined: subscription
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:2002:30: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:2002:53: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:2007:23: undefined: onboarding
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:2041:30: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:2041:53: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:2072:30: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:2072:53: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:2077:23: undefined: onboarding
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:2102:30: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:2102:53: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:2107:22: undefined: dashboard
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:2139:30: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:2139:53: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:2144:17: undefined: sitemanager
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:2180:30: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:2180:53: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:2190:17: undefined: sitemanager
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:2259:30: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:2259:53: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:2269:17: undefined: sitemanager
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:2312:30: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:2312:53: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:2317:25: undefined: pagemanager
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:2355:30: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:2365:25: undefined: pagemanager
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:2412:30: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:2412:53: undefined: auth
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\schema.resolvers.go:2417:17: undefined: sitemanager

exit status 1