[{"resource": "/c:/Users/<USER>/Desktop/goVwPlatformAPI/cmd/api/main.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "BrokenImport", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "BrokenImport"}}, "severity": 8, "message": "could not import goVwPlatformAPI/graph/generated (no required module provides package \"goVwPlatformAPI/graph/generated\")", "source": "compiler", "startLineNumber": 13, "startColumn": 2, "endLineNumber": 13, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "severity": 8, "message": "missing import path", "source": "go list", "startLineNumber": 8, "startColumn": 16, "endLineNumber": 8, "endColumn": 16}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "severity": 8, "message": "missing import path", "source": "syntax", "startLineNumber": 8, "startColumn": 16, "endLineNumber": 8, "endColumn": 16}]