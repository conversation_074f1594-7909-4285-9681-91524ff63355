errors before gqlgen generate
[{
	"resource": "/c:/Users/<USER>/Desktop/goVwPlatformAPI/cmd/api/main.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "CannotInferTypeArgs",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "CannotInferTypeArgs"
		}
	},
	"severity": 8,
	"message": "in call to lru.New, cannot infer T (declared at C:\\Users\\<USER>\\go\\pkg\\mod\\github.com\\99designs\\gqlgen@v0.17.76\\graphql\\handler\\lru\\lru.go:17:10)",
	"source": "compiler",
	"startLineNumber": 1297,
	"startColumn": 10,
	"endLineNumber": 1297,
	"endColumn": 22
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "ec.unmarshalInputJSON undefined (type *executionContext has no field or method unmarshalInputJSON)",
	"source": "compiler",
	"startLineNumber": 123074,
	"startColumn": 17,
	"endLineNumber": 123074,
	"endColumn": 35
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "ec._JSON undefined (type *executionContext has no field or method _JSON)",
	"source": "compiler",
	"startLineNumber": 123085,
	"startColumn": 12,
	"endLineNumber": 123085,
	"endColumn": 17
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "ec.unmarshalInputTime undefined (type *executionContext has no field or method unmarshalInputTime)",
	"source": "compiler",
	"startLineNumber": 124995,
	"startColumn": 17,
	"endLineNumber": 124995,
	"endColumn": 35
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "ec._Time undefined (type *executionContext has no field or method _Time)",
	"source": "compiler",
	"startLineNumber": 125000,
	"startColumn": 12,
	"endLineNumber": 125000,
	"endColumn": 17
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "ec.unmarshalInputJSON undefined (type *executionContext has no field or method unmarshalInputJSON)",
	"source": "compiler",
	"startLineNumber": 126237,
	"startColumn": 17,
	"endLineNumber": 126237,
	"endColumn": 35
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "ec._JSON undefined (type *executionContext has no field or method _JSON)",
	"source": "compiler",
	"startLineNumber": 126245,
	"startColumn": 12,
	"endLineNumber": 126245,
	"endColumn": 17
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "ec.unmarshalInputTime undefined (type *executionContext has no field or method unmarshalInputTime)",
	"source": "compiler",
	"startLineNumber": 126605,
	"startColumn": 17,
	"endLineNumber": 126605,
	"endColumn": 35
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "ec._Time undefined (type *executionContext has no field or method _Time)",
	"source": "compiler",
	"startLineNumber": 126613,
	"startColumn": 12,
	"endLineNumber": 126613,
	"endColumn": 17
}]


errors 