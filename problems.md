[{"resource": "/c:/Users/<USER>/Desktop/goVwPlatformAPI/cmd/api/main.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "BrokenImport", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "BrokenImport"}}, "severity": 8, "message": "could not import goVwPlatformAPI/graph/generated (no required module provides package \"goVwPlatformAPI/graph/generated\")", "source": "compiler", "startLineNumber": 13, "startColumn": 2, "endLineNumber": 13, "endColumn": 35}, {"resource": "/c:/Users/<USER>/Desktop/goVwPlatformAPI/cmd/api/main.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field DB in struct literal of type graph.Resolver", "source": "compiler", "startLineNumber": 1278, "startColumn": 3, "endLineNumber": 1278, "endColumn": 5}, {"resource": "/c:/Users/<USER>/Desktop/goVwPlatformAPI/cmd/api/main.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field SubscriptionService in struct literal of type graph.Resolver", "source": "compiler", "startLineNumber": 1279, "startColumn": 3, "endLineNumber": 1279, "endColumn": 22}, {"resource": "/c:/Users/<USER>/Desktop/goVwPlatformAPI/cmd/api/main.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field PageManager in struct literal of type graph.Resolver", "source": "compiler", "startLineNumber": 1280, "startColumn": 3, "endLineNumber": 1280, "endColumn": 14}, {"resource": "/c:/Users/<USER>/Desktop/goVwPlatformAPI/cmd/api/main.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field OnboardingService in struct literal of type graph.Resolver", "source": "compiler", "startLineNumber": 1281, "startColumn": 3, "endLineNumber": 1281, "endColumn": 20}, {"resource": "/c:/Users/<USER>/Desktop/goVwPlatformAPI/cmd/api/main.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field DashboardService in struct literal of type graph.Resolver", "source": "compiler", "startLineNumber": 1282, "startColumn": 3, "endLineNumber": 1282, "endColumn": 19}, {"resource": "/c:/Users/<USER>/Desktop/goVwPlatformAPI/cmd/api/main.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "CannotInferTypeArgs", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "CannotInferTypeArgs"}}, "severity": 8, "message": "in call to lru.<PERSON>, cannot infer T (declared at C:\\Users\\<USER>\\go\\pkg\\mod\\github.com\\99designs\\gqlgen@v0.17.76\\graphql\\handler\\lru\\lru.go:17:10)", "source": "compiler", "startLineNumber": 1297, "startColumn": 10, "endLineNumber": 1297, "endColumn": 22}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "BrokenImport", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "BrokenImport"}}, "severity": 8, "message": "could not import context (missing metadata for import of \"context\")", "source": "compiler", "startLineNumber": 6, "startColumn": 2, "endLineNumber": 6, "endColumn": 11}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "severity": 8, "message": "package goVwPlatformAPI/graph/generated is not in std (C:\\Program Files\\Go\\src\\goVwPlatformAPI\\graph\\generated)", "source": "go list", "startLineNumber": 7, "startColumn": 2, "endLineNumber": 7, "endColumn": 2}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "BrokenImport", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "BrokenImport"}}, "severity": 8, "message": "could not import goVwPlatformAPI/graph/generated (missing metadata for import of \"goVwPlatformAPI/graph/generated\")", "source": "compiler", "startLineNumber": 7, "startColumn": 2, "endLineNumber": 7, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "BrokenImport", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "BrokenImport"}}, "severity": 8, "message": "could not import goVwPlatformAPI/graph/generated (no required module provides package \"goVwPlatformAPI/graph/generated\")", "source": "compiler", "startLineNumber": 7, "startColumn": 2, "endLineNumber": 7, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "BrokenImport", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "BrokenImport"}}, "severity": 8, "message": "could not import goVwPlatformAPI/graph/model (missing metadata for import of \"goVwPlatformAPI/graph/model\")", "source": "compiler", "startLineNumber": 8, "startColumn": 2, "endLineNumber": 8, "endColumn": 31}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "BrokenImport", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "BrokenImport"}}, "severity": 8, "message": "could not import time (missing metadata for import of \"time\")", "source": "compiler", "startLineNumber": 9, "startColumn": 2, "endLineNumber": 9, "endColumn": 8}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "BrokenImport", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "BrokenImport"}}, "severity": 8, "message": "could not import github.com/99designs/gqlgen/graphql (missing metadata for import of \"github.com/99designs/gqlgen/graphql\")", "source": "compiler", "startLineNumber": 11, "startColumn": 2, "endLineNumber": 11, "endColumn": 39}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/go.mod", "owner": "_generated_diagnostic_collection_name_#2", "severity": 4, "message": "github.com/vektah/gqlparser/v2 should be indirect", "source": "go mod tidy", "startLineNumber": 15, "startColumn": 2, "endLineNumber": 15, "endColumn": 40}]