[{"resource": "/c:/Users/<USER>/Desktop/goVwPlatformAPI/cmd/api/main.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "BrokenImport", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "BrokenImport"}}, "severity": 8, "message": "could not import goVwPlatformAPI/graph/generated (no required module provides package \"goVwPlatformAPI/graph/generated\")", "source": "compiler", "startLineNumber": 13, "startColumn": 2, "endLineNumber": 13, "endColumn": 35}, {"resource": "/c:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver_minimal.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateDecl", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateDecl"}}, "severity": 8, "message": "Resolver redeclared in this block", "source": "compiler", "startLineNumber": 11, "startColumn": 6, "endLineNumber": 11, "endColumn": 14, "relatedInformation": [{"startLineNumber": 27, "startColumn": 6, "endLineNumber": 27, "endColumn": 14, "message": "other declaration of Resolver", "resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go"}]}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "severity": 8, "message": "missing import path", "source": "go list", "startLineNumber": 8, "startColumn": 16, "endLineNumber": 8, "endColumn": 16}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "severity": 8, "message": "missing import path", "source": "syntax", "startLineNumber": 8, "startColumn": 16, "endLineNumber": 8, "endColumn": 16}, {"resource": "/c:/Users/<USER>/Desktop/goVwPlatformAPI/run_migrations.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateDecl", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateDecl"}}, "severity": 8, "message": "main redeclared in this block (see details)", "source": "compiler", "startLineNumber": 10, "startColumn": 6, "endLineNumber": 10, "endColumn": 10, "relatedInformation": [{"startLineNumber": 17, "startColumn": 6, "endLineNumber": 17, "endColumn": 10, "message": "", "resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/test_resolver.go"}]}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/test_resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateDecl", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateDecl"}}, "severity": 8, "message": "main redeclared in this block", "source": "compiler", "startLineNumber": 17, "startColumn": 6, "endLineNumber": 17, "endColumn": 10, "relatedInformation": [{"startLineNumber": 10, "startColumn": 6, "endLineNumber": 10, "endColumn": 10, "message": "other declaration of main", "resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/run_migrations.go"}]}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/go.mod", "owner": "_generated_diagnostic_collection_name_#2", "severity": 4, "message": "github.com/vektah/gqlparser/v2 should be indirect", "source": "go mod tidy", "startLineNumber": 15, "startColumn": 2, "endLineNumber": 15, "endColumn": 40}]