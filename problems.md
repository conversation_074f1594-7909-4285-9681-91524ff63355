[{"resource": "/c:/Users/<USER>/Desktop/goVwPlatformAPI/cmd/api/main.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field DB in struct literal of type graph.Resolver", "source": "compiler", "startLineNumber": 1278, "startColumn": 3, "endLineNumber": 1278, "endColumn": 5}, {"resource": "/c:/Users/<USER>/Desktop/goVwPlatformAPI/cmd/api/main.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field SubscriptionService in struct literal of type graph.Resolver", "source": "compiler", "startLineNumber": 1279, "startColumn": 3, "endLineNumber": 1279, "endColumn": 22}, {"resource": "/c:/Users/<USER>/Desktop/goVwPlatformAPI/cmd/api/main.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field PageManager in struct literal of type graph.Resolver", "source": "compiler", "startLineNumber": 1280, "startColumn": 3, "endLineNumber": 1280, "endColumn": 14}, {"resource": "/c:/Users/<USER>/Desktop/goVwPlatformAPI/cmd/api/main.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field OnboardingService in struct literal of type graph.Resolver", "source": "compiler", "startLineNumber": 1281, "startColumn": 3, "endLineNumber": 1281, "endColumn": 20}, {"resource": "/c:/Users/<USER>/Desktop/goVwPlatformAPI/cmd/api/main.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field DashboardService in struct literal of type graph.Resolver", "source": "compiler", "startLineNumber": 1282, "startColumn": 3, "endLineNumber": 1282, "endColumn": 19}, {"resource": "/c:/Users/<USER>/Desktop/goVwPlatformAPI/cmd/api/main.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "InvalidIfaceAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "InvalidIfaceAssign"}}, "severity": 8, "message": "cannot use resolver (variable of type *graph.Resolver) as generated.ResolverRoot value in struct literal: *graph.Resolver does not implement generated.ResolverRoot (unexported method __InputValue)", "source": "compiler", "startLineNumber": 1286, "startColumn": 79, "endLineNumber": 1286, "endColumn": 87}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "InvalidIfaceAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "InvalidIfaceAssign"}}, "severity": 8, "message": "cannot use &executableSchema{…} (value of type *executableSchema) as graphql.ExecutableSchema value in return statement: *executableSchema does not implement graphql.ExecutableSchema (wrong type for method Complexity)\n\t\thave Complexity(context.Context, string, string, int, map[string]any) (int, bool)\n\t\twant Complexity(string, string, int, map[string]interface{}) (int, bool)", "source": "compiler", "startLineNumber": 26, "startColumn": 9, "endLineNumber": 31, "endColumn": 3}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec.unmarshalInputJSON undefined (type *executionContext has no field or method unmarshalInputJSON)", "source": "compiler", "startLineNumber": 123176, "startColumn": 17, "endLineNumber": 123176, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec._JSON undefined (type *executionContext has no field or method _JSON)", "source": "compiler", "startLineNumber": 123187, "startColumn": 12, "endLineNumber": 123187, "endColumn": 17}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec.unmarshalInputTime undefined (type *executionContext has no field or method unmarshalInputTime)", "source": "compiler", "startLineNumber": 125097, "startColumn": 17, "endLineNumber": 125097, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec._Time undefined (type *executionContext has no field or method _Time)", "source": "compiler", "startLineNumber": 125102, "startColumn": 12, "endLineNumber": 125102, "endColumn": 17}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec.unmarshalInputJSON undefined (type *executionContext has no field or method unmarshalInputJSON)", "source": "compiler", "startLineNumber": 126339, "startColumn": 17, "endLineNumber": 126339, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec._JSON undefined (type *executionContext has no field or method _JSON)", "source": "compiler", "startLineNumber": 126347, "startColumn": 12, "endLineNumber": 126347, "endColumn": 17}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec.unmarshalInputTime undefined (type *executionContext has no field or method unmarshalInputTime)", "source": "compiler", "startLineNumber": 126707, "startColumn": 17, "endLineNumber": 126707, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec._Time undefined (type *executionContext has no field or method _Time)", "source": "compiler", "startLineNumber": 126715, "startColumn": 12, "endLineNumber": 126715, "endColumn": 17}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/prelude.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UnexportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UnexportedName"}}, "severity": 8, "message": "name __InputValueResolver not exported by package generated", "source": "compiler", "startLineNumber": 30, "startColumn": 45, "endLineNumber": 30, "endColumn": 65}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/prelude.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UnexportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UnexportedName"}}, "severity": 8, "message": "name __TypeResolver not exported by package generated", "source": "compiler", "startLineNumber": 33, "startColumn": 39, "endLineNumber": 33, "endColumn": 53}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/prelude.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateDecl", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateDecl"}}, "severity": 8, "message": "__InputValueResolver redeclared in this block (see details)", "source": "compiler", "startLineNumber": 35, "startColumn": 6, "endLineNumber": 35, "endColumn": 26, "relatedInformation": [{"startLineNumber": 2720, "startColumn": 6, "endLineNumber": 2720, "endColumn": 26, "message": "", "resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go"}]}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/prelude.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateDecl", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateDecl"}}, "severity": 8, "message": "__TypeResolver redeclared in this block (see details)", "source": "compiler", "startLineNumber": 36, "startColumn": 6, "endLineNumber": 36, "endColumn": 20, "relatedInformation": [{"startLineNumber": 2721, "startColumn": 6, "endLineNumber": 2721, "endColumn": 20, "message": "", "resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go"}]}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 31, "startColumn": 17, "endLineNumber": 31, "endColumn": 19}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 39, "startColumn": 10, "endLineNumber": 39, "endColumn": 12}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 75, "startColumn": 19, "endLineNumber": 75, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 104, "startColumn": 11, "endLineNumber": 104, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 118, "startColumn": 13, "endLineNumber": 118, "endColumn": 15}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 128, "startColumn": 11, "endLineNumber": 128, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 142, "startColumn": 13, "endLineNumber": 142, "endColumn": 15}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 152, "startColumn": 11, "endLineNumber": 152, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 166, "startColumn": 13, "endLineNumber": 166, "endColumn": 15}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 176, "startColumn": 11, "endLineNumber": 176, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 190, "startColumn": 13, "endLineNumber": 190, "endColumn": 15}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 200, "startColumn": 11, "endLineNumber": 200, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 214, "startColumn": 13, "endLineNumber": 214, "endColumn": 15}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 224, "startColumn": 11, "endLineNumber": 224, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 254, "startColumn": 17, "endLineNumber": 254, "endColumn": 19}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 281, "startColumn": 11, "endLineNumber": 281, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 291, "startColumn": 20, "endLineNumber": 291, "endColumn": 22}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 310, "startColumn": 13, "endLineNumber": 310, "endColumn": 15}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 325, "startColumn": 11, "endLineNumber": 325, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 335, "startColumn": 10, "endLineNumber": 335, "endColumn": 12}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 346, "startColumn": 11, "endLineNumber": 346, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 374, "startColumn": 11, "endLineNumber": 374, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 392, "startColumn": 11, "endLineNumber": 392, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 406, "startColumn": 11, "endLineNumber": 406, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 420, "startColumn": 11, "endLineNumber": 420, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 434, "startColumn": 11, "endLineNumber": 434, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 448, "startColumn": 10, "endLineNumber": 448, "endColumn": 12}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 468, "startColumn": 13, "endLineNumber": 468, "endColumn": 15}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 631, "startColumn": 46, "endLineNumber": 631, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use int32(site.PageCount) (value of type int32) as int value in struct literal", "source": "compiler", "startLineNumber": 677, "startColumn": 17, "endLineNumber": 677, "endColumn": 38}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 695, "startColumn": 46, "endLineNumber": 695, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 763, "startColumn": 46, "endLineNumber": 763, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 793, "startColumn": 54, "endLineNumber": 793, "endColumn": 56}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 848, "startColumn": 62, "endLineNumber": 848, "endColumn": 64}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 918, "startColumn": 62, "endLineNumber": 918, "endColumn": 64}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 938, "startColumn": 54, "endLineNumber": 938, "endColumn": 56}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 976, "startColumn": 46, "endLineNumber": 976, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1168, "startColumn": 47, "endLineNumber": 1168, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1202, "startColumn": 47, "endLineNumber": 1202, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1270, "startColumn": 47, "endLineNumber": 1270, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1287, "startColumn": 63, "endLineNumber": 1287, "endColumn": 65}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.SubscriptionService undefined (type *mutationResolver has no field or method SubscriptionService)", "source": "compiler", "startLineNumber": 1337, "startColumn": 11, "endLineNumber": 1337, "endColumn": 30}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.SubscriptionService undefined (type *mutationResolver has no field or method SubscriptionService)", "source": "compiler", "startLineNumber": 1343, "startColumn": 25, "endLineNumber": 1343, "endColumn": 44}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1377, "startColumn": 63, "endLineNumber": 1377, "endColumn": 65}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1398, "startColumn": 57, "endLineNumber": 1398, "endColumn": 59}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1449, "startColumn": 57, "endLineNumber": 1449, "endColumn": 59}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1505, "startColumn": 57, "endLineNumber": 1505, "endColumn": 59}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1660, "startColumn": 20, "endLineNumber": 1660, "endColumn": 22}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1710, "startColumn": 11, "endLineNumber": 1710, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1719, "startColumn": 11, "endLineNumber": 1719, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1724, "startColumn": 11, "endLineNumber": 1724, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1729, "startColumn": 11, "endLineNumber": 1729, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1736, "startColumn": 20, "endLineNumber": 1736, "endColumn": 22}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1750, "startColumn": 22, "endLineNumber": 1750, "endColumn": 24}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1772, "startColumn": 18, "endLineNumber": 1772, "endColumn": 20}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1788, "startColumn": 47, "endLineNumber": 1788, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1842, "startColumn": 47, "endLineNumber": 1842, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1875, "startColumn": 47, "endLineNumber": 1875, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1901, "startColumn": 47, "endLineNumber": 1901, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1915, "startColumn": 63, "endLineNumber": 1915, "endColumn": 65}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1950, "startColumn": 63, "endLineNumber": 1950, "endColumn": 65}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1987, "startColumn": 63, "endLineNumber": 1987, "endColumn": 65}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2018, "startColumn": 57, "endLineNumber": 2018, "endColumn": 59}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2068, "startColumn": 11, "endLineNumber": 2068, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2088, "startColumn": 57, "endLineNumber": 2088, "endColumn": 59}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2118, "startColumn": 54, "endLineNumber": 2118, "endColumn": 56}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2155, "startColumn": 46, "endLineNumber": 2155, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2201, "startColumn": 46, "endLineNumber": 2201, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2280, "startColumn": 46, "endLineNumber": 2280, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2328, "startColumn": 62, "endLineNumber": 2328, "endColumn": 64}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2376, "startColumn": 62, "endLineNumber": 2376, "endColumn": 64}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2428, "startColumn": 46, "endLineNumber": 2428, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method __InputValueResolver.IsDeprecated already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\prelude.resolvers.go:15:32", "source": "compiler", "startLineNumber": 2692, "startColumn": 32, "endLineNumber": 2692, "endColumn": 44}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method __InputValueResolver.DeprecationReason already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\prelude.resolvers.go:20:32", "source": "compiler", "startLineNumber": 2697, "startColumn": 32, "endLineNumber": 2697, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method __TypeResolver.IsOneOf already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\prelude.resolvers.go:25:26", "source": "compiler", "startLineNumber": 2702, "startColumn": 26, "endLineNumber": 2702, "endColumn": 33}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method Resolver.__InputValue already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\prelude.resolvers.go:30:20", "source": "compiler", "startLineNumber": 2713, "startColumn": 20, "endLineNumber": 2713, "endColumn": 32}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UnexportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UnexportedName"}}, "severity": 8, "message": "name __InputValueResolver not exported by package generated", "source": "compiler", "startLineNumber": 2713, "startColumn": 45, "endLineNumber": 2713, "endColumn": 65}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method Resolver.__Type already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\prelude.resolvers.go:33:20", "source": "compiler", "startLineNumber": 2716, "startColumn": 20, "endLineNumber": 2716, "endColumn": 26}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UnexportedName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UnexportedName"}}, "severity": 8, "message": "name __TypeResolver not exported by package generated", "source": "compiler", "startLineNumber": 2716, "startColumn": 39, "endLineNumber": 2716, "endColumn": 53}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateDecl", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateDecl"}}, "severity": 8, "message": "mutationResolver redeclared in this block (see details)", "source": "compiler", "startLineNumber": 2718, "startColumn": 6, "endLineNumber": 2718, "endColumn": 22, "relatedInformation": [{"startLineNumber": 2686, "startColumn": 6, "endLineNumber": 2686, "endColumn": 22, "message": "", "resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go"}]}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateDecl", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateDecl"}}, "severity": 8, "message": "queryResolver redeclared in this block (see details)", "source": "compiler", "startLineNumber": 2719, "startColumn": 6, "endLineNumber": 2719, "endColumn": 19, "relatedInformation": [{"startLineNumber": 2687, "startColumn": 6, "endLineNumber": 2687, "endColumn": 19, "message": "", "resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go"}]}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateDecl", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateDecl"}}, "severity": 8, "message": "__InputValueResolver redeclared in this block", "source": "compiler", "startLineNumber": 2720, "startColumn": 6, "endLineNumber": 2720, "endColumn": 26, "relatedInformation": [{"startLineNumber": 35, "startColumn": 6, "endLineNumber": 35, "endColumn": 26, "message": "other declaration of __InputValueResolver", "resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/prelude.resolvers.go"}]}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateDecl", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateDecl"}}, "severity": 8, "message": "__TypeResolver redeclared in this block", "source": "compiler", "startLineNumber": 2721, "startColumn": 6, "endLineNumber": 2721, "endColumn": 20, "relatedInformation": [{"startLineNumber": 36, "startColumn": 6, "endLineNumber": 36, "endColumn": 20, "message": "other declaration of __TypeResolver", "resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/prelude.resolvers.go"}]}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.<PERSON><PERSON> already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:30:28", "source": "compiler", "startLineNumber": 19, "startColumn": 28, "endLineNumber": 19, "endColumn": 33}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 20, "startColumn": 17, "endLineNumber": 20, "endColumn": 19}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 28, "startColumn": 10, "endLineNumber": 28, "endColumn": 12}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: crypto", "source": "compiler", "startLineNumber": 33, "startColumn": 6, "endLineNumber": 33, "endColumn": 12}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: jwt", "source": "compiler", "startLineNumber": 37, "startColumn": 11, "endLineNumber": 37, "endColumn": 14}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: jwt", "source": "compiler", "startLineNumber": 37, "startColumn": 29, "endLineNumber": 37, "endColumn": 32}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: jwt", "source": "compiler", "startLineNumber": 37, "startColumn": 53, "endLineNumber": 37, "endColumn": 56}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: jwt", "source": "compiler", "startLineNumber": 39, "startColumn": 14, "endLineNumber": 39, "endColumn": 17}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: os", "source": "compiler", "startLineNumber": 42, "startColumn": 48, "endLineNumber": 42, "endColumn": 50}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateAddonStatus already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:65:28", "source": "compiler", "startLineNumber": 54, "startColumn": 28, "endLineNumber": 54, "endColumn": 45}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 55, "startColumn": 17, "endLineNumber": 55, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 60, "startColumn": 6, "endLineNumber": 60, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 64, "startColumn": 19, "endLineNumber": 64, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.SaveAddonConfig already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:94:28", "source": "compiler", "startLineNumber": 83, "startColumn": 28, "endLineNumber": 83, "endColumn": 43}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 84, "startColumn": 17, "endLineNumber": 84, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 89, "startColumn": 6, "endLineNumber": 89, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 93, "startColumn": 11, "endLineNumber": 93, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.SubmitToReview already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:108:28", "source": "compiler", "startLineNumber": 97, "startColumn": 28, "endLineNumber": 97, "endColumn": 42}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 98, "startColumn": 17, "endLineNumber": 98, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 103, "startColumn": 6, "endLineNumber": 103, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 107, "startColumn": 13, "endLineNumber": 107, "endColumn": 15}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 117, "startColumn": 11, "endLineNumber": 117, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.ApproveAddon already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:132:28", "source": "compiler", "startLineNumber": 121, "startColumn": 28, "endLineNumber": 121, "endColumn": 40}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 122, "startColumn": 17, "endLineNumber": 122, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 127, "startColumn": 6, "endLineNumber": 127, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 131, "startColumn": 13, "endLineNumber": 131, "endColumn": 15}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 141, "startColumn": 11, "endLineNumber": 141, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.RevertAddonToDevelopment already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:156:28", "source": "compiler", "startLineNumber": 145, "startColumn": 28, "endLineNumber": 145, "endColumn": 52}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 146, "startColumn": 17, "endLineNumber": 146, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 151, "startColumn": 6, "endLineNumber": 151, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 155, "startColumn": 13, "endLineNumber": 155, "endColumn": 15}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 165, "startColumn": 11, "endLineNumber": 165, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.DisableAddon already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:180:28", "source": "compiler", "startLineNumber": 169, "startColumn": 28, "endLineNumber": 169, "endColumn": 40}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 170, "startColumn": 17, "endLineNumber": 170, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 175, "startColumn": 6, "endLineNumber": 175, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 179, "startColumn": 13, "endLineNumber": 179, "endColumn": 15}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 189, "startColumn": 11, "endLineNumber": 189, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.EnableAddon already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:204:28", "source": "compiler", "startLineNumber": 193, "startColumn": 28, "endLineNumber": 193, "endColumn": 39}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 194, "startColumn": 17, "endLineNumber": 194, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 199, "startColumn": 6, "endLineNumber": 199, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 203, "startColumn": 13, "endLineNumber": 203, "endColumn": 15}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 213, "startColumn": 11, "endLineNumber": 213, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateAddonMetadata already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:228:28", "source": "compiler", "startLineNumber": 217, "startColumn": 28, "endLineNumber": 217, "endColumn": 47}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.GenerateAddonPreview already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:233:28", "source": "compiler", "startLineNumber": 222, "startColumn": 28, "endLineNumber": 222, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.GetAddonPreview already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:238:28", "source": "compiler", "startLineNumber": 227, "startColumn": 28, "endLineNumber": 227, "endColumn": 43}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.StartAddonTest already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:243:28", "source": "compiler", "startLineNumber": 232, "startColumn": 28, "endLineNumber": 232, "endColumn": 42}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.Register already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:248:28", "source": "compiler", "startLineNumber": 237, "startColumn": 28, "endLineNumber": 237, "endColumn": 36}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: crypto", "source": "compiler", "startLineNumber": 238, "startColumn": 23, "endLineNumber": 238, "endColumn": 29}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 243, "startColumn": 17, "endLineNumber": 243, "endColumn": 19}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: jwt", "source": "compiler", "startLineNumber": 248, "startColumn": 11, "endLineNumber": 248, "endColumn": 14}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: jwt", "source": "compiler", "startLineNumber": 248, "startColumn": 29, "endLineNumber": 248, "endColumn": 32}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: jwt", "source": "compiler", "startLineNumber": 248, "startColumn": 53, "endLineNumber": 248, "endColumn": 56}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: jwt", "source": "compiler", "startLineNumber": 250, "startColumn": 14, "endLineNumber": 250, "endColumn": 17}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: os", "source": "compiler", "startLineNumber": 253, "startColumn": 48, "endLineNumber": 253, "endColumn": 50}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CreateVehicle already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:276:28", "source": "compiler", "startLineNumber": 265, "startColumn": 28, "endLineNumber": 265, "endColumn": 41}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 266, "startColumn": 17, "endLineNumber": 266, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 270, "startColumn": 11, "endLineNumber": 270, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateVehicle already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:285:28", "source": "compiler", "startLineNumber": 274, "startColumn": 28, "endLineNumber": 274, "endColumn": 41}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 275, "startColumn": 17, "endLineNumber": 275, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 280, "startColumn": 20, "endLineNumber": 280, "endColumn": 22}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.DeleteVehicle already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:304:28", "source": "compiler", "startLineNumber": 293, "startColumn": 28, "endLineNumber": 293, "endColumn": 41}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 294, "startColumn": 17, "endLineNumber": 294, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 299, "startColumn": 13, "endLineNumber": 299, "endColumn": 15}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.GenerateSiteAPIKey already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:319:28", "source": "compiler", "startLineNumber": 308, "startColumn": 28, "endLineNumber": 308, "endColumn": 46}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 309, "startColumn": 17, "endLineNumber": 309, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 314, "startColumn": 11, "endLineNumber": 314, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.RevokeAPIKey already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:329:28", "source": "compiler", "startLineNumber": 318, "startColumn": 28, "endLineNumber": 318, "endColumn": 40}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 319, "startColumn": 17, "endLineNumber": 319, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 324, "startColumn": 10, "endLineNumber": 324, "endColumn": 12}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.RotateAPIKey already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:340:28", "source": "compiler", "startLineNumber": 329, "startColumn": 28, "endLineNumber": 329, "endColumn": 40}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 330, "startColumn": 17, "endLineNumber": 330, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 335, "startColumn": 11, "endLineNumber": 335, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CreateWebAddon already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:350:28", "source": "compiler", "startLineNumber": 339, "startColumn": 28, "endLineNumber": 339, "endColumn": 42}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateWebAddon already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:355:28", "source": "compiler", "startLineNumber": 344, "startColumn": 28, "endLineNumber": 344, "endColumn": 42}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CreatePredefinedSnippet already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:360:28", "source": "compiler", "startLineNumber": 349, "startColumn": 28, "endLineNumber": 349, "endColumn": 51}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 350, "startColumn": 17, "endLineNumber": 350, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 363, "startColumn": 11, "endLineNumber": 363, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdatePredefinedSnippet already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:378:28", "source": "compiler", "startLineNumber": 367, "startColumn": 28, "endLineNumber": 367, "endColumn": 51}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 368, "startColumn": 17, "endLineNumber": 368, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 381, "startColumn": 11, "endLineNumber": 381, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.MovePage already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:396:28", "source": "compiler", "startLineNumber": 385, "startColumn": 28, "endLineNumber": 385, "endColumn": 36}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 386, "startColumn": 17, "endLineNumber": 386, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 391, "startColumn": 6, "endLineNumber": 391, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 395, "startColumn": 11, "endLineNumber": 395, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CreateSnippet already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:410:28", "source": "compiler", "startLineNumber": 399, "startColumn": 28, "endLineNumber": 399, "endColumn": 41}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 400, "startColumn": 17, "endLineNumber": 400, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 405, "startColumn": 6, "endLineNumber": 405, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 409, "startColumn": 11, "endLineNumber": 409, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateSnippet already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:424:28", "source": "compiler", "startLineNumber": 413, "startColumn": 28, "endLineNumber": 413, "endColumn": 41}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 414, "startColumn": 17, "endLineNumber": 414, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 419, "startColumn": 6, "endLineNumber": 419, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 423, "startColumn": 11, "endLineNumber": 423, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.DeleteSnippet already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:438:28", "source": "compiler", "startLineNumber": 427, "startColumn": 28, "endLineNumber": 427, "endColumn": 41}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 428, "startColumn": 17, "endLineNumber": 428, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 433, "startColumn": 6, "endLineNumber": 433, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 437, "startColumn": 10, "endLineNumber": 437, "endColumn": 12}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CallExternalAPI already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:457:28", "source": "compiler", "startLineNumber": 446, "startColumn": 28, "endLineNumber": 446, "endColumn": 43}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 447, "startColumn": 17, "endLineNumber": 447, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 452, "startColumn": 6, "endLineNumber": 452, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 457, "startColumn": 13, "endLineNumber": 457, "endColumn": 15}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CreateExternalAPI already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:480:28", "source": "compiler", "startLineNumber": 469, "startColumn": 28, "endLineNumber": 469, "endColumn": 45}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateExternalAPI already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:485:28", "source": "compiler", "startLineNumber": 474, "startColumn": 28, "endLineNumber": 474, "endColumn": 45}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.DeleteExternalAPI already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:490:28", "source": "compiler", "startLineNumber": 479, "startColumn": 28, "endLineNumber": 479, "endColumn": 45}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CreateAPICredentials already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:495:28", "source": "compiler", "startLineNumber": 484, "startColumn": 28, "endLineNumber": 484, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateAPICredentials already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:500:28", "source": "compiler", "startLineNumber": 489, "startColumn": 28, "endLineNumber": 489, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.DeleteAPICredentials already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:505:28", "source": "compiler", "startLineNumber": 494, "startColumn": 28, "endLineNumber": 494, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.TestAPIConnection already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:510:28", "source": "compiler", "startLineNumber": 499, "startColumn": 28, "endLineNumber": 499, "endColumn": 45}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.ProxyAPICall already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:515:28", "source": "compiler", "startLineNumber": 504, "startColumn": 28, "endLineNumber": 504, "endColumn": 40}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.AdminUpdateUserStatus already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:520:28", "source": "compiler", "startLineNumber": 509, "startColumn": 28, "endLineNumber": 509, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.AdminUpdateUserRole already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:525:28", "source": "compiler", "startLineNumber": 514, "startColumn": 28, "endLineNumber": 514, "endColumn": 47}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.AdminAddUserNote already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:530:28", "source": "compiler", "startLineNumber": 519, "startColumn": 28, "endLineNumber": 519, "endColumn": 44}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.AdminSuspendUser already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:535:28", "source": "compiler", "startLineNumber": 524, "startColumn": 28, "endLineNumber": 524, "endColumn": 44}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.AdminUnsuspendUser already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:540:28", "source": "compiler", "startLineNumber": 529, "startColumn": 28, "endLineNumber": 529, "endColumn": 46}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.AdminDeleteUser already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:545:28", "source": "compiler", "startLineNumber": 534, "startColumn": 28, "endLineNumber": 534, "endColumn": 43}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.AdminReviewExpertApplication already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:550:28", "source": "compiler", "startLineNumber": 539, "startColumn": 28, "endLineNumber": 539, "endColumn": 56}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.AdminApproveExpertApplication already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:555:28", "source": "compiler", "startLineNumber": 544, "startColumn": 28, "endLineNumber": 544, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.AdminRejectExpertApplication already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:560:28", "source": "compiler", "startLineNumber": 549, "startColumn": 28, "endLineNumber": 549, "endColumn": 56}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.AdminApproveContent already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:565:28", "source": "compiler", "startLineNumber": 554, "startColumn": 28, "endLineNumber": 554, "endColumn": 47}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.AdminRejectContent already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:570:28", "source": "compiler", "startLineNumber": 559, "startColumn": 28, "endLineNumber": 559, "endColumn": 46}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.AdminHideContent already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:575:28", "source": "compiler", "startLineNumber": 564, "startColumn": 28, "endLineNumber": 564, "endColumn": 44}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.AdminDeleteContent already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:580:28", "source": "compiler", "startLineNumber": 569, "startColumn": 28, "endLineNumber": 569, "endColumn": 46}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.AdminApproveAddon already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:585:28", "source": "compiler", "startLineNumber": 574, "startColumn": 28, "endLineNumber": 574, "endColumn": 45}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.AdminRejectAddon already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:590:28", "source": "compiler", "startLineNumber": 579, "startColumn": 28, "endLineNumber": 579, "endColumn": 44}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.AdminRequestAddonChanges already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:595:28", "source": "compiler", "startLineNumber": 584, "startColumn": 28, "endLineNumber": 584, "endColumn": 52}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateProfile already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:600:28", "source": "compiler", "startLineNumber": 589, "startColumn": 28, "endLineNumber": 589, "endColumn": 41}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateSettings already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:605:28", "source": "compiler", "startLineNumber": 594, "startColumn": 28, "endLineNumber": 594, "endColumn": 42}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.ChangePassword already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:610:28", "source": "compiler", "startLineNumber": 599, "startColumn": 28, "endLineNumber": 599, "endColumn": 42}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UploadAvatar already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:615:28", "source": "compiler", "startLineNumber": 604, "startColumn": 28, "endLineNumber": 604, "endColumn": 40}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.DeleteAccount already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:620:28", "source": "compiler", "startLineNumber": 609, "startColumn": 28, "endLineNumber": 609, "endColumn": 41}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CreateSite already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:625:28", "source": "compiler", "startLineNumber": 614, "startColumn": 28, "endLineNumber": 614, "endColumn": 38}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 615, "startColumn": 30, "endLineNumber": 615, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 615, "startColumn": 53, "endLineNumber": 615, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: sitemanager", "source": "compiler", "startLineNumber": 620, "startColumn": 17, "endLineNumber": 620, "endColumn": 28}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 620, "startColumn": 46, "endLineNumber": 620, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: sitemanager", "source": "compiler", "startLineNumber": 622, "startColumn": 18, "endLineNumber": 622, "endColumn": 29}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateSite already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:684:28", "source": "compiler", "startLineNumber": 673, "startColumn": 28, "endLineNumber": 673, "endColumn": 38}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 674, "startColumn": 30, "endLineNumber": 674, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 674, "startColumn": 53, "endLineNumber": 674, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: sitemanager", "source": "compiler", "startLineNumber": 684, "startColumn": 17, "endLineNumber": 684, "endColumn": 28}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 684, "startColumn": 46, "endLineNumber": 684, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: sitemanager", "source": "compiler", "startLineNumber": 686, "startColumn": 18, "endLineNumber": 686, "endColumn": 29}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.DeleteSite already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:752:28", "source": "compiler", "startLineNumber": 741, "startColumn": 28, "endLineNumber": 741, "endColumn": 38}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 742, "startColumn": 30, "endLineNumber": 742, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 742, "startColumn": 53, "endLineNumber": 742, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: sitemanager", "source": "compiler", "startLineNumber": 752, "startColumn": 17, "endLineNumber": 752, "endColumn": 28}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 752, "startColumn": 46, "endLineNumber": 752, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.PublishSite already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:777:28", "source": "compiler", "startLineNumber": 766, "startColumn": 28, "endLineNumber": 766, "endColumn": 39}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UnpublishSite already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:782:28", "source": "compiler", "startLineNumber": 771, "startColumn": 28, "endLineNumber": 771, "endColumn": 41}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CreatePage already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:787:28", "source": "compiler", "startLineNumber": 776, "startColumn": 28, "endLineNumber": 776, "endColumn": 38}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 777, "startColumn": 30, "endLineNumber": 777, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 777, "startColumn": 53, "endLineNumber": 777, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: pagemanager", "source": "compiler", "startLineNumber": 782, "startColumn": 17, "endLineNumber": 782, "endColumn": 28}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 782, "startColumn": 54, "endLineNumber": 782, "endColumn": 56}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: pagemanager", "source": "compiler", "startLineNumber": 788, "startColumn": 18, "endLineNumber": 788, "endColumn": 29}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdatePage already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:842:28", "source": "compiler", "startLineNumber": 831, "startColumn": 28, "endLineNumber": 831, "endColumn": 38}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 832, "startColumn": 30, "endLineNumber": 832, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 832, "startColumn": 53, "endLineNumber": 832, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: pagemanager", "source": "compiler", "startLineNumber": 837, "startColumn": 25, "endLineNumber": 837, "endColumn": 36}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 837, "startColumn": 62, "endLineNumber": 837, "endColumn": 64}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: pagemanager", "source": "compiler", "startLineNumber": 839, "startColumn": 18, "endLineNumber": 839, "endColumn": 29}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.DeletePage already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:912:28", "source": "compiler", "startLineNumber": 901, "startColumn": 28, "endLineNumber": 901, "endColumn": 38}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 902, "startColumn": 30, "endLineNumber": 902, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 902, "startColumn": 53, "endLineNumber": 902, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: pagemanager", "source": "compiler", "startLineNumber": 907, "startColumn": 25, "endLineNumber": 907, "endColumn": 36}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 907, "startColumn": 62, "endLineNumber": 907, "endColumn": 64}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.PublishPage already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:932:28", "source": "compiler", "startLineNumber": 921, "startColumn": 28, "endLineNumber": 921, "endColumn": 39}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 922, "startColumn": 30, "endLineNumber": 922, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 922, "startColumn": 53, "endLineNumber": 922, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: pagemanager", "source": "compiler", "startLineNumber": 927, "startColumn": 17, "endLineNumber": 927, "endColumn": 28}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 927, "startColumn": 54, "endLineNumber": 927, "endColumn": 56}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: pagemanager", "source": "compiler", "startLineNumber": 934, "startColumn": 80, "endLineNumber": 934, "endColumn": 91}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UnpublishPage already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:965:28", "source": "compiler", "startLineNumber": 954, "startColumn": 28, "endLineNumber": 954, "endColumn": 41}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateSiteSettings already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:970:28", "source": "compiler", "startLineNumber": 959, "startColumn": 28, "endLineNumber": 959, "endColumn": 46}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 960, "startColumn": 30, "endLineNumber": 960, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 960, "startColumn": 53, "endLineNumber": 960, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: sitemanager", "source": "compiler", "startLineNumber": 965, "startColumn": 17, "endLineNumber": 965, "endColumn": 28}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 965, "startColumn": 46, "endLineNumber": 965, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: sitemanager", "source": "compiler", "startLineNumber": 972, "startColumn": 18, "endLineNumber": 972, "endColumn": 29}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.SubmitExpertApplication already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1031:28", "source": "compiler", "startLineNumber": 1020, "startColumn": 28, "endLineNumber": 1020, "endColumn": 51}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateExpertProfile already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1036:28", "source": "compiler", "startLineNumber": 1025, "startColumn": 28, "endLineNumber": 1025, "endColumn": 47}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.AddPortfolioItem already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1041:28", "source": "compiler", "startLineNumber": 1030, "startColumn": 28, "endLineNumber": 1030, "endColumn": 44}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.RemovePortfolioItem already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1046:28", "source": "compiler", "startLineNumber": 1035, "startColumn": 28, "endLineNumber": 1035, "endColumn": 47}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.AddCertification already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1051:28", "source": "compiler", "startLineNumber": 1040, "startColumn": 28, "endLineNumber": 1040, "endColumn": 44}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.RemoveCertification already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1056:28", "source": "compiler", "startLineNumber": 1045, "startColumn": 28, "endLineNumber": 1045, "endColumn": 47}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CreateProject already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1061:28", "source": "compiler", "startLineNumber": 1050, "startColumn": 28, "endLineNumber": 1050, "endColumn": 41}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateProject already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1066:28", "source": "compiler", "startLineNumber": 1055, "startColumn": 28, "endLineNumber": 1055, "endColumn": 41}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.AcceptProject already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1071:28", "source": "compiler", "startLineNumber": 1060, "startColumn": 28, "endLineNumber": 1060, "endColumn": 41}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CompleteProject already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1076:28", "source": "compiler", "startLineNumber": 1065, "startColumn": 28, "endLineNumber": 1065, "endColumn": 43}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.AddProjectMessage already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1081:28", "source": "compiler", "startLineNumber": 1070, "startColumn": 28, "endLineNumber": 1070, "endColumn": 45}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CreateExpertReview already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1086:28", "source": "compiler", "startLineNumber": 1075, "startColumn": 28, "endLineNumber": 1075, "endColumn": 46}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateExpertReview already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1091:28", "source": "compiler", "startLineNumber": 1080, "startColumn": 28, "endLineNumber": 1080, "endColumn": 46}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CreateSupportTicket already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1096:28", "source": "compiler", "startLineNumber": 1085, "startColumn": 28, "endLineNumber": 1085, "endColumn": 47}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateSupportTicket already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1101:28", "source": "compiler", "startLineNumber": 1090, "startColumn": 28, "endLineNumber": 1090, "endColumn": 47}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.AddTicketMessage already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1106:28", "source": "compiler", "startLineNumber": 1095, "startColumn": 28, "endLineNumber": 1095, "endColumn": 44}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CloseSupportTicket already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1111:28", "source": "compiler", "startLineNumber": 1100, "startColumn": 28, "endLineNumber": 1100, "endColumn": 46}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.MarkNotificationRead already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1116:28", "source": "compiler", "startLineNumber": 1105, "startColumn": 28, "endLineNumber": 1105, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.MarkAllNotificationsRead already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1121:28", "source": "compiler", "startLineNumber": 1110, "startColumn": 28, "endLineNumber": 1110, "endColumn": 52}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.DeleteNotification already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1126:28", "source": "compiler", "startLineNumber": 1115, "startColumn": 28, "endLineNumber": 1115, "endColumn": 46}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CreateBusinessPlan already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1131:28", "source": "compiler", "startLineNumber": 1120, "startColumn": 28, "endLineNumber": 1120, "endColumn": 46}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateBusinessPlan already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1136:28", "source": "compiler", "startLineNumber": 1125, "startColumn": 28, "endLineNumber": 1125, "endColumn": 46}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateBusinessPlanSection already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1141:28", "source": "compiler", "startLineNumber": 1130, "startColumn": 28, "endLineNumber": 1130, "endColumn": 53}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CreateFinancialPlan already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1146:28", "source": "compiler", "startLineNumber": 1135, "startColumn": 28, "endLineNumber": 1135, "endColumn": 47}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateFinancialPlan already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1151:28", "source": "compiler", "startLineNumber": 1140, "startColumn": 28, "endLineNumber": 1140, "endColumn": 47}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CreateComplianceCheck already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1156:28", "source": "compiler", "startLineNumber": 1145, "startColumn": 28, "endLineNumber": 1145, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CreateTenant already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1161:28", "source": "compiler", "startLineNumber": 1150, "startColumn": 28, "endLineNumber": 1150, "endColumn": 40}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1152, "startColumn": 30, "endLineNumber": 1152, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1152, "startColumn": 53, "endLineNumber": 1152, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1153, "startColumn": 13, "endLineNumber": 1153, "endColumn": 17}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: database", "source": "compiler", "startLineNumber": 1157, "startColumn": 19, "endLineNumber": 1157, "endColumn": 27}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1157, "startColumn": 47, "endLineNumber": 1157, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateTenant already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1195:28", "source": "compiler", "startLineNumber": 1184, "startColumn": 28, "endLineNumber": 1184, "endColumn": 40}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1186, "startColumn": 30, "endLineNumber": 1186, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1186, "startColumn": 53, "endLineNumber": 1186, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1187, "startColumn": 13, "endLineNumber": 1187, "endColumn": 17}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: database", "source": "compiler", "startLineNumber": 1191, "startColumn": 19, "endLineNumber": 1191, "endColumn": 27}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1191, "startColumn": 47, "endLineNumber": 1191, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.DeleteTenant already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1258:28", "source": "compiler", "startLineNumber": 1247, "startColumn": 28, "endLineNumber": 1247, "endColumn": 40}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1249, "startColumn": 30, "endLineNumber": 1249, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1249, "startColumn": 53, "endLineNumber": 1249, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1250, "startColumn": 13, "endLineNumber": 1250, "endColumn": 17}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1255, "startColumn": 11, "endLineNumber": 1255, "endColumn": 15}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: database", "source": "compiler", "startLineNumber": 1259, "startColumn": 19, "endLineNumber": 1259, "endColumn": 27}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1259, "startColumn": 47, "endLineNumber": 1259, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CreateSubscriptionTier already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1280:28", "source": "compiler", "startLineNumber": 1269, "startColumn": 28, "endLineNumber": 1269, "endColumn": 50}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1271, "startColumn": 30, "endLineNumber": 1271, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1271, "startColumn": 53, "endLineNumber": 1271, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1272, "startColumn": 13, "endLineNumber": 1272, "endColumn": 17}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: subscription", "source": "compiler", "startLineNumber": 1276, "startColumn": 25, "endLineNumber": 1276, "endColumn": 37}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1276, "startColumn": 63, "endLineNumber": 1276, "endColumn": 65}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateSubscriptionTier already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1320:28", "source": "compiler", "startLineNumber": 1309, "startColumn": 28, "endLineNumber": 1309, "endColumn": 50}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.DeleteSubscriptionTier already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1325:28", "source": "compiler", "startLineNumber": 1314, "startColumn": 28, "endLineNumber": 1314, "endColumn": 50}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpgradeSubscription already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1330:28", "source": "compiler", "startLineNumber": 1319, "startColumn": 28, "endLineNumber": 1319, "endColumn": 47}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1320, "startColumn": 30, "endLineNumber": 1320, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1320, "startColumn": 53, "endLineNumber": 1320, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.SubscriptionService undefined (type *mutationResolver has no field or method SubscriptionService)", "source": "compiler", "startLineNumber": 1326, "startColumn": 11, "endLineNumber": 1326, "endColumn": 30}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.SubscriptionService undefined (type *mutationResolver has no field or method SubscriptionService)", "source": "compiler", "startLineNumber": 1332, "startColumn": 25, "endLineNumber": 1332, "endColumn": 44}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.DowngradeSubscription already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1361:28", "source": "compiler", "startLineNumber": 1350, "startColumn": 28, "endLineNumber": 1350, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CancelSubscription already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1366:28", "source": "compiler", "startLineNumber": 1355, "startColumn": 28, "endLineNumber": 1355, "endColumn": 46}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.TrackUsage already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1371:28", "source": "compiler", "startLineNumber": 1360, "startColumn": 28, "endLineNumber": 1360, "endColumn": 38}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1361, "startColumn": 30, "endLineNumber": 1361, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1361, "startColumn": 53, "endLineNumber": 1361, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: subscription", "source": "compiler", "startLineNumber": 1366, "startColumn": 25, "endLineNumber": 1366, "endColumn": 37}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1366, "startColumn": 63, "endLineNumber": 1366, "endColumn": 65}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.GenerateInvoice already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1387:28", "source": "compiler", "startLineNumber": 1376, "startColumn": 28, "endLineNumber": 1376, "endColumn": 43}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CompleteOnboardingStep already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1392:28", "source": "compiler", "startLineNumber": 1381, "startColumn": 28, "endLineNumber": 1381, "endColumn": 50}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1382, "startColumn": 30, "endLineNumber": 1382, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1382, "startColumn": 53, "endLineNumber": 1382, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: onboarding", "source": "compiler", "startLineNumber": 1387, "startColumn": 23, "endLineNumber": 1387, "endColumn": 33}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1387, "startColumn": 57, "endLineNumber": 1387, "endColumn": 59}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateUserProfile already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1443:28", "source": "compiler", "startLineNumber": 1432, "startColumn": 28, "endLineNumber": 1432, "endColumn": 45}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1433, "startColumn": 30, "endLineNumber": 1433, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1433, "startColumn": 53, "endLineNumber": 1433, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: onboarding", "source": "compiler", "startLineNumber": 1438, "startColumn": 23, "endLineNumber": 1438, "endColumn": 33}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1438, "startColumn": 57, "endLineNumber": 1438, "endColumn": 59}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: onboarding", "source": "compiler", "startLineNumber": 1440, "startColumn": 14, "endLineNumber": 1440, "endColumn": 24}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateUserSettings already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1499:28", "source": "compiler", "startLineNumber": 1488, "startColumn": 28, "endLineNumber": 1488, "endColumn": 46}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1489, "startColumn": 30, "endLineNumber": 1489, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1489, "startColumn": 53, "endLineNumber": 1489, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: onboarding", "source": "compiler", "startLineNumber": 1494, "startColumn": 23, "endLineNumber": 1494, "endColumn": 33}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1494, "startColumn": 57, "endLineNumber": 1494, "endColumn": 59}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.DuplicatePage already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1555:28", "source": "compiler", "startLineNumber": 1544, "startColumn": 28, "endLineNumber": 1544, "endColumn": 41}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.GetSiteSettings already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1560:28", "source": "compiler", "startLineNumber": 1549, "startColumn": 28, "endLineNumber": 1549, "endColumn": 43}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CompileSite already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1565:28", "source": "compiler", "startLineNumber": 1554, "startColumn": 28, "endLineNumber": 1554, "endColumn": 39}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CreateExpertApplication already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1570:28", "source": "compiler", "startLineNumber": 1559, "startColumn": 28, "endLineNumber": 1559, "endColumn": 51}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.ReviewExpertApplication already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1575:28", "source": "compiler", "startLineNumber": 1564, "startColumn": 28, "endLineNumber": 1564, "endColumn": 51}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CreateClientRequirement already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1580:28", "source": "compiler", "startLineNumber": 1569, "startColumn": 28, "endLineNumber": 1569, "endColumn": 51}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.FindExpertMatches already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1585:28", "source": "compiler", "startLineNumber": 1574, "startColumn": 28, "endLineNumber": 1574, "endColumn": 45}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.ContactExpert already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1590:28", "source": "compiler", "startLineNumber": 1579, "startColumn": 28, "endLineNumber": 1579, "endColumn": 41}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.RespondToMatch already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1595:28", "source": "compiler", "startLineNumber": 1584, "startColumn": 28, "endLineNumber": 1584, "endColumn": 42}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CreateProjectEngagement already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1600:28", "source": "compiler", "startLineNumber": 1589, "startColumn": 28, "endLineNumber": 1589, "endColumn": 51}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateEngagementStatus already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1605:28", "source": "compiler", "startLineNumber": 1594, "startColumn": 28, "endLineNumber": 1594, "endColumn": 50}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.AddProjectMilestone already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1610:28", "source": "compiler", "startLineNumber": 1599, "startColumn": 28, "endLineNumber": 1599, "endColumn": 47}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CompleteProjectMilestone already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1615:28", "source": "compiler", "startLineNumber": 1604, "startColumn": 28, "endLineNumber": 1604, "endColumn": 52}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CreateConversation already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1620:28", "source": "compiler", "startLineNumber": 1609, "startColumn": 28, "endLineNumber": 1609, "endColumn": 46}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.SendMessage already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1625:28", "source": "compiler", "startLineNumber": 1614, "startColumn": 28, "endLineNumber": 1614, "endColumn": 39}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.MarkMessageAsRead already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1630:28", "source": "compiler", "startLineNumber": 1619, "startColumn": 28, "endLineNumber": 1619, "endColumn": 45}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.MarkConversationAsRead already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1635:28", "source": "compiler", "startLineNumber": 1624, "startColumn": 28, "endLineNumber": 1624, "endColumn": 50}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateMilestoneProgress already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1640:28", "source": "compiler", "startLineNumber": 1629, "startColumn": 28, "endLineNumber": 1629, "endColumn": 51}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.Health already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1645:25", "source": "compiler", "startLineNumber": 1634, "startColumn": 25, "endLineNumber": 1634, "endColumn": 31}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.GetAddonsInReview already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1650:25", "source": "compiler", "startLineNumber": 1639, "startColumn": 25, "endLineNumber": 1639, "endColumn": 42}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1640, "startColumn": 17, "endLineNumber": 1640, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1645, "startColumn": 6, "endLineNumber": 1645, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1649, "startColumn": 20, "endLineNumber": 1649, "endColumn": 22}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.GetAddonTestSession already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1695:25", "source": "compiler", "startLineNumber": 1684, "startColumn": 25, "endLineNumber": 1684, "endColumn": 44}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.GetAddonTestHistory already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1700:25", "source": "compiler", "startLineNumber": 1689, "startColumn": 25, "endLineNumber": 1689, "endColumn": 44}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.Vehicles already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1705:25", "source": "compiler", "startLineNumber": 1694, "startColumn": 25, "endLineNumber": 1694, "endColumn": 33}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1695, "startColumn": 17, "endLineNumber": 1695, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1699, "startColumn": 11, "endLineNumber": 1699, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.VehicleByVin already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1714:25", "source": "compiler", "startLineNumber": 1703, "startColumn": 25, "endLineNumber": 1703, "endColumn": 37}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1704, "startColumn": 17, "endLineNumber": 1704, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1708, "startColumn": 11, "endLineNumber": 1708, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.WebAddons already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1723:25", "source": "compiler", "startLineNumber": 1712, "startColumn": 25, "endLineNumber": 1712, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1713, "startColumn": 11, "endLineNumber": 1713, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.PredefinedSnippets already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1728:25", "source": "compiler", "startLineNumber": 1717, "startColumn": 25, "endLineNumber": 1717, "endColumn": 43}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1718, "startColumn": 11, "endLineNumber": 1718, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.Component already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1733:25", "source": "compiler", "startLineNumber": 1722, "startColumn": 25, "endLineNumber": 1722, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1725, "startColumn": 20, "endLineNumber": 1725, "endColumn": 22}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1739, "startColumn": 22, "endLineNumber": 1739, "endColumn": 24}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.GetNavigation already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1766:25", "source": "compiler", "startLineNumber": 1755, "startColumn": 25, "endLineNumber": 1755, "endColumn": 38}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1756, "startColumn": 17, "endLineNumber": 1756, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1761, "startColumn": 18, "endLineNumber": 1761, "endColumn": 20}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.Tenants already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1781:25", "source": "compiler", "startLineNumber": 1770, "startColumn": 25, "endLineNumber": 1770, "endColumn": 32}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1772, "startColumn": 30, "endLineNumber": 1772, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1772, "startColumn": 53, "endLineNumber": 1772, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1773, "startColumn": 13, "endLineNumber": 1773, "endColumn": 17}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: database", "source": "compiler", "startLineNumber": 1777, "startColumn": 19, "endLineNumber": 1777, "endColumn": 27}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1777, "startColumn": 47, "endLineNumber": 1777, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.Tenant already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1831:25", "source": "compiler", "startLineNumber": 1820, "startColumn": 25, "endLineNumber": 1820, "endColumn": 31}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1822, "startColumn": 30, "endLineNumber": 1822, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1822, "startColumn": 53, "endLineNumber": 1822, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1827, "startColumn": 6, "endLineNumber": 1827, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: database", "source": "compiler", "startLineNumber": 1831, "startColumn": 19, "endLineNumber": 1831, "endColumn": 27}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1831, "startColumn": 47, "endLineNumber": 1831, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.TenantStats already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1864:25", "source": "compiler", "startLineNumber": 1853, "startColumn": 25, "endLineNumber": 1853, "endColumn": 36}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1855, "startColumn": 30, "endLineNumber": 1855, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1855, "startColumn": 53, "endLineNumber": 1855, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1860, "startColumn": 6, "endLineNumber": 1860, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: database", "source": "compiler", "startLineNumber": 1864, "startColumn": 19, "endLineNumber": 1864, "endColumn": 27}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1864, "startColumn": 47, "endLineNumber": 1864, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.TenantLimits already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1890:25", "source": "compiler", "startLineNumber": 1879, "startColumn": 25, "endLineNumber": 1879, "endColumn": 37}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1881, "startColumn": 30, "endLineNumber": 1881, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1881, "startColumn": 53, "endLineNumber": 1881, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1886, "startColumn": 6, "endLineNumber": 1886, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: database", "source": "compiler", "startLineNumber": 1890, "startColumn": 19, "endLineNumber": 1890, "endColumn": 27}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1890, "startColumn": 47, "endLineNumber": 1890, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.SubscriptionTiers already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1914:25", "source": "compiler", "startLineNumber": 1903, "startColumn": 25, "endLineNumber": 1903, "endColumn": 42}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: subscription", "source": "compiler", "startLineNumber": 1904, "startColumn": 25, "endLineNumber": 1904, "endColumn": 37}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1904, "startColumn": 63, "endLineNumber": 1904, "endColumn": 65}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.SubscriptionTier already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1939:25", "source": "compiler", "startLineNumber": 1928, "startColumn": 25, "endLineNumber": 1928, "endColumn": 41}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.UserSubscription already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1944:25", "source": "compiler", "startLineNumber": 1933, "startColumn": 25, "endLineNumber": 1933, "endColumn": 41}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1934, "startColumn": 30, "endLineNumber": 1934, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1934, "startColumn": 53, "endLineNumber": 1934, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: subscription", "source": "compiler", "startLineNumber": 1939, "startColumn": 25, "endLineNumber": 1939, "endColumn": 37}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1939, "startColumn": 63, "endLineNumber": 1939, "endColumn": 65}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.UserSubscriptionHistory already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1976:25", "source": "compiler", "startLineNumber": 1965, "startColumn": 25, "endLineNumber": 1965, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.CurrentUsage already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1981:25", "source": "compiler", "startLineNumber": 1970, "startColumn": 25, "endLineNumber": 1970, "endColumn": 37}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1971, "startColumn": 30, "endLineNumber": 1971, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1971, "startColumn": 53, "endLineNumber": 1971, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: subscription", "source": "compiler", "startLineNumber": 1976, "startColumn": 25, "endLineNumber": 1976, "endColumn": 37}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1976, "startColumn": 63, "endLineNumber": 1976, "endColumn": 65}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.UsageHistory already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1997:25", "source": "compiler", "startLineNumber": 1986, "startColumn": 25, "endLineNumber": 1986, "endColumn": 37}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.UserInvoices already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2002:25", "source": "compiler", "startLineNumber": 1991, "startColumn": 25, "endLineNumber": 1991, "endColumn": 37}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.Invoice already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2007:25", "source": "compiler", "startLineNumber": 1996, "startColumn": 25, "endLineNumber": 1996, "endColumn": 32}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.OnboardingProgress already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2012:25", "source": "compiler", "startLineNumber": 2001, "startColumn": 25, "endLineNumber": 2001, "endColumn": 43}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2002, "startColumn": 30, "endLineNumber": 2002, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2002, "startColumn": 53, "endLineNumber": 2002, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: onboarding", "source": "compiler", "startLineNumber": 2007, "startColumn": 23, "endLineNumber": 2007, "endColumn": 33}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2007, "startColumn": 57, "endLineNumber": 2007, "endColumn": 59}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.UserProfile already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2051:25", "source": "compiler", "startLineNumber": 2040, "startColumn": 25, "endLineNumber": 2040, "endColumn": 36}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2041, "startColumn": 30, "endLineNumber": 2041, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2041, "startColumn": 53, "endLineNumber": 2041, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2057, "startColumn": 11, "endLineNumber": 2057, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.UserSettings already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2082:25", "source": "compiler", "startLineNumber": 2071, "startColumn": 25, "endLineNumber": 2071, "endColumn": 37}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2072, "startColumn": 30, "endLineNumber": 2072, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2072, "startColumn": 53, "endLineNumber": 2072, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: onboarding", "source": "compiler", "startLineNumber": 2077, "startColumn": 23, "endLineNumber": 2077, "endColumn": 33}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2077, "startColumn": 57, "endLineNumber": 2077, "endColumn": 59}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.DashboardStats already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2112:25", "source": "compiler", "startLineNumber": 2101, "startColumn": 25, "endLineNumber": 2101, "endColumn": 39}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2102, "startColumn": 30, "endLineNumber": 2102, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2102, "startColumn": 53, "endLineNumber": 2102, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: dashboard", "source": "compiler", "startLineNumber": 2107, "startColumn": 22, "endLineNumber": 2107, "endColumn": 31}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2107, "startColumn": 54, "endLineNumber": 2107, "endColumn": 56}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.MySites already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2149:25", "source": "compiler", "startLineNumber": 2138, "startColumn": 25, "endLineNumber": 2138, "endColumn": 32}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2139, "startColumn": 30, "endLineNumber": 2139, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2139, "startColumn": 53, "endLineNumber": 2139, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: sitemanager", "source": "compiler", "startLineNumber": 2144, "startColumn": 17, "endLineNumber": 2144, "endColumn": 28}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2144, "startColumn": 46, "endLineNumber": 2144, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.Site already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2190:25", "source": "compiler", "startLineNumber": 2179, "startColumn": 25, "endLineNumber": 2179, "endColumn": 29}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2180, "startColumn": 30, "endLineNumber": 2180, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2180, "startColumn": 53, "endLineNumber": 2180, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: sitemanager", "source": "compiler", "startLineNumber": 2190, "startColumn": 17, "endLineNumber": 2190, "endColumn": 28}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2190, "startColumn": 46, "endLineNumber": 2190, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.SitePages already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2269:25", "source": "compiler", "startLineNumber": 2258, "startColumn": 25, "endLineNumber": 2258, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2259, "startColumn": 30, "endLineNumber": 2259, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2259, "startColumn": 53, "endLineNumber": 2259, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: sitemanager", "source": "compiler", "startLineNumber": 2269, "startColumn": 17, "endLineNumber": 2269, "endColumn": 28}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2269, "startColumn": 46, "endLineNumber": 2269, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.Page already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2322:25", "source": "compiler", "startLineNumber": 2311, "startColumn": 25, "endLineNumber": 2311, "endColumn": 29}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2312, "startColumn": 30, "endLineNumber": 2312, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2312, "startColumn": 53, "endLineNumber": 2312, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: pagemanager", "source": "compiler", "startLineNumber": 2317, "startColumn": 25, "endLineNumber": 2317, "endColumn": 36}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2317, "startColumn": 62, "endLineNumber": 2317, "endColumn": 64}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.PagesByParent already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2365:25", "source": "compiler", "startLineNumber": 2354, "startColumn": 25, "endLineNumber": 2354, "endColumn": 38}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2355, "startColumn": 30, "endLineNumber": 2355, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2355, "startColumn": 53, "endLineNumber": 2355, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: pagemanager", "source": "compiler", "startLineNumber": 2365, "startColumn": 25, "endLineNumber": 2365, "endColumn": 36}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2365, "startColumn": 62, "endLineNumber": 2365, "endColumn": 64}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.SiteSettings already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2422:25", "source": "compiler", "startLineNumber": 2411, "startColumn": 25, "endLineNumber": 2411, "endColumn": 37}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2412, "startColumn": 30, "endLineNumber": 2412, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2412, "startColumn": 53, "endLineNumber": 2412, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: sitemanager", "source": "compiler", "startLineNumber": 2417, "startColumn": 17, "endLineNumber": 2417, "endColumn": 28}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2417, "startColumn": 46, "endLineNumber": 2417, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.SitePublishingHistory already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2457:25", "source": "compiler", "startLineNumber": 2446, "startColumn": 25, "endLineNumber": 2446, "endColumn": 46}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.SiteVersions already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2462:25", "source": "compiler", "startLineNumber": 2451, "startColumn": 25, "endLineNumber": 2451, "endColumn": 37}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.ExpertApplications already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2467:25", "source": "compiler", "startLineNumber": 2456, "startColumn": 25, "endLineNumber": 2456, "endColumn": 43}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.ExpertApplication already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2472:25", "source": "compiler", "startLineNumber": 2461, "startColumn": 25, "endLineNumber": 2461, "endColumn": 42}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.MyExpertApplication already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2477:25", "source": "compiler", "startLineNumber": 2466, "startColumn": 25, "endLineNumber": 2466, "endColumn": 44}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.ClientRequirements already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2482:25", "source": "compiler", "startLineNumber": 2471, "startColumn": 25, "endLineNumber": 2471, "endColumn": 43}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.ClientRequirement already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2487:25", "source": "compiler", "startLineNumber": 2476, "startColumn": 25, "endLineNumber": 2476, "endColumn": 42}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.MyClientRequirements already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2492:25", "source": "compiler", "startLineNumber": 2481, "startColumn": 25, "endLineNumber": 2481, "endColumn": 45}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.ExpertMatches already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2497:25", "source": "compiler", "startLineNumber": 2486, "startColumn": 25, "endLineNumber": 2486, "endColumn": 38}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.MyMatches already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2502:25", "source": "compiler", "startLineNumber": 2491, "startColumn": 25, "endLineNumber": 2491, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.ProjectEngagements already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2507:25", "source": "compiler", "startLineNumber": 2496, "startColumn": 25, "endLineNumber": 2496, "endColumn": 43}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.ProjectEngagement already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2512:25", "source": "compiler", "startLineNumber": 2501, "startColumn": 25, "endLineNumber": 2501, "endColumn": 42}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.MyProjectEngagements already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2517:25", "source": "compiler", "startLineNumber": 2506, "startColumn": 25, "endLineNumber": 2506, "endColumn": 45}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.MyConversations already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2522:25", "source": "compiler", "startLineNumber": 2511, "startColumn": 25, "endLineNumber": 2511, "endColumn": 40}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.Conversation already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2527:25", "source": "compiler", "startLineNumber": 2516, "startColumn": 25, "endLineNumber": 2516, "endColumn": 37}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.ConversationMessages already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2532:25", "source": "compiler", "startLineNumber": 2521, "startColumn": 25, "endLineNumber": 2521, "endColumn": 45}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.ExternalAPIs already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2537:25", "source": "compiler", "startLineNumber": 2526, "startColumn": 25, "endLineNumber": 2526, "endColumn": 37}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.ExternalAPI already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2542:25", "source": "compiler", "startLineNumber": 2531, "startColumn": 25, "endLineNumber": 2531, "endColumn": 36}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.MyAPICredentials already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2547:25", "source": "compiler", "startLineNumber": 2536, "startColumn": 25, "endLineNumber": 2536, "endColumn": 41}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.APIConnections already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2552:25", "source": "compiler", "startLineNumber": 2541, "startColumn": 25, "endLineNumber": 2541, "endColumn": 39}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.Me already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2557:25", "source": "compiler", "startLineNumber": 2546, "startColumn": 25, "endLineNumber": 2546, "endColumn": 27}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.Notifications already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2562:25", "source": "compiler", "startLineNumber": 2551, "startColumn": 25, "endLineNumber": 2551, "endColumn": 38}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.SiteAnalytics already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2567:25", "source": "compiler", "startLineNumber": 2556, "startColumn": 25, "endLineNumber": 2556, "endColumn": 38}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.ExpertProfiles already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2572:25", "source": "compiler", "startLineNumber": 2561, "startColumn": 25, "endLineNumber": 2561, "endColumn": 39}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.ExpertProfile already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2577:25", "source": "compiler", "startLineNumber": 2566, "startColumn": 25, "endLineNumber": 2566, "endColumn": 38}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.MyExpertProfile already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2582:25", "source": "compiler", "startLineNumber": 2571, "startColumn": 25, "endLineNumber": 2571, "endColumn": 40}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.Projects already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2587:25", "source": "compiler", "startLineNumber": 2576, "startColumn": 25, "endLineNumber": 2576, "endColumn": 33}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.Project already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2592:25", "source": "compiler", "startLineNumber": 2581, "startColumn": 25, "endLineNumber": 2581, "endColumn": 32}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.MyTickets already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2597:25", "source": "compiler", "startLineNumber": 2586, "startColumn": 25, "endLineNumber": 2586, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.Ticket already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2602:25", "source": "compiler", "startLineNumber": 2591, "startColumn": 25, "endLineNumber": 2591, "endColumn": 31}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.BusinessPlan already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2607:25", "source": "compiler", "startLineNumber": 2596, "startColumn": 25, "endLineNumber": 2596, "endColumn": 37}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.FinancialPlan already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2612:25", "source": "compiler", "startLineNumber": 2601, "startColumn": 25, "endLineNumber": 2601, "endColumn": 38}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.CompetitorResearch already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2617:25", "source": "compiler", "startLineNumber": 2606, "startColumn": 25, "endLineNumber": 2606, "endColumn": 43}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.ComplianceCheck already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2622:25", "source": "compiler", "startLineNumber": 2611, "startColumn": 25, "endLineNumber": 2611, "endColumn": 40}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.AdminDashboardKPIs already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2627:25", "source": "compiler", "startLineNumber": 2616, "startColumn": 25, "endLineNumber": 2616, "endColumn": 43}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.AdminUsers already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2632:25", "source": "compiler", "startLineNumber": 2621, "startColumn": 25, "endLineNumber": 2621, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.AdminUser already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2637:25", "source": "compiler", "startLineNumber": 2626, "startColumn": 25, "endLineNumber": 2626, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.AdminUserStats already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2642:25", "source": "compiler", "startLineNumber": 2631, "startColumn": 25, "endLineNumber": 2631, "endColumn": 39}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.AdminExpertApplications already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2647:25", "source": "compiler", "startLineNumber": 2636, "startColumn": 25, "endLineNumber": 2636, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.AdminExpertApplication already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2652:25", "source": "compiler", "startLineNumber": 2641, "startColumn": 25, "endLineNumber": 2641, "endColumn": 47}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.AdminExpertProfiles already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2657:25", "source": "compiler", "startLineNumber": 2646, "startColumn": 25, "endLineNumber": 2646, "endColumn": 44}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.AdminModerationQueue already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2662:25", "source": "compiler", "startLineNumber": 2651, "startColumn": 25, "endLineNumber": 2651, "endColumn": 45}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.AdminModerationStats already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2667:25", "source": "compiler", "startLineNumber": 2656, "startColumn": 25, "endLineNumber": 2656, "endColumn": 45}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.AdminAddonReviews already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2672:25", "source": "compiler", "startLineNumber": 2661, "startColumn": 25, "endLineNumber": 2661, "endColumn": 42}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.AdminAddonReview already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2677:25", "source": "compiler", "startLineNumber": 2666, "startColumn": 25, "endLineNumber": 2666, "endColumn": 41}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.AdminAddonReviewStats already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2682:25", "source": "compiler", "startLineNumber": 2671, "startColumn": 25, "endLineNumber": 2671, "endColumn": 46}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.SystemHealth already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2687:25", "source": "compiler", "startLineNumber": 2676, "startColumn": 25, "endLineNumber": 2676, "endColumn": 37}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method Resolver.Mutation already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2707:20", "source": "compiler", "startLineNumber": 2681, "startColumn": 20, "endLineNumber": 2681, "endColumn": 28}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method Resolver.Query already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2710:20", "source": "compiler", "startLineNumber": 2684, "startColumn": 20, "endLineNumber": 2684, "endColumn": 25}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateDecl", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateDecl"}}, "severity": 8, "message": "mutationResolver redeclared in this block", "source": "compiler", "startLineNumber": 2686, "startColumn": 6, "endLineNumber": 2686, "endColumn": 22, "relatedInformation": [{"startLineNumber": 2718, "startColumn": 6, "endLineNumber": 2718, "endColumn": 22, "message": "other declaration of mutationResolver", "resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go"}]}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateDecl", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateDecl"}}, "severity": 8, "message": "queryResolver redeclared in this block", "source": "compiler", "startLineNumber": 2687, "startColumn": 6, "endLineNumber": 2687, "endColumn": 19, "relatedInformation": [{"startLineNumber": 2719, "startColumn": 6, "endLineNumber": 2719, "endColumn": 19, "message": "other declaration of queryResolver", "resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go"}]}]