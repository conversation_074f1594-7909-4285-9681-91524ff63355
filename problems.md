[{"resource": "/c:/Users/<USER>/Desktop/goVwPlatformAPI/cmd/api/main.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "BrokenImport", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "BrokenImport"}}, "severity": 8, "message": "could not import goVwPlatformAPI/graph/generated (missing import goVwPlatformAPI/graph/generated)", "source": "compiler", "startLineNumber": 13, "startColumn": 2, "endLineNumber": 13, "endColumn": 35}, {"resource": "/c:/Users/<USER>/Desktop/goVwPlatformAPI/cmd/api/main.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field DB in struct literal of type graph.Resolver", "source": "compiler", "startLineNumber": 1278, "startColumn": 3, "endLineNumber": 1278, "endColumn": 5}, {"resource": "/c:/Users/<USER>/Desktop/goVwPlatformAPI/cmd/api/main.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field SubscriptionService in struct literal of type graph.Resolver", "source": "compiler", "startLineNumber": 1279, "startColumn": 3, "endLineNumber": 1279, "endColumn": 22}, {"resource": "/c:/Users/<USER>/Desktop/goVwPlatformAPI/cmd/api/main.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field PageManager in struct literal of type graph.Resolver", "source": "compiler", "startLineNumber": 1280, "startColumn": 3, "endLineNumber": 1280, "endColumn": 14}, {"resource": "/c:/Users/<USER>/Desktop/goVwPlatformAPI/cmd/api/main.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field OnboardingService in struct literal of type graph.Resolver", "source": "compiler", "startLineNumber": 1281, "startColumn": 3, "endLineNumber": 1281, "endColumn": 20}, {"resource": "/c:/Users/<USER>/Desktop/goVwPlatformAPI/cmd/api/main.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field DashboardService in struct literal of type graph.Resolver", "source": "compiler", "startLineNumber": 1282, "startColumn": 3, "endLineNumber": 1282, "endColumn": 19}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "BrokenImport", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "BrokenImport"}}, "severity": 8, "message": "could not import context (missing metadata for import of \"context\")", "source": "compiler", "startLineNumber": 6, "startColumn": 2, "endLineNumber": 6, "endColumn": 11}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "BrokenImport", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "BrokenImport"}}, "severity": 8, "message": "could not import fmt (missing metadata for import of \"fmt\")", "source": "compiler", "startLineNumber": 7, "startColumn": 2, "endLineNumber": 7, "endColumn": 7}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "severity": 8, "message": "package goVwPlatformAPI/graph/generated is not in std (C:\\Program Files\\Go\\src\\goVwPlatformAPI\\graph\\generated)", "source": "go list", "startLineNumber": 8, "startColumn": 2, "endLineNumber": 8, "endColumn": 2}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "BrokenImport", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "BrokenImport"}}, "severity": 8, "message": "could not import goVwPlatformAPI/graph/generated (missing import goVwPlatformAPI/graph/generated)", "source": "compiler", "startLineNumber": 8, "startColumn": 2, "endLineNumber": 8, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "BrokenImport", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "BrokenImport"}}, "severity": 8, "message": "could not import goVwPlatformAPI/graph/generated (missing metadata for import of \"goVwPlatformAPI/graph/generated\")", "source": "compiler", "startLineNumber": 8, "startColumn": 2, "endLineNumber": 8, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "BrokenImport", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "BrokenImport"}}, "severity": 8, "message": "could not import goVwPlatformAPI/graph/model (missing metadata for import of \"goVwPlatformAPI/graph/model\")", "source": "compiler", "startLineNumber": 9, "startColumn": 2, "endLineNumber": 9, "endColumn": 31}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "BrokenImport", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "BrokenImport"}}, "severity": 8, "message": "could not import goVwPlatformAPI/internal/auth (missing metadata for import of \"goVwPlatformAPI/internal/auth\")", "source": "compiler", "startLineNumber": 10, "startColumn": 2, "endLineNumber": 10, "endColumn": 33}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "BrokenImport", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "BrokenImport"}}, "severity": 8, "message": "could not import goVwPlatformAPI/internal/crypto (missing metadata for import of \"goVwPlatformAPI/internal/crypto\")", "source": "compiler", "startLineNumber": 11, "startColumn": 2, "endLineNumber": 11, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "BrokenImport", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "BrokenImport"}}, "severity": 8, "message": "could not import goVwPlatformAPI/internal/dashboard (missing metadata for import of \"goVwPlatformAPI/internal/dashboard\")", "source": "compiler", "startLineNumber": 12, "startColumn": 2, "endLineNumber": 12, "endColumn": 38}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "BrokenImport", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "BrokenImport"}}, "severity": 8, "message": "could not import goVwPlatformAPI/internal/database (missing metadata for import of \"goVwPlatformAPI/internal/database\")", "source": "compiler", "startLineNumber": 13, "startColumn": 2, "endLineNumber": 13, "endColumn": 37}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "BrokenImport", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "BrokenImport"}}, "severity": 8, "message": "could not import goVwPlatformAPI/internal/onboarding (missing metadata for import of \"goVwPlatformAPI/internal/onboarding\")", "source": "compiler", "startLineNumber": 14, "startColumn": 2, "endLineNumber": 14, "endColumn": 39}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "BrokenImport", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "BrokenImport"}}, "severity": 8, "message": "could not import goVwPlatformAPI/internal/pagemanager (missing metadata for import of \"goVwPlatformAPI/internal/pagemanager\")", "source": "compiler", "startLineNumber": 15, "startColumn": 2, "endLineNumber": 15, "endColumn": 40}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "BrokenImport", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "BrokenImport"}}, "severity": 8, "message": "could not import goVwPlatformAPI/internal/sitemanager (missing metadata for import of \"goVwPlatformAPI/internal/sitemanager\")", "source": "compiler", "startLineNumber": 16, "startColumn": 2, "endLineNumber": 16, "endColumn": 40}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "BrokenImport", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "BrokenImport"}}, "severity": 8, "message": "could not import goVwPlatformAPI/internal/subscription (missing metadata for import of \"goVwPlatformAPI/internal/subscription\")", "source": "compiler", "startLineNumber": 17, "startColumn": 2, "endLineNumber": 17, "endColumn": 41}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "BrokenImport", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "BrokenImport"}}, "severity": 8, "message": "could not import os (missing metadata for import of \"os\")", "source": "compiler", "startLineNumber": 18, "startColumn": 2, "endLineNumber": 18, "endColumn": 6}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "BrokenImport", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "BrokenImport"}}, "severity": 8, "message": "could not import strconv (missing metadata for import of \"strconv\")", "source": "compiler", "startLineNumber": 19, "startColumn": 2, "endLineNumber": 19, "endColumn": 11}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "BrokenImport", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "BrokenImport"}}, "severity": 8, "message": "could not import time (missing metadata for import of \"time\")", "source": "compiler", "startLineNumber": 20, "startColumn": 2, "endLineNumber": 20, "endColumn": 8}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "BrokenImport", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "BrokenImport"}}, "severity": 8, "message": "could not import github.com/99designs/gqlgen/graphql (missing metadata for import of \"github.com/99designs/gqlgen/graphql\")", "source": "compiler", "startLineNumber": 22, "startColumn": 2, "endLineNumber": 22, "endColumn": 39}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "BrokenImport", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "BrokenImport"}}, "severity": 8, "message": "could not import github.com/99designs/gqlgen/graphql/introspection (missing metadata for import of \"github.com/99designs/gqlgen/graphql/introspection\")", "source": "compiler", "startLineNumber": 23, "startColumn": 2, "endLineNumber": 23, "endColumn": 53}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "BrokenImport", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "BrokenImport"}}, "severity": 8, "message": "could not import github.com/golang-jwt/jwt/v5 (missing metadata for import of \"github.com/golang-jwt/jwt/v5\")", "source": "compiler", "startLineNumber": 24, "startColumn": 6, "endLineNumber": 24, "endColumn": 36}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 31, "startColumn": 17, "endLineNumber": 31, "endColumn": 19}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 39, "startColumn": 10, "endLineNumber": 39, "endColumn": 12}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 75, "startColumn": 19, "endLineNumber": 75, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 104, "startColumn": 11, "endLineNumber": 104, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 118, "startColumn": 13, "endLineNumber": 118, "endColumn": 15}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 128, "startColumn": 11, "endLineNumber": 128, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 142, "startColumn": 13, "endLineNumber": 142, "endColumn": 15}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 152, "startColumn": 11, "endLineNumber": 152, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 166, "startColumn": 13, "endLineNumber": 166, "endColumn": 15}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 176, "startColumn": 11, "endLineNumber": 176, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 190, "startColumn": 13, "endLineNumber": 190, "endColumn": 15}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 200, "startColumn": 11, "endLineNumber": 200, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 214, "startColumn": 13, "endLineNumber": 214, "endColumn": 15}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 224, "startColumn": 11, "endLineNumber": 224, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 254, "startColumn": 17, "endLineNumber": 254, "endColumn": 19}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 281, "startColumn": 11, "endLineNumber": 281, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 291, "startColumn": 20, "endLineNumber": 291, "endColumn": 22}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 310, "startColumn": 13, "endLineNumber": 310, "endColumn": 15}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 325, "startColumn": 11, "endLineNumber": 325, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 335, "startColumn": 10, "endLineNumber": 335, "endColumn": 12}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 346, "startColumn": 11, "endLineNumber": 346, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 374, "startColumn": 11, "endLineNumber": 374, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 392, "startColumn": 11, "endLineNumber": 392, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 406, "startColumn": 11, "endLineNumber": 406, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 420, "startColumn": 11, "endLineNumber": 420, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 434, "startColumn": 11, "endLineNumber": 434, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 448, "startColumn": 10, "endLineNumber": 448, "endColumn": 12}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 468, "startColumn": 13, "endLineNumber": 468, "endColumn": 15}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 631, "startColumn": 46, "endLineNumber": 631, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 695, "startColumn": 46, "endLineNumber": 695, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 763, "startColumn": 46, "endLineNumber": 763, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 793, "startColumn": 54, "endLineNumber": 793, "endColumn": 56}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 848, "startColumn": 62, "endLineNumber": 848, "endColumn": 64}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 918, "startColumn": 62, "endLineNumber": 918, "endColumn": 64}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 938, "startColumn": 54, "endLineNumber": 938, "endColumn": 56}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 976, "startColumn": 46, "endLineNumber": 976, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1168, "startColumn": 47, "endLineNumber": 1168, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1202, "startColumn": 47, "endLineNumber": 1202, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1270, "startColumn": 47, "endLineNumber": 1270, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1287, "startColumn": 63, "endLineNumber": 1287, "endColumn": 65}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.SubscriptionService undefined (type *mutationResolver has no field or method SubscriptionService)", "source": "compiler", "startLineNumber": 1337, "startColumn": 11, "endLineNumber": 1337, "endColumn": 30}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.SubscriptionService undefined (type *mutationResolver has no field or method SubscriptionService)", "source": "compiler", "startLineNumber": 1343, "startColumn": 25, "endLineNumber": 1343, "endColumn": 44}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1377, "startColumn": 63, "endLineNumber": 1377, "endColumn": 65}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1398, "startColumn": 57, "endLineNumber": 1398, "endColumn": 59}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1449, "startColumn": 57, "endLineNumber": 1449, "endColumn": 59}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1505, "startColumn": 57, "endLineNumber": 1505, "endColumn": 59}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1660, "startColumn": 20, "endLineNumber": 1660, "endColumn": 22}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1710, "startColumn": 11, "endLineNumber": 1710, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1719, "startColumn": 11, "endLineNumber": 1719, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1724, "startColumn": 11, "endLineNumber": 1724, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1729, "startColumn": 11, "endLineNumber": 1729, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1736, "startColumn": 20, "endLineNumber": 1736, "endColumn": 22}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1750, "startColumn": 22, "endLineNumber": 1750, "endColumn": 24}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1772, "startColumn": 18, "endLineNumber": 1772, "endColumn": 20}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1788, "startColumn": 47, "endLineNumber": 1788, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1842, "startColumn": 47, "endLineNumber": 1842, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1875, "startColumn": 47, "endLineNumber": 1875, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1901, "startColumn": 47, "endLineNumber": 1901, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1915, "startColumn": 63, "endLineNumber": 1915, "endColumn": 65}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1950, "startColumn": 63, "endLineNumber": 1950, "endColumn": 65}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1987, "startColumn": 63, "endLineNumber": 1987, "endColumn": 65}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2018, "startColumn": 57, "endLineNumber": 2018, "endColumn": 59}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2068, "startColumn": 11, "endLineNumber": 2068, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2088, "startColumn": 57, "endLineNumber": 2088, "endColumn": 59}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2118, "startColumn": 54, "endLineNumber": 2118, "endColumn": 56}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2155, "startColumn": 46, "endLineNumber": 2155, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2201, "startColumn": 46, "endLineNumber": 2201, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2280, "startColumn": 46, "endLineNumber": 2280, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2328, "startColumn": 62, "endLineNumber": 2328, "endColumn": 64}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2376, "startColumn": 62, "endLineNumber": 2376, "endColumn": 64}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2428, "startColumn": 46, "endLineNumber": 2428, "endColumn": 48}]