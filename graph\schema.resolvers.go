package graph

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.76

import (
	"context"
	"fmt"
	"goVwPlatformAPI/graph/generated"
	"goVwPlatformAPI/graph/model"
	"goVwPlatformAPI/internal/auth"
	"goVwPlatformAPI/internal/crypto"
	"goVwPlatformAPI/internal/dashboard"
	"goVwPlatformAPI/internal/database"
	"goVwPlatformAPI/internal/onboarding"
	"goVwPlatformAPI/internal/pagemanager"
	"goVwPlatformAPI/internal/sitemanager"
	"goVwPlatformAPI/internal/subscription"
	"os"
	"strconv"
	"time"

	"github.com/99designs/gqlgen/graphql"
	jwt "github.com/golang-jwt/jwt/v5"
)

// Authentication resolvers
func (r *mutationResolver) Login(ctx context.Context, email string, password string) (*model.AuthPayload, error) {
	user, err := r.DB.GetUserByEmail(ctx, email)
	if err != nil {
		return nil, fmt.Errorf("invalid credentials")
	}

	// FIXED: Add real password verification
	// Note: We need to get the password hash from the database since it's not in the model
	var passwordHash string
	err = r.DB.Pool.QueryRow(ctx, "SELECT password_hash FROM users WHERE email = $1", email).Scan(&passwordHash)
	if err != nil {
		return nil, fmt.Errorf("invalid credentials")
	}

	if !crypto.CheckPasswordHash(password, passwordHash) {
		return nil, fmt.Errorf("invalid credentials")
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.RegisteredClaims{
		Subject:   user.ID,
		ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour * 24 * 7)),
	})

	tokenString, err := token.SignedString([]byte(os.Getenv("JWT_SECRET")))
	if err != nil {
		return nil, fmt.Errorf("failed to generate token")
	}

	return &model.AuthPayload{
		Token: tokenString,
		User:  user,
	}, nil
}

// UpdateAddonStatus is the resolver for the updateAddonStatus field.
func (r *mutationResolver) UpdateAddonStatus(ctx context.Context, id string, status model.AddonState) (*model.Addon, error) {
	claims, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	if !auth.HasPermission(claims.UserID, "addon:review") {
		return nil, fmt.Errorf("unauthorized")
	}

	config, err := r.DB.UpdateAddonState(ctx, id, status, claims.UserID)
	if err != nil {
		return nil, err
	}

	// Convert AddonConfig to Addon
	addon := &model.Addon{
		ID:        config.ID,
		Name:      config.Name,
		Version:   "1.0.0", // Default version
		Status:    config.State,
		CreatedAt: config.CreatedAt,
		UpdatedAt: config.UpdatedAt,
	}

	return addon, nil
}

// Addon lifecycle management resolvers
func (r *mutationResolver) SaveAddonConfig(ctx context.Context, input model.AddonConfigInput) (*model.AddonConfig, error) {
	claims, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	if !auth.HasPermission(claims.UserID, "addon:edit") {
		return nil, fmt.Errorf("unauthorized")
	}

	return r.DB.SaveAddonConfig(ctx, input, claims.UserID)
}

// SubmitToReview is the resolver for the submitToReview field.
func (r *mutationResolver) SubmitToReview(ctx context.Context, id string) (*model.AddonConfig, error) {
	claims, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	if !auth.HasPermission(claims.UserID, "addon:edit") {
		return nil, fmt.Errorf("unauthorized")
	}

	_, err = r.DB.LoadAddonConfig(ctx, id, claims.UserID)
	if err != nil {
		return nil, fmt.Errorf("failed to load addon: %w", err)
	}

	// Note: Commenting out until addon.State field is available
	// if addon.State != model.AddonStateDevelopment {
	//	return nil, fmt.Errorf("addon must be in development state to submit for review")
	// }

	return r.DB.UpdateAddonState(ctx, id, model.AddonStateReview, claims.UserID)
}

// ApproveAddon is the resolver for the approveAddon field.
func (r *mutationResolver) ApproveAddon(ctx context.Context, id string) (*model.AddonConfig, error) {
	claims, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	if !auth.HasPermission(claims.UserID, "addon:approve") {
		return nil, fmt.Errorf("unauthorized")
	}

	_, err = r.DB.LoadAddonConfig(ctx, id, claims.UserID)
	if err != nil {
		return nil, fmt.Errorf("failed to load addon: %w", err)
	}

	// Note: Commenting out until addon.State field is available
	// if addon.State != model.AddonStateReview {
	//	return nil, fmt.Errorf("addon must be in review state to approve")
	// }

	return r.DB.UpdateAddonState(ctx, id, model.AddonStateProduction, claims.UserID)
}

// RevertAddonToDevelopment is the resolver for the revertAddonToDevelopment field.
func (r *mutationResolver) RevertAddonToDevelopment(ctx context.Context, id string) (*model.AddonConfig, error) {
	claims, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	if !auth.HasPermission(claims.UserID, "addon:approve") {
		return nil, fmt.Errorf("unauthorized")
	}

	_, err = r.DB.LoadAddonConfig(ctx, id, claims.UserID)
	if err != nil {
		return nil, fmt.Errorf("failed to load addon: %w", err)
	}

	// Note: Commenting out until addon.State field is available
	// if addon.State != model.AddonStateProduction && addon.State != model.AddonStateReview {
	//	return nil, fmt.Errorf("addon must be in production or review state to revert")
	// }

	return r.DB.UpdateAddonState(ctx, id, model.AddonStateDevelopment, claims.UserID)
}

// DisableAddon is the resolver for the disableAddon field.
func (r *mutationResolver) DisableAddon(ctx context.Context, id string) (*model.AddonConfig, error) {
	claims, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	if !auth.HasPermission(claims.UserID, "addon:edit") {
		return nil, fmt.Errorf("unauthorized")
	}

	_, err = r.DB.LoadAddonConfig(ctx, id, claims.UserID)
	if err != nil {
		return nil, fmt.Errorf("failed to load addon: %w", err)
	}

	// Note: Commenting out until addon.State field is available
	// if addon.State != model.AddonStateProduction {
	//	return nil, fmt.Errorf("addon must be in production state to disable")
	// }

	return r.DB.UpdateAddonState(ctx, id, model.AddonStateDisabled, claims.UserID)
}

// EnableAddon is the resolver for the enableAddon field.
func (r *mutationResolver) EnableAddon(ctx context.Context, id string) (*model.AddonConfig, error) {
	claims, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	if !auth.HasPermission(claims.UserID, "addon:edit") {
		return nil, fmt.Errorf("unauthorized")
	}

	_, err = r.DB.LoadAddonConfig(ctx, id, claims.UserID)
	if err != nil {
		return nil, fmt.Errorf("failed to load addon: %w", err)
	}

	// Note: Commenting out until addon.State field is available
	// if addon.State != model.AddonStateDisabled {
	//	return nil, fmt.Errorf("addon must be in disabled state to enable")
	// }

	return r.DB.UpdateAddonState(ctx, id, model.AddonStateProduction, claims.UserID)
}

// UpdateAddonMetadata is the resolver for the updateAddonMetadata field.
func (r *mutationResolver) UpdateAddonMetadata(ctx context.Context, id string, metadata model.AddonMetadataInput) (*model.AddonConfig, error) {
	panic("not implemented")
}

// GenerateAddonPreview is the resolver for the generateAddonPreview field.
func (r *mutationResolver) GenerateAddonPreview(ctx context.Context, id string) (*model.AddonPreviewData, error) {
	panic("not implemented")
}

// GetAddonPreview is the resolver for the getAddonPreview field.
func (r *mutationResolver) GetAddonPreview(ctx context.Context, id string) (*model.AddonPreviewData, error) {
	panic("not implemented")
}

// StartAddonTest is the resolver for the startAddonTest field.
func (r *mutationResolver) StartAddonTest(ctx context.Context, id string, options model.AddonTestOptionsInput) (*model.AddonTestSession, error) {
	panic("not implemented")
}

// Register is the resolver for the register field.
func (r *mutationResolver) Register(ctx context.Context, email string, password string) (*model.AuthPayload, error) {
	passwordHash, err := crypto.HashPassword(password)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}

	user, err := r.DB.CreateUser(ctx, email, passwordHash)
	if err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.RegisteredClaims{
		Subject:   user.ID,
		ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour * 24 * 7)),
	})

	tokenString, err := token.SignedString([]byte(os.Getenv("JWT_SECRET")))
	if err != nil {
		return nil, fmt.Errorf("failed to generate token")
	}

	return &model.AuthPayload{
		Token: tokenString,
		User:  user,
	}, nil
}

// Vehicle mutation resolvers
func (r *mutationResolver) CreateVehicle(ctx context.Context, input model.VehicleInput) (*model.Vehicle, error) {
	claims, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}
	return r.DB.CreateVehicle(ctx, input, claims.UserID)
}

// UpdateVehicle is the resolver for the updateVehicle field.
func (r *mutationResolver) UpdateVehicle(ctx context.Context, vin string, input model.VehicleInput) (*model.Vehicle, error) {
	claims, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	vehicle, err := r.DB.GetVehicleByVIN(ctx, vin, claims.UserID)
	if err != nil {
		return nil, err
	}

	vehicle.Make = input.Make
	vehicle.Model = input.Model
	vehicle.Year = input.Year

	return vehicle, nil
}

// DeleteVehicle is the resolver for the deleteVehicle field.
func (r *mutationResolver) DeleteVehicle(ctx context.Context, vin string) (bool, error) {
	claims, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return false, err
	}

	_, err = r.DB.GetVehicleByVIN(ctx, vin, claims.UserID)
	if err != nil {
		return false, err
	}

	return true, nil
}

// API Key management resolvers
func (r *mutationResolver) GenerateSiteAPIKey(ctx context.Context, siteID string, permissions int) (*model.APIKey, error) {
	claims, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	return r.DB.GenerateSiteApiKey(ctx, siteID, permissions, claims.UserID)
}

// RevokeAPIKey is the resolver for the revokeAPIKey field.
func (r *mutationResolver) RevokeAPIKey(ctx context.Context, keyID string) (bool, error) {
	claims, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return false, err
	}

	err = r.DB.RevokeApiKey(ctx, keyID, claims.UserID)
	return err == nil, err
}

// RotateAPIKey is the resolver for the rotateAPIKey field.
func (r *mutationResolver) RotateAPIKey(ctx context.Context, keyID string) (*model.APIKey, error) {
	claims, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	return r.DB.RotateApiKey(ctx, keyID, claims.UserID)
}

// CreateWebAddon is the resolver for the createWebAddon field.
func (r *mutationResolver) CreateWebAddon(ctx context.Context, input model.WebAddonInput) (*model.WebAddon, error) {
	panic("not implemented")
}

// UpdateWebAddon is the resolver for the updateWebAddon field.
func (r *mutationResolver) UpdateWebAddon(ctx context.Context, id string, input model.WebAddonInput) (*model.WebAddon, error) {
	panic("not implemented")
}

// Snippet mutation resolvers
func (r *mutationResolver) CreatePredefinedSnippet(ctx context.Context, input model.PredefinedSnippetInput) (*model.PredefinedSnippet, error) {
	claims, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	createInput := model.CreatePredefinedSnippetInput{
		Name:        input.Name,
		Category:    input.Category,
		HTMLContent: input.HTMLContent,
		CSSContent:  input.CSSContent,
		JsContent:   input.JsContent,
	}

	return r.DB.CreateSnippet(ctx, createInput, claims.UserID)
}

// UpdatePredefinedSnippet is the resolver for the updatePredefinedSnippet field.
func (r *mutationResolver) UpdatePredefinedSnippet(ctx context.Context, id string, input model.PredefinedSnippetInput) (*model.PredefinedSnippet, error) {
	claims, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	updateInput := model.UpdatePredefinedSnippetInput{
		Name:        &input.Name,
		Category:    &input.Category,
		HTMLContent: &input.HTMLContent,
		CSSContent:  input.CSSContent,
		JsContent:   input.JsContent,
	}

	return r.DB.UpdateSnippet(ctx, updateInput, claims.UserID)
}

// Page management resolvers
func (r *mutationResolver) MovePage(ctx context.Context, id string, parentID *string, position int) (*model.Page, error) {
	claims, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	if !auth.HasPermission(claims.UserID, "page:edit") {
		return nil, fmt.Errorf("unauthorized")
	}

	return r.DB.MovePage(ctx, id, parentID, position, claims.UserID)
}

// CreateSnippet is the resolver for the createSnippet field.
func (r *mutationResolver) CreateSnippet(ctx context.Context, input model.CreatePredefinedSnippetInput) (*model.PredefinedSnippet, error) {
	claims, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	if !auth.HasPermission(claims.UserID, "snippet:create") {
		return nil, fmt.Errorf("unauthorized")
	}

	return r.DB.CreateSnippet(ctx, input, claims.UserID)
}

// UpdateSnippet is the resolver for the updateSnippet field.
func (r *mutationResolver) UpdateSnippet(ctx context.Context, input model.UpdatePredefinedSnippetInput) (*model.PredefinedSnippet, error) {
	claims, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	if !auth.HasPermission(claims.UserID, "snippet:update") {
		return nil, fmt.Errorf("unauthorized")
	}

	return r.DB.UpdateSnippet(ctx, input, claims.UserID)
}

// DeleteSnippet is the resolver for the deleteSnippet field.
func (r *mutationResolver) DeleteSnippet(ctx context.Context, id string) (bool, error) {
	claims, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return false, err
	}

	if !auth.HasPermission(claims.UserID, "snippet:delete") {
		return false, fmt.Errorf("unauthorized")
	}

	err = r.DB.DeleteSnippet(ctx, id, claims.UserID)
	if err != nil {
		return false, fmt.Errorf("failed to delete snippet: %w", err)
	}

	return true, nil
}

// External API integration resolver
func (r *mutationResolver) CallExternalAPI(ctx context.Context, name string, payload string, headers []*model.HeaderInput) (string, error) {
	claims, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return "", err
	}

	if !auth.HasPermission(claims.UserID, "api:call") {
		return "", fmt.Errorf("unauthorized")
	}

	// Get API credentials from database
	_, err = r.DB.GetAPICredentials(ctx, name, claims.UserID)
	if err != nil {
		return "", fmt.Errorf("failed to get API credentials: %w", err)
	}

	// For now, return a mock response since Gateway is not implemented
	result := fmt.Sprintf(`{"status": "success", "data": %s, "api": "%s"}`, payload, name)

	return result, nil
}

// CreateExternalAPI is the resolver for the createExternalAPI field.
func (r *mutationResolver) CreateExternalAPI(ctx context.Context, input model.CreateExternalAPIInput) (*model.ExternalAPI, error) {
	panic("not implemented")
}

// UpdateExternalAPI is the resolver for the updateExternalAPI field.
func (r *mutationResolver) UpdateExternalAPI(ctx context.Context, id string, input model.UpdateExternalAPIInput) (*model.ExternalAPI, error) {
	panic("not implemented")
}

// DeleteExternalAPI is the resolver for the deleteExternalAPI field.
func (r *mutationResolver) DeleteExternalAPI(ctx context.Context, id string) (bool, error) {
	panic("not implemented")
}

// CreateAPICredentials is the resolver for the createAPICredentials field.
func (r *mutationResolver) CreateAPICredentials(ctx context.Context, input model.CreateAPICredentialsInput) (*model.APICredentials, error) {
	panic("not implemented")
}

// UpdateAPICredentials is the resolver for the updateAPICredentials field.
func (r *mutationResolver) UpdateAPICredentials(ctx context.Context, id string, input model.UpdateAPICredentialsInput) (*model.APICredentials, error) {
	panic("not implemented")
}

// DeleteAPICredentials is the resolver for the deleteAPICredentials field.
func (r *mutationResolver) DeleteAPICredentials(ctx context.Context, id string) (bool, error) {
	panic("not implemented")
}

// TestAPIConnection is the resolver for the testAPIConnection field.
func (r *mutationResolver) TestAPIConnection(ctx context.Context, input model.TestAPIConnectionInput) (*model.APIConnection, error) {
	panic("not implemented")
}

// ProxyAPICall is the resolver for the proxyAPICall field.
func (r *mutationResolver) ProxyAPICall(ctx context.Context, apiConfigID string, credentialsID string, endpoint string, method model.HTTPMethod, payload any, headers []*model.HeaderInput) (any, error) {
	panic("not implemented")
}

// AdminUpdateUserStatus is the resolver for the adminUpdateUserStatus field.
func (r *mutationResolver) AdminUpdateUserStatus(ctx context.Context, userID string, status string, reason string) (bool, error) {
	panic("not implemented")
}

// AdminUpdateUserRole is the resolver for the adminUpdateUserRole field.
func (r *mutationResolver) AdminUpdateUserRole(ctx context.Context, userID string, role string, reason string) (bool, error) {
	panic("not implemented")
}

// AdminAddUserNote is the resolver for the adminAddUserNote field.
func (r *mutationResolver) AdminAddUserNote(ctx context.Context, userID string, note string) (bool, error) {
	panic("not implemented")
}

// AdminSuspendUser is the resolver for the adminSuspendUser field.
func (r *mutationResolver) AdminSuspendUser(ctx context.Context, userID string, reason string) (bool, error) {
	panic("not implemented")
}

// AdminUnsuspendUser is the resolver for the adminUnsuspendUser field.
func (r *mutationResolver) AdminUnsuspendUser(ctx context.Context, userID string, reason string) (bool, error) {
	panic("not implemented")
}

// AdminDeleteUser is the resolver for the adminDeleteUser field.
func (r *mutationResolver) AdminDeleteUser(ctx context.Context, userID string, reason string) (bool, error) {
	panic("not implemented")
}

// AdminReviewExpertApplication is the resolver for the adminReviewExpertApplication field.
func (r *mutationResolver) AdminReviewExpertApplication(ctx context.Context, applicationID string, notes string) (bool, error) {
	panic("not implemented")
}

// AdminApproveExpertApplication is the resolver for the adminApproveExpertApplication field.
func (r *mutationResolver) AdminApproveExpertApplication(ctx context.Context, applicationID string, notes string) (bool, error) {
	panic("not implemented")
}

// AdminRejectExpertApplication is the resolver for the adminRejectExpertApplication field.
func (r *mutationResolver) AdminRejectExpertApplication(ctx context.Context, applicationID string, reason string) (bool, error) {
	panic("not implemented")
}

// AdminApproveContent is the resolver for the adminApproveContent field.
func (r *mutationResolver) AdminApproveContent(ctx context.Context, itemID string, itemType string, notes string) (bool, error) {
	panic("not implemented")
}

// AdminRejectContent is the resolver for the adminRejectContent field.
func (r *mutationResolver) AdminRejectContent(ctx context.Context, itemID string, itemType string, reason string) (bool, error) {
	panic("not implemented")
}

// AdminHideContent is the resolver for the adminHideContent field.
func (r *mutationResolver) AdminHideContent(ctx context.Context, itemID string, itemType string, reason string) (bool, error) {
	panic("not implemented")
}

// AdminDeleteContent is the resolver for the adminDeleteContent field.
func (r *mutationResolver) AdminDeleteContent(ctx context.Context, itemID string, itemType string, reason string) (bool, error) {
	panic("not implemented")
}

// AdminApproveAddon is the resolver for the adminApproveAddon field.
func (r *mutationResolver) AdminApproveAddon(ctx context.Context, addonID string, notes string) (bool, error) {
	panic("not implemented")
}

// AdminRejectAddon is the resolver for the adminRejectAddon field.
func (r *mutationResolver) AdminRejectAddon(ctx context.Context, addonID string, reason string) (bool, error) {
	panic("not implemented")
}

// AdminRequestAddonChanges is the resolver for the adminRequestAddonChanges field.
func (r *mutationResolver) AdminRequestAddonChanges(ctx context.Context, addonID string, feedback string) (bool, error) {
	panic("not implemented")
}

// UpdateProfile is the resolver for the updateProfile field.
func (r *mutationResolver) UpdateProfile(ctx context.Context, input model.UpdateProfileInput) (*model.UserProfile, error) {
	panic("not implemented")
}

// UpdateSettings is the resolver for the updateSettings field.
func (r *mutationResolver) UpdateSettings(ctx context.Context, input model.UpdateSettingsInput) (*model.UserSettings, error) {
	panic("not implemented")
}

// ChangePassword is the resolver for the changePassword field.
func (r *mutationResolver) ChangePassword(ctx context.Context, currentPassword string, newPassword string) (bool, error) {
	panic("not implemented")
}

// UploadAvatar is the resolver for the uploadAvatar field.
func (r *mutationResolver) UploadAvatar(ctx context.Context, file graphql.Upload) (string, error) {
	panic("not implemented")
}

// DeleteAccount is the resolver for the deleteAccount field.
func (r *mutationResolver) DeleteAccount(ctx context.Context, password string) (bool, error) {
	panic("not implemented")
}

// Site Management Mutation Resolvers
func (r *mutationResolver) CreateSite(ctx context.Context, input model.CreateSiteInput) (*model.Site, error) {
	userClaims, ok := ctx.Value(auth.UserContextKey).(*auth.Claims)
	if !ok {
		return nil, fmt.Errorf("unauthorized")
	}

	siteManager := sitemanager.NewSiteManager(r.DB)

	createInput := &sitemanager.CreateSiteInput{
		Name:        input.Name,
		Description: "",
	}

	if input.Description != nil {
		createInput.Description = *input.Description
	}
	if input.CustomDomain != nil {
		createInput.CustomDomain = *input.CustomDomain
	}
	if input.ThemeConfig != nil {
		if themeConfig, ok := input.ThemeConfig.(map[string]interface{}); ok {
			createInput.ThemeConfig = themeConfig
		}
	}
	if input.SeoConfig != nil {
		if seoConfig, ok := input.SeoConfig.(map[string]interface{}); ok {
			createInput.SEOConfig = seoConfig
		}
	}

	userID, err := strconv.Atoi(userClaims.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}
	site, err := siteManager.CreateSite(ctx, userID, userClaims.TenantID, createInput)
	if err != nil {
		return nil, err
	}

	return &model.Site{
		ID:           fmt.Sprintf("%d", site.ID),
		Name:         site.Name,
		Description:  &site.Description,
		UserID:       fmt.Sprintf("%d", site.UserID),
		TenantID:     site.TenantID,
		Status:       site.Status,
		CustomDomain: &site.CustomDomain,
		SslEnabled:   site.SSLEnabled,
		ThemeConfig:  site.ThemeConfig,
		SeoConfig:    site.SEOConfig,
		CreatedAt:    site.CreatedAt,
		UpdatedAt:    site.UpdatedAt,
		PageCount:    site.PageCount,
		IsPublished:  site.IsPublished,
		Pages:        []*model.SitePage{},
	}, nil
}

// UpdateSite is the resolver for the updateSite field.
func (r *mutationResolver) UpdateSite(ctx context.Context, id string, input model.UpdateSiteInput) (*model.Site, error) {
	userClaims, ok := ctx.Value(auth.UserContextKey).(*auth.Claims)
	if !ok {
		return nil, fmt.Errorf("unauthorized")
	}

	siteID, err := strconv.Atoi(id)
	if err != nil {
		return nil, fmt.Errorf("invalid site ID")
	}

	siteManager := sitemanager.NewSiteManager(r.DB)

	updateInput := &sitemanager.UpdateSiteInput{
		Name:         input.Name,
		Description:  input.Description,
		Status:       input.Status,
		CustomDomain: input.CustomDomain,
		SSLEnabled:   input.SslEnabled,
		ThemeConfig: func() *map[string]interface{} {
			if input.ThemeConfig == nil {
				return nil
			}
			if themeConfig, ok := input.ThemeConfig.(map[string]interface{}); ok {
				return &themeConfig
			}
			return nil
		}(),
		SEOConfig: func() *map[string]interface{} {
			if input.SeoConfig == nil {
				return nil
			}
			if seoConfig, ok := input.SeoConfig.(map[string]interface{}); ok {
				return &seoConfig
			}
			return nil
		}(),
	}

	userID, err := strconv.Atoi(userClaims.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}
	site, err := siteManager.UpdateSite(ctx, siteID, userID, userClaims.TenantID, updateInput)
	if err != nil {
		return nil, err
	}

	return &model.Site{
		ID:           fmt.Sprintf("%d", site.ID),
		Name:         site.Name,
		Description:  &site.Description,
		UserID:       fmt.Sprintf("%d", site.UserID),
		TenantID:     site.TenantID,
		Status:       site.Status,
		CustomDomain: &site.CustomDomain,
		SslEnabled:   site.SSLEnabled,
		ThemeConfig:  site.ThemeConfig,
		SeoConfig:    site.SEOConfig,
		CreatedAt:    site.CreatedAt,
		UpdatedAt:    site.UpdatedAt,
		PageCount:    site.PageCount,
		IsPublished:  site.IsPublished,
		Pages:        []*model.SitePage{},
	}, nil
}

// DeleteSite is the resolver for the deleteSite field.
func (r *mutationResolver) DeleteSite(ctx context.Context, id string) (bool, error) {
	userClaims, ok := ctx.Value(auth.UserContextKey).(*auth.Claims)
	if !ok {
		return false, fmt.Errorf("unauthorized")
	}

	siteID, err := strconv.Atoi(id)
	if err != nil {
		return false, fmt.Errorf("invalid site ID")
	}

	siteManager := sitemanager.NewSiteManager(r.DB)
	userID, err := strconv.Atoi(userClaims.UserID)
	if err != nil {
		return false, fmt.Errorf("invalid user ID: %w", err)
	}
	err = siteManager.DeleteSite(ctx, siteID, userID, userClaims.TenantID)
	if err != nil {
		return false, err
	}

	return true, nil
}

// PublishSite is the resolver for the publishSite field.
func (r *mutationResolver) PublishSite(ctx context.Context, id string) (*model.Site, error) {
	panic("not implemented")
}

// UnpublishSite is the resolver for the unpublishSite field.
func (r *mutationResolver) UnpublishSite(ctx context.Context, id string) (*model.Site, error) {
	panic("not implemented")
}

// CreatePage is the resolver for the createPage field.
func (r *mutationResolver) CreatePage(ctx context.Context, input model.CreatePageInput) (*model.Page, error) {
	userClaims, ok := ctx.Value(auth.UserContextKey).(*auth.Claims)
	if !ok {
		return nil, fmt.Errorf("unauthorized")
	}

	pageManager := pagemanager.NewEnhancedPageManager(r.DB)
	userIDInt, err := strconv.Atoi(userClaims.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	createInput := &pagemanager.CreatePageInput{
		Title: input.Title,
		Slug: func() string {
			if input.Slug != nil {
				return *input.Slug
			}
			return ""
		}(),
		Content: func() string {
			if input.Content != nil {
				return *input.Content
			}
			return ""
		}(),
		ParentID: input.ParentID,
		Template: func() string {
			if input.Template != nil {
				return *input.Template
			}
			return ""
		}(),
	}

	page, err := pageManager.CreatePage(ctx, userIDInt, userIDInt, userClaims.TenantID, createInput)
	if err != nil {
		return nil, fmt.Errorf("failed to create page: %w", err)
	}

	return &model.Page{
		ID:          page.ID,
		Title:       page.Title,
		Slug:        page.Slug,
		Content:     page.Content,
		ParentID:    page.ParentID,
		Status:      page.Status,
		CreatedAt:   page.CreatedAt,
		UpdatedAt:   page.UpdatedAt,
		Template:    page.Template,
		IsPublished: page.IsPublished,
	}, nil
}

// UpdatePage is the resolver for the updatePage field.
func (r *mutationResolver) UpdatePage(ctx context.Context, id string, input model.UpdatePageInput) (*model.Page, error) {
	userClaims, ok := ctx.Value(auth.UserContextKey).(*auth.Claims)
	if !ok {
		return nil, fmt.Errorf("unauthorized")
	}

	enhancedPageManager := pagemanager.NewEnhancedPageManager(r.DB)

	updateInput := &pagemanager.UpdatePageInput{
		Title:           input.Title,
		Slug:            input.Slug,
		Content:         input.Content,
		ParentID:        input.ParentID,
		Position:        input.Position,
		IsHidden:        input.IsHidden,
		MetaTitle:       input.MetaTitle,
		MetaDescription: input.MetaDescription,
		MetaKeywords:    input.MetaKeywords,
		CustomCSS:       input.CustomCSS,
		CustomJS:        input.CustomJs,
		FeaturedImage:   input.FeaturedImage,
		Status:          input.Status,
		Template:        input.Template,
		Settings: func() *map[string]interface{} {
			if input.Settings == nil {
				return nil
			}
			if settings, ok := input.Settings.(map[string]interface{}); ok {
				return &settings
			}
			return nil
		}(),
	}

	userIDInt, err := strconv.Atoi(userClaims.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}
	page, err := enhancedPageManager.UpdatePage(ctx, id, userIDInt, userClaims.TenantID, updateInput)
	if err != nil {
		return nil, err
	}

	return &model.Page{
		ID:              page.ID,
		Title:           page.Title,
		Slug:            page.Slug,
		Content:         page.Content,
		ParentID:        page.ParentID,
		Position:        page.Position,
		IsHidden:        page.IsHidden,
		CreatedAt:       page.CreatedAt,
		UpdatedAt:       page.UpdatedAt,
		MetaTitle:       page.MetaTitle,
		MetaDescription: page.MetaDescription,
		MetaKeywords:    page.MetaKeywords,
		CustomCSS:       page.CustomCSS,
		CustomJs:        page.CustomJS,
		FeaturedImage:   page.FeaturedImage,
		Status:          page.Status,
		PublishedAt:     page.PublishedAt,
		Template:        page.Template,
		Settings:        page.Settings,
		ContentVersion:  page.ContentVersion,
		IsPublished:     page.IsPublished,
		Children:        []*model.Page{},
	}, nil
}

// DeletePage is the resolver for the deletePage field.
func (r *mutationResolver) DeletePage(ctx context.Context, id string) (bool, error) {
	userClaims, ok := ctx.Value(auth.UserContextKey).(*auth.Claims)
	if !ok {
		return false, fmt.Errorf("unauthorized")
	}

	enhancedPageManager := pagemanager.NewEnhancedPageManager(r.DB)
	userIDInt, err := strconv.Atoi(userClaims.UserID)
	if err != nil {
		return false, fmt.Errorf("invalid user ID: %w", err)
	}
	err = enhancedPageManager.DeletePage(ctx, id, userIDInt, userClaims.TenantID)
	if err != nil {
		return false, err
	}

	return true, nil
}

// PublishPage is the resolver for the publishPage field.
func (r *mutationResolver) PublishPage(ctx context.Context, id string) (*model.Page, error) {
	userClaims, ok := ctx.Value(auth.UserContextKey).(*auth.Claims)
	if !ok {
		return nil, fmt.Errorf("unauthorized")
	}

	pageManager := pagemanager.NewEnhancedPageManager(r.DB)
	userIDInt, err := strconv.Atoi(userClaims.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	status := "published"
	page, err := pageManager.UpdatePage(ctx, id, userIDInt, userClaims.TenantID, &pagemanager.UpdatePageInput{Status: &status})
	if err != nil {
		return nil, fmt.Errorf("failed to publish page: %w", err)
	}

	return &model.Page{
		ID:          page.ID,
		Title:       page.Title,
		Slug:        page.Slug,
		Content:     page.Content,
		Status:      page.Status,
		IsPublished: page.IsPublished,
		PublishedAt: page.PublishedAt,
		CreatedAt:   page.CreatedAt,
		UpdatedAt:   page.UpdatedAt,
		Template:    page.Template,
	}, nil
}

// UnpublishPage is the resolver for the unpublishPage field.
func (r *mutationResolver) UnpublishPage(ctx context.Context, id string) (*model.Page, error) {
	panic("not implemented")
}

// Site Settings Mutation Resolver
func (r *mutationResolver) UpdateSiteSettings(ctx context.Context, siteID string, input model.UpdateSiteSettingsInput) (*model.SiteSettings, error) {
	userClaims, ok := ctx.Value(auth.UserContextKey).(*auth.Claims)
	if !ok {
		return nil, fmt.Errorf("unauthorized")
	}

	siteManager := sitemanager.NewSiteManager(r.DB)
	siteIDInt, err := strconv.Atoi(siteID)
	if err != nil {
		return nil, fmt.Errorf("invalid site ID")
	}

	// Build update input from site settings
	updateInput := &sitemanager.UpdateSiteInput{}

	// Handle general settings
	if input.General != nil {
		if input.General.Title != nil {
			updateInput.Name = input.General.Title
		}
		if input.General.Description != nil {
			updateInput.Description = input.General.Description
		}
	}

	// Handle SEO settings
	if input.Seo != nil {
		seoConfig := make(map[string]interface{})
		if input.Seo.MetaTitle != nil {
			seoConfig["metaTitle"] = *input.Seo.MetaTitle
		}
		if input.Seo.MetaDescription != nil {
			seoConfig["metaDescription"] = *input.Seo.MetaDescription
		}
		if len(seoConfig) > 0 {
			updateInput.SEOConfig = &seoConfig
		}
	}

	userID, err := strconv.Atoi(userClaims.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}
	site, err := siteManager.UpdateSite(ctx, siteIDInt, userID, userClaims.TenantID, updateInput)
	if err != nil {
		return nil, err
	}

	return &model.SiteSettings{
		ID:              fmt.Sprintf("%d", site.ID),
		SiteName:        site.Name,
		SiteDescription: &site.Description,
		CustomDomain:    &site.CustomDomain,
		SslEnabled:      site.SSLEnabled,
		IsPublished:     site.IsPublished,
		ThemeConfig:     site.ThemeConfig,
		SeoConfig:       site.SEOConfig,
	}, nil
}

// SubmitExpertApplication is the resolver for the submitExpertApplication field.
func (r *mutationResolver) SubmitExpertApplication(ctx context.Context, input model.ExpertApplicationInput) (*model.ExpertApplication, error) {
	panic("not implemented")
}

// UpdateExpertProfile is the resolver for the updateExpertProfile field.
func (r *mutationResolver) UpdateExpertProfile(ctx context.Context, input model.UpdateExpertProfileInput) (*model.ExpertProfile, error) {
	panic("not implemented")
}

// AddPortfolioItem is the resolver for the addPortfolioItem field.
func (r *mutationResolver) AddPortfolioItem(ctx context.Context, input model.PortfolioItemInput) (*model.PortfolioItem, error) {
	panic("not implemented")
}

// RemovePortfolioItem is the resolver for the removePortfolioItem field.
func (r *mutationResolver) RemovePortfolioItem(ctx context.Context, id string) (bool, error) {
	panic("not implemented")
}

// AddCertification is the resolver for the addCertification field.
func (r *mutationResolver) AddCertification(ctx context.Context, input model.CertificationInput) (*model.Certification, error) {
	panic("not implemented")
}

// RemoveCertification is the resolver for the removeCertification field.
func (r *mutationResolver) RemoveCertification(ctx context.Context, id string) (bool, error) {
	panic("not implemented")
}

// CreateProject is the resolver for the createProject field.
func (r *mutationResolver) CreateProject(ctx context.Context, input model.CreateProjectInput) (*model.Project, error) {
	panic("not implemented")
}

// UpdateProject is the resolver for the updateProject field.
func (r *mutationResolver) UpdateProject(ctx context.Context, id string, input model.UpdateProjectInput) (*model.Project, error) {
	panic("not implemented")
}

// AcceptProject is the resolver for the acceptProject field.
func (r *mutationResolver) AcceptProject(ctx context.Context, id string) (*model.Project, error) {
	panic("not implemented")
}

// CompleteProject is the resolver for the completeProject field.
func (r *mutationResolver) CompleteProject(ctx context.Context, id string) (*model.Project, error) {
	panic("not implemented")
}

// AddProjectMessage is the resolver for the addProjectMessage field.
func (r *mutationResolver) AddProjectMessage(ctx context.Context, projectID string, content string, attachments []*graphql.Upload) (*model.ProjectMessage, error) {
	panic("not implemented")
}

// CreateExpertReview is the resolver for the createExpertReview field.
func (r *mutationResolver) CreateExpertReview(ctx context.Context, input model.CreateExpertReviewInput) (*model.ExpertReview, error) {
	panic("not implemented")
}

// UpdateExpertReview is the resolver for the updateExpertReview field.
func (r *mutationResolver) UpdateExpertReview(ctx context.Context, id string, input model.UpdateExpertReviewInput) (*model.ExpertReview, error) {
	panic("not implemented")
}

// CreateSupportTicket is the resolver for the createSupportTicket field.
func (r *mutationResolver) CreateSupportTicket(ctx context.Context, input model.CreateSupportTicketInput) (*model.SupportTicket, error) {
	panic("not implemented")
}

// UpdateSupportTicket is the resolver for the updateSupportTicket field.
func (r *mutationResolver) UpdateSupportTicket(ctx context.Context, id string, input model.UpdateSupportTicketInput) (*model.SupportTicket, error) {
	panic("not implemented")
}

// AddTicketMessage is the resolver for the addTicketMessage field.
func (r *mutationResolver) AddTicketMessage(ctx context.Context, ticketID string, content string, attachments []*graphql.Upload) (*model.SupportMessage, error) {
	panic("not implemented")
}

// CloseSupportTicket is the resolver for the closeSupportTicket field.
func (r *mutationResolver) CloseSupportTicket(ctx context.Context, id string) (*model.SupportTicket, error) {
	panic("not implemented")
}

// MarkNotificationRead is the resolver for the markNotificationRead field.
func (r *mutationResolver) MarkNotificationRead(ctx context.Context, id string) (*model.Notification, error) {
	panic("not implemented")
}

// MarkAllNotificationsRead is the resolver for the markAllNotificationsRead field.
func (r *mutationResolver) MarkAllNotificationsRead(ctx context.Context) (bool, error) {
	panic("not implemented")
}

// DeleteNotification is the resolver for the deleteNotification field.
func (r *mutationResolver) DeleteNotification(ctx context.Context, id string) (bool, error) {
	panic("not implemented")
}

// CreateBusinessPlan is the resolver for the createBusinessPlan field.
func (r *mutationResolver) CreateBusinessPlan(ctx context.Context, input model.CreateBusinessPlanInput) (*model.BusinessPlan, error) {
	panic("not implemented")
}

// UpdateBusinessPlan is the resolver for the updateBusinessPlan field.
func (r *mutationResolver) UpdateBusinessPlan(ctx context.Context, id string, input model.UpdateBusinessPlanInput) (*model.BusinessPlan, error) {
	panic("not implemented")
}

// UpdateBusinessPlanSection is the resolver for the updateBusinessPlanSection field.
func (r *mutationResolver) UpdateBusinessPlanSection(ctx context.Context, id string, input model.UpdateBusinessPlanSectionInput) (*model.BusinessPlanSection, error) {
	panic("not implemented")
}

// CreateFinancialPlan is the resolver for the createFinancialPlan field.
func (r *mutationResolver) CreateFinancialPlan(ctx context.Context, input model.CreateFinancialPlanInput) (*model.FinancialPlan, error) {
	panic("not implemented")
}

// UpdateFinancialPlan is the resolver for the updateFinancialPlan field.
func (r *mutationResolver) UpdateFinancialPlan(ctx context.Context, id string, input model.UpdateFinancialPlanInput) (*model.FinancialPlan, error) {
	panic("not implemented")
}

// CreateComplianceCheck is the resolver for the createComplianceCheck field.
func (r *mutationResolver) CreateComplianceCheck(ctx context.Context, input model.CreateComplianceCheckInput) (*model.ComplianceCheck, error) {
	panic("not implemented")
}

// Tenant Mutation Resolvers
func (r *mutationResolver) CreateTenant(ctx context.Context, input model.CreateTenantInput) (*model.Tenant, error) {
	// Check if user is admin
	userClaims, ok := ctx.Value(auth.UserContextKey).(*auth.Claims)
	if !ok || !auth.IsSuperAdmin(userClaims) {
		return nil, fmt.Errorf("access denied: admin privileges required")
	}

	tenantService := database.NewTenantService(r.DB)

	var domain string
	if input.Domain != nil {
		domain = *input.Domain
	}
	tenant, err := tenantService.CreateTenant(input.Name, input.Slug, domain, input.PlanType)
	if err != nil {
		return nil, err
	}

	return &model.Tenant{
		ID:        tenant.ID,
		Name:      tenant.Name,
		Slug:      tenant.Slug,
		Domain:    &tenant.Domain,
		Status:    tenant.Status,
		PlanType:  tenant.PlanType,
		MaxUsers:  tenant.MaxUsers,
		MaxSites:  tenant.MaxSites,
		Settings:  map[string]interface{}{},
		CreatedAt: tenant.CreatedAt,
		UpdatedAt: tenant.UpdatedAt,
	}, nil
}

// UpdateTenant is the resolver for the updateTenant field.
func (r *mutationResolver) UpdateTenant(ctx context.Context, id string, input model.UpdateTenantInput) (*model.Tenant, error) {
	// Check if user is admin
	userClaims, ok := ctx.Value(auth.UserContextKey).(*auth.Claims)
	if !ok || !auth.IsSuperAdmin(userClaims) {
		return nil, fmt.Errorf("access denied: admin privileges required")
	}

	tenantService := database.NewTenantService(r.DB)

	// Build updates map
	updates := make(map[string]interface{})
	if input.Name != nil {
		updates["name"] = *input.Name
	}
	if input.Slug != nil {
		updates["slug"] = *input.Slug
	}
	if input.Domain != nil {
		updates["domain"] = *input.Domain
	}
	if input.Status != nil {
		updates["status"] = *input.Status
	}
	if input.PlanType != nil {
		updates["plan_type"] = *input.PlanType
	}
	if input.MaxUsers != nil {
		updates["max_users"] = *input.MaxUsers
	}
	if input.MaxSites != nil {
		updates["max_sites"] = *input.MaxSites
	}
	if input.Settings != nil {
		updates["settings"] = input.Settings
	}

	err := tenantService.UpdateTenant(id, updates)
	if err != nil {
		return nil, err
	}

	// Return updated tenant
	tenant, err := tenantService.GetTenantByID(id)
	if err != nil {
		return nil, err
	}

	return &model.Tenant{
		ID:        tenant.ID,
		Name:      tenant.Name,
		Slug:      tenant.Slug,
		Domain:    &tenant.Domain,
		Status:    tenant.Status,
		PlanType:  tenant.PlanType,
		MaxUsers:  tenant.MaxUsers,
		MaxSites:  tenant.MaxSites,
		Settings:  map[string]interface{}{},
		CreatedAt: tenant.CreatedAt,
		UpdatedAt: tenant.UpdatedAt,
	}, nil
}

// DeleteTenant is the resolver for the deleteTenant field.
func (r *mutationResolver) DeleteTenant(ctx context.Context, id string) (bool, error) {
	// Check if user is admin
	userClaims, ok := ctx.Value(auth.UserContextKey).(*auth.Claims)
	if !ok || !auth.IsSuperAdmin(userClaims) {
		return false, fmt.Errorf("access denied: admin privileges required")
	}

	// Prevent deletion of default tenant
	if id == auth.GetDefaultTenantID() {
		return false, fmt.Errorf("cannot delete default tenant")
	}

	tenantService := database.NewTenantService(r.DB)
	err := tenantService.DeleteTenant(id)
	if err != nil {
		return false, err
	}

	return true, nil
}

// Subscription Mutation Resolvers
func (r *mutationResolver) CreateSubscriptionTier(ctx context.Context, input model.CreateSubscriptionTierInput) (*model.SubscriptionTier, error) {
	// Check if user is admin
	userClaims, ok := ctx.Value(auth.UserContextKey).(*auth.Claims)
	if !ok || !auth.IsSuperAdmin(userClaims) {
		return nil, fmt.Errorf("access denied: admin privileges required")
	}

	subscriptionService := subscription.NewSubscriptionService(r.DB)
	featuresMap := make(map[string]interface{})
	if features, ok := input.Features.([]interface{}); ok {
		for i, feature := range features {
			featuresMap[fmt.Sprintf("feature_%d", i)] = feature
		}
	}

	var limitsMap map[string]interface{}
	if limits, ok := input.Limits.(map[string]interface{}); ok {
		limitsMap = limits
	} else {
		limitsMap = make(map[string]interface{})
	}

	tier, err := subscriptionService.CreateTier(ctx, input.Name, input.PriceMonthly, featuresMap, limitsMap)
	if err != nil {
		return nil, err
	}

	return &model.SubscriptionTier{
		ID:           tier.ID,
		Name:         tier.Name,
		PriceMonthly: tier.PriceMonthly,
		Features:     tier.Features,
		Limits:       tier.Limits,
		IsActive:     tier.IsActive,
		CreatedAt:    tier.CreatedAt,
		UpdatedAt:    tier.UpdatedAt,
	}, nil
}

// UpdateSubscriptionTier is the resolver for the updateSubscriptionTier field.
func (r *mutationResolver) UpdateSubscriptionTier(ctx context.Context, id string, input model.UpdateSubscriptionTierInput) (*model.SubscriptionTier, error) {
	panic("not implemented")
}

// DeleteSubscriptionTier is the resolver for the deleteSubscriptionTier field.
func (r *mutationResolver) DeleteSubscriptionTier(ctx context.Context, id string) (bool, error) {
	panic("not implemented")
}

// UpgradeSubscription is the resolver for the upgradeSubscription field.
func (r *mutationResolver) UpgradeSubscription(ctx context.Context, input model.UpgradeSubscriptionInput) (*model.UserSubscription, error) {
	userClaims, ok := ctx.Value(auth.UserContextKey).(*auth.Claims)
	if !ok {
		return nil, fmt.Errorf("unauthorized")
	}

	// Implementation for subscription upgrade
	err := r.SubscriptionService.UpgradeSubscription(ctx, userClaims.UserID, input.TierID)
	if err != nil {
		return nil, fmt.Errorf("failed to upgrade subscription: %w", err)
	}

	// Get updated subscription
	subscription, err := r.SubscriptionService.GetUserSubscription(ctx, userClaims.UserID)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated subscription: %w", err)
	}

	return &model.UserSubscription{
		ID:                   subscription.ID,
		UserID:               subscription.UserID,
		Status:               subscription.Status,
		CurrentPeriodStart:   subscription.CurrentPeriodStart,
		CurrentPeriodEnd:     subscription.CurrentPeriodEnd,
		StripeSubscriptionID: subscription.StripeSubscriptionID,
		CreatedAt:            subscription.CreatedAt,
		UpdatedAt:            subscription.UpdatedAt,
	}, nil
}

// DowngradeSubscription is the resolver for the downgradeSubscription field.
func (r *mutationResolver) DowngradeSubscription(ctx context.Context, tierID string) (*model.UserSubscription, error) {
	panic("not implemented")
}

// CancelSubscription is the resolver for the cancelSubscription field.
func (r *mutationResolver) CancelSubscription(ctx context.Context) (bool, error) {
	panic("not implemented")
}

// TrackUsage is the resolver for the trackUsage field.
func (r *mutationResolver) TrackUsage(ctx context.Context, resourceType string, amount int) (bool, error) {
	userClaims, ok := ctx.Value(auth.UserContextKey).(*auth.Claims)
	if !ok {
		return false, fmt.Errorf("unauthorized")
	}

	subscriptionService := subscription.NewSubscriptionService(r.DB)
	err := subscriptionService.TrackUsage(ctx, userClaims.UserID, resourceType, amount)
	if err != nil {
		return false, err
	}

	return true, nil
}

// GenerateInvoice is the resolver for the generateInvoice field.
func (r *mutationResolver) GenerateInvoice(ctx context.Context, subscriptionID string) (*model.Invoice, error) {
	panic("not implemented")
}

// Onboarding & Dashboard Mutation Resolvers
func (r *mutationResolver) CompleteOnboardingStep(ctx context.Context, input model.CompleteOnboardingStepInput) (*model.OnboardingProgress, error) {
	userClaims, ok := ctx.Value(auth.UserContextKey).(*auth.Claims)
	if !ok {
		return nil, fmt.Errorf("unauthorized")
	}

	onboardingService := onboarding.NewOnboardingService(r.DB)
	var data map[string]interface{}
	if input.Data != nil {
		if d, ok := input.Data.(map[string]interface{}); ok {
			data = d
		}
	}
	err := onboardingService.CompleteOnboardingStep(ctx, userClaims.UserID, input.StepName, data)
	if err != nil {
		return nil, err
	}

	// Return updated progress
	progress, err := onboardingService.GetUserOnboardingProgress(ctx, userClaims.UserID)
	if err != nil {
		return nil, err
	}

	var steps []*model.OnboardingStep
	for _, progressStep := range progress.Steps {
		steps = append(steps, &model.OnboardingStep{
			ID:          progressStep.ID,
			Name:        progressStep.Name,
			Title:       progressStep.Title,
			Description: progressStep.Description,
			Required:    progressStep.Required,
			Order:       progressStep.Order,
			Completed:   progressStep.Completed,
			CompletedAt: progressStep.CompletedAt,
			Data:        progressStep.Data,
		})
	}

	return &model.OnboardingProgress{
		UserID:         progress.UserID,
		CurrentStep:    progress.CurrentStep,
		TotalSteps:     progress.TotalSteps,
		CompletedSteps: progress.CompletedSteps,
		IsCompleted:    progress.IsCompleted,
		CompletionRate: progress.CompletionRate,
		Steps:          steps,
	}, nil
}

// UpdateUserProfile is the resolver for the updateUserProfile field.
func (r *mutationResolver) UpdateUserProfile(ctx context.Context, input model.UpdateUserProfileInput) (*model.UserProfile, error) {
	userClaims, ok := ctx.Value(auth.UserContextKey).(*auth.Claims)
	if !ok {
		return nil, fmt.Errorf("unauthorized")
	}

	onboardingService := onboarding.NewOnboardingService(r.DB)

	profile := &onboarding.UserProfile{
		UserID: userClaims.UserID,
	}

	if input.FirstName != nil {
		profile.FirstName = *input.FirstName
	}
	if input.LastName != nil {
		profile.LastName = *input.LastName
	}
	if input.Company != nil {
		profile.Company = *input.Company
	}
	if input.BusinessType != nil {
		profile.BusinessType = *input.BusinessType
	}
	if input.Industry != nil {
		profile.Industry = *input.Industry
	}
	if input.TeamSize != nil {
		profile.TeamSize = *input.TeamSize
	}
	if input.PrimaryGoal != nil {
		profile.PrimaryGoal = *input.PrimaryGoal
	}
	if input.AvatarURL != nil {
		profile.AvatarURL = *input.AvatarURL
	}
	if input.Timezone != nil {
		profile.Timezone = *input.Timezone
	}
	if input.Language != nil {
		profile.Language = *input.Language
	}

	err := onboardingService.UpdateUserProfile(ctx, userClaims.UserID, profile)
	if err != nil {
		return nil, err
	}

	// Return updated profile
	queryResolver := &queryResolver{
		Resolver: r.Resolver,
	}
	return queryResolver.UserProfile(ctx)
}

// UpdateUserSettings is the resolver for the updateUserSettings field.
func (r *mutationResolver) UpdateUserSettings(ctx context.Context, input model.UpdateUserSettingsInput) (*model.UserSettings, error) {
	userClaims, ok := ctx.Value(auth.UserContextKey).(*auth.Claims)
	if !ok {
		return nil, fmt.Errorf("unauthorized")
	}

	onboardingService := onboarding.NewOnboardingService(r.DB)

	// Get current settings
	currentSettings, err := onboardingService.GetUserSettings(ctx, userClaims.UserID)
	if err != nil {
		return nil, err
	}

	// Update only provided fields
	if input.EmailNotifications != nil {
		currentSettings.EmailNotifications = *input.EmailNotifications
	}
	if input.BrowserNotifications != nil {
		currentSettings.BrowserNotifications = *input.BrowserNotifications
	}
	if input.MarketingEmails != nil {
		currentSettings.MarketingEmails = *input.MarketingEmails
	}
	if input.WeeklyDigest != nil {
		currentSettings.WeeklyDigest = *input.WeeklyDigest
	}
	if input.SecurityAlerts != nil {
		currentSettings.SecurityAlerts = *input.SecurityAlerts
	}
	if input.Theme != nil {
		currentSettings.Theme = *input.Theme
	}
	if input.SidebarCollapsed != nil {
		currentSettings.SidebarCollapsed = *input.SidebarCollapsed
	}
	if input.DefaultDashboardView != nil {
		currentSettings.DefaultDashboardView = *input.DefaultDashboardView
	}
	if input.AutoSave != nil {
		currentSettings.AutoSave = *input.AutoSave
	}

	err = onboardingService.UpdateUserSettings(ctx, userClaims.UserID, currentSettings)
	if err != nil {
		return nil, err
	}

	// Return updated settings
	queryResolver := &queryResolver{
		Resolver: r.Resolver,
	}
	return queryResolver.UserSettings(ctx)
}

// DuplicatePage is the resolver for the duplicatePage field.
func (r *mutationResolver) DuplicatePage(ctx context.Context, pageID string, newTitle *string) (*model.Page, error) {
	panic("not implemented")
}

// GetSiteSettings is the resolver for the getSiteSettings field.
func (r *mutationResolver) GetSiteSettings(ctx context.Context, siteID string) (*model.SiteSettings, error) {
	panic("not implemented")
}

// CompileSite is the resolver for the compileSite field.
func (r *mutationResolver) CompileSite(ctx context.Context, siteID string) (*model.CompilationResult, error) {
	panic("not implemented")
}

// CreateExpertApplication is the resolver for the createExpertApplication field.
func (r *mutationResolver) CreateExpertApplication(ctx context.Context, input model.CreateExpertApplicationInput) (*model.ExpertApplication, error) {
	panic("not implemented")
}

// ReviewExpertApplication is the resolver for the reviewExpertApplication field.
func (r *mutationResolver) ReviewExpertApplication(ctx context.Context, id string, status string, notes *string) (*model.ExpertApplication, error) {
	panic("not implemented")
}

// CreateClientRequirement is the resolver for the createClientRequirement field.
func (r *mutationResolver) CreateClientRequirement(ctx context.Context, input model.CreateClientRequirementInput) (*model.ClientRequirement, error) {
	panic("not implemented")
}

// FindExpertMatches is the resolver for the findExpertMatches field.
func (r *mutationResolver) FindExpertMatches(ctx context.Context, requirementID string) ([]*model.ExpertClientMatch, error) {
	panic("not implemented")
}

// ContactExpert is the resolver for the contactExpert field.
func (r *mutationResolver) ContactExpert(ctx context.Context, matchID string, message string) (*model.ExpertClientMatch, error) {
	panic("not implemented")
}

// RespondToMatch is the resolver for the respondToMatch field.
func (r *mutationResolver) RespondToMatch(ctx context.Context, matchID string, response string, accept bool) (*model.ExpertClientMatch, error) {
	panic("not implemented")
}

// CreateProjectEngagement is the resolver for the createProjectEngagement field.
func (r *mutationResolver) CreateProjectEngagement(ctx context.Context, input model.CreateProjectEngagementInput) (*model.ProjectEngagement, error) {
	panic("not implemented")
}

// UpdateEngagementStatus is the resolver for the updateEngagementStatus field.
func (r *mutationResolver) UpdateEngagementStatus(ctx context.Context, id string, status string) (*model.ProjectEngagement, error) {
	panic("not implemented")
}

// AddProjectMilestone is the resolver for the addProjectMilestone field.
func (r *mutationResolver) AddProjectMilestone(ctx context.Context, engagementID string, title string, description *string, dueDate time.Time, value *float64) (*model.ProjectMilestone, error) {
	panic("not implemented")
}

// CompleteProjectMilestone is the resolver for the completeProjectMilestone field.
func (r *mutationResolver) CompleteProjectMilestone(ctx context.Context, id string, notes *string) (*model.ProjectMilestone, error) {
	panic("not implemented")
}

// CreateConversation is the resolver for the createConversation field.
func (r *mutationResolver) CreateConversation(ctx context.Context, participantIds []string) (*model.Conversation, error) {
	panic("not implemented")
}

// SendMessage is the resolver for the sendMessage field.
func (r *mutationResolver) SendMessage(ctx context.Context, input model.SendMessageInput) (*model.Message, error) {
	panic("not implemented")
}

// MarkMessageAsRead is the resolver for the markMessageAsRead field.
func (r *mutationResolver) MarkMessageAsRead(ctx context.Context, messageID string) (*model.Message, error) {
	panic("not implemented")
}

// MarkConversationAsRead is the resolver for the markConversationAsRead field.
func (r *mutationResolver) MarkConversationAsRead(ctx context.Context, conversationID string) (bool, error) {
	panic("not implemented")
}

// UpdateMilestoneProgress is the resolver for the updateMilestoneProgress field.
func (r *mutationResolver) UpdateMilestoneProgress(ctx context.Context, id string, progressPercentage int, notes *string) (*model.ProjectMilestone, error) {
	panic("not implemented")
}

// Query resolvers
func (r *queryResolver) Health(ctx context.Context) (string, error) {
	return "OK", nil
}

// GetAddonsInReview is the resolver for the getAddonsInReview field.
func (r *queryResolver) GetAddonsInReview(ctx context.Context) ([]*model.AddonConfig, error) {
	claims, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	if !auth.HasPermission(claims.UserID, "addon:review") {
		return nil, fmt.Errorf("unauthorized")
	}

	configs, err := r.DB.GetAddonsInReview(ctx)
	if err != nil {
		return nil, err
	}

	// Convert AddonConfig to Addon
	var addons []*model.Addon
	for _, config := range configs {
		addon := &model.Addon{
			ID:        config.ID,
			Name:      config.Name,
			Version:   "1.0.0", // Default version
			Status:    config.State,
			CreatedAt: config.CreatedAt,
			UpdatedAt: config.UpdatedAt,
		}
		addons = append(addons, addon)
	}

	// Convert to AddonConfig slice
	var addonConfigs []*model.AddonConfig
	for _, addon := range addons {
		addonConfigs = append(addonConfigs, &model.AddonConfig{
			ID:        addon.ID,
			Name:      addon.Name,
			Config:    "{}",
			State:     addon.Status,
			CreatedAt: addon.CreatedAt,
			UpdatedAt: addon.UpdatedAt,
		})
	}
	return addonConfigs, nil
}

// GetAddonTestSession is the resolver for the getAddonTestSession field.
func (r *queryResolver) GetAddonTestSession(ctx context.Context, sessionID string) (*model.AddonTestSession, error) {
	panic("not implemented")
}

// GetAddonTestHistory is the resolver for the getAddonTestHistory field.
func (r *queryResolver) GetAddonTestHistory(ctx context.Context, addonID string) ([]*model.AddonTestSession, error) {
	panic("not implemented")
}

// Vehicles is the resolver for the vehicles field.
func (r *queryResolver) Vehicles(ctx context.Context) ([]*model.Vehicle, error) {
	claims, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}
	return r.DB.GetVehicles(ctx, claims.UserID)
}

// VehicleByVin is the resolver for the vehicleByVIN field.
func (r *queryResolver) VehicleByVin(ctx context.Context, vin string) (*model.Vehicle, error) {
	claims, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}
	return r.DB.GetVehicleByVIN(ctx, vin, claims.UserID)
}

// WebAddons is the resolver for the webAddons field.
func (r *queryResolver) WebAddons(ctx context.Context, status *string) ([]*model.WebAddon, error) {
	return r.DB.GetWebAddons(ctx, status)
}

// PredefinedSnippets is the resolver for the predefinedSnippets field.
func (r *queryResolver) PredefinedSnippets(ctx context.Context, category *string) ([]*model.PredefinedSnippet, error) {
	return r.DB.GetPredefinedSnippets(ctx, category)
}

// Component is the resolver for the component field.
func (r *queryResolver) Component(ctx context.Context, id string, typeArg model.ComponentType) (model.ComponentResult, error) {
	switch typeArg {
	case model.ComponentTypeAddon:
		addons, err := r.DB.GetWebAddons(ctx, nil)
		if err != nil {
			return nil, err
		}
		for _, addon := range addons {
			if addon.ID == id {
				return &model.WebAddon{
					ID:   addon.ID,
					Name: addon.Name,
				}, nil
			}
		}
		return nil, fmt.Errorf("addon not found")
	case model.ComponentTypeSnippet:
		snippets, err := r.DB.GetPredefinedSnippets(ctx, nil)
		if err != nil {
			return nil, err
		}
		for _, snippet := range snippets {
			if snippet.ID == id {
				return snippet, nil
			}
		}
		return nil, fmt.Errorf("snippet not found")
	default:
		return nil, fmt.Errorf("unknown component type")
	}
}

// GetNavigation is the resolver for the getNavigation field.
func (r *queryResolver) GetNavigation(ctx context.Context) ([]*model.Page, error) {
	claims, err := auth.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	pages, err := r.DB.GetNavigation(ctx, claims.UserID)
	if err != nil {
		return nil, fmt.Errorf("failed to get navigation: %w", err)
	}

	return pages, nil
}

// Tenant Query Resolvers
func (r *queryResolver) Tenants(ctx context.Context, limit *int, offset *int, status *string) ([]*model.Tenant, error) {
	// Check if user is admin
	userClaims, ok := ctx.Value(auth.UserContextKey).(*auth.Claims)
	if !ok || !auth.IsSuperAdmin(userClaims) {
		return nil, fmt.Errorf("access denied: admin privileges required")
	}

	tenantService := database.NewTenantService(r.DB)

	// Set defaults
	limitVal := 25
	if limit != nil {
		limitVal = *limit
	}
	offsetVal := 0
	if offset != nil {
		offsetVal = *offset
	}
	statusVal := ""
	if status != nil {
		statusVal = *status
	}

	tenants, err := tenantService.ListTenants(limitVal, offsetVal, statusVal)
	if err != nil {
		return nil, err
	}

	// Convert to GraphQL model
	var result []*model.Tenant
	for _, tenant := range tenants {
		result = append(result, &model.Tenant{
			ID:        tenant.ID,
			Name:      tenant.Name,
			Slug:      tenant.Slug,
			Domain:    &tenant.Domain,
			Status:    tenant.Status,
			PlanType:  tenant.PlanType,
			MaxUsers:  tenant.MaxUsers,
			MaxSites:  tenant.MaxSites,
			Settings:  map[string]interface{}{},
			CreatedAt: tenant.CreatedAt,
			UpdatedAt: tenant.UpdatedAt,
		})
	}

	return result, nil
}

// Tenant is the resolver for the tenant field.
func (r *queryResolver) Tenant(ctx context.Context, id string) (*model.Tenant, error) {
	// Check if user is admin or belongs to this tenant
	userClaims, ok := ctx.Value(auth.UserContextKey).(*auth.Claims)
	if !ok {
		return nil, fmt.Errorf("unauthorized")
	}

	if !auth.IsSuperAdmin(userClaims) && userClaims.TenantID != id {
		return nil, fmt.Errorf("access denied")
	}

	tenantService := database.NewTenantService(r.DB)
	tenant, err := tenantService.GetTenantByID(id)
	if err != nil {
		return nil, err
	}

	return &model.Tenant{
		ID:        tenant.ID,
		Name:      tenant.Name,
		Slug:      tenant.Slug,
		Domain:    &tenant.Domain,
		Status:    tenant.Status,
		PlanType:  tenant.PlanType,
		MaxUsers:  tenant.MaxUsers,
		MaxSites:  tenant.MaxSites,
		Settings:  map[string]interface{}{},
		CreatedAt: tenant.CreatedAt,
		UpdatedAt: tenant.UpdatedAt,
	}, nil
}

// TenantStats is the resolver for the tenantStats field.
func (r *queryResolver) TenantStats(ctx context.Context, tenantID string) (*model.TenantStats, error) {
	// Check if user is admin or belongs to this tenant
	userClaims, ok := ctx.Value(auth.UserContextKey).(*auth.Claims)
	if !ok {
		return nil, fmt.Errorf("unauthorized")
	}

	if !auth.IsSuperAdmin(userClaims) && userClaims.TenantID != tenantID {
		return nil, fmt.Errorf("access denied")
	}

	tenantService := database.NewTenantService(r.DB)
	stats, err := tenantService.GetTenantStats(tenantID)
	if err != nil {
		return nil, err
	}

	return &model.TenantStats{
		UserCount:  stats["user_count"].(int),
		SiteCount:  stats["site_count"].(int),
		PageCount:  stats["page_count"].(int),
		AddonCount: stats["addon_count"].(int),
	}, nil
}

// TenantLimits is the resolver for the tenantLimits field.
func (r *queryResolver) TenantLimits(ctx context.Context, tenantID string) (*model.TenantLimits, error) {
	// Check if user is admin or belongs to this tenant
	userClaims, ok := ctx.Value(auth.UserContextKey).(*auth.Claims)
	if !ok {
		return nil, fmt.Errorf("unauthorized")
	}

	if !auth.IsSuperAdmin(userClaims) && userClaims.TenantID != tenantID {
		return nil, fmt.Errorf("access denied")
	}

	tenantService := database.NewTenantService(r.DB)
	limits, err := tenantService.CheckTenantLimits(tenantID)
	if err != nil {
		return nil, err
	}

	return &model.TenantLimits{
		UsersWithinLimit: limits["users_within_limit"],
		SitesWithinLimit: limits["sites_within_limit"],
	}, nil
}

// Subscription Query Resolvers
func (r *queryResolver) SubscriptionTiers(ctx context.Context) ([]*model.SubscriptionTier, error) {
	subscriptionService := subscription.NewSubscriptionService(r.DB)
	tiers, err := subscriptionService.GetAllTiers(ctx)
	if err != nil {
		return nil, err
	}

	var result []*model.SubscriptionTier
	for _, tier := range tiers {
		result = append(result, &model.SubscriptionTier{
			ID:           tier.ID,
			Name:         tier.Name,
			PriceMonthly: tier.PriceMonthly,
			Features:     tier.Features,
			Limits:       tier.Limits,
			IsActive:     tier.IsActive,
			CreatedAt:    tier.CreatedAt,
			UpdatedAt:    tier.UpdatedAt,
		})
	}

	return result, nil
}

// SubscriptionTier is the resolver for the subscriptionTier field.
func (r *queryResolver) SubscriptionTier(ctx context.Context, id string) (*model.SubscriptionTier, error) {
	panic("not implemented")
}

// UserSubscription is the resolver for the userSubscription field.
func (r *queryResolver) UserSubscription(ctx context.Context) (*model.UserSubscription, error) {
	userClaims, ok := ctx.Value(auth.UserContextKey).(*auth.Claims)
	if !ok {
		return nil, fmt.Errorf("unauthorized")
	}

	subscriptionService := subscription.NewSubscriptionService(r.DB)
	sub, err := subscriptionService.GetUserSubscription(ctx, userClaims.UserID)
	if err != nil {
		return nil, err
	}

	return &model.UserSubscription{
		ID:                   sub.ID,
		UserID:               sub.UserID,
		Status:               sub.Status,
		CurrentPeriodStart:   sub.CurrentPeriodStart,
		CurrentPeriodEnd:     sub.CurrentPeriodEnd,
		StripeSubscriptionID: sub.StripeSubscriptionID,
		CreatedAt:            sub.CreatedAt,
		UpdatedAt:            sub.UpdatedAt,
		Tier: &model.SubscriptionTier{
			ID:           sub.Tier.ID,
			Name:         sub.Tier.Name,
			PriceMonthly: sub.Tier.PriceMonthly,
			Features:     sub.Tier.Features,
			Limits:       sub.Tier.Limits,
		},
	}, nil
}

// UserSubscriptionHistory is the resolver for the userSubscriptionHistory field.
func (r *queryResolver) UserSubscriptionHistory(ctx context.Context) ([]*model.UserSubscription, error) {
	panic("not implemented")
}

// CurrentUsage is the resolver for the currentUsage field.
func (r *queryResolver) CurrentUsage(ctx context.Context, resourceType string) (int, error) {
	userClaims, ok := ctx.Value(auth.UserContextKey).(*auth.Claims)
	if !ok {
		return 0, fmt.Errorf("unauthorized")
	}

	subscriptionService := subscription.NewSubscriptionService(r.DB)
	usage, err := subscriptionService.GetCurrentUsage(ctx, userClaims.UserID, resourceType)
	if err != nil {
		return 0, err
	}

	return usage, nil
}

// UsageHistory is the resolver for the usageHistory field.
func (r *queryResolver) UsageHistory(ctx context.Context, resourceType *string, limit *int) ([]*model.UsageTracking, error) {
	panic("not implemented")
}

// UserInvoices is the resolver for the userInvoices field.
func (r *queryResolver) UserInvoices(ctx context.Context, limit *int, offset *int) ([]*model.Invoice, error) {
	panic("not implemented")
}

// Invoice is the resolver for the invoice field.
func (r *queryResolver) Invoice(ctx context.Context, id string) (*model.Invoice, error) {
	panic("not implemented")
}

// Onboarding & Dashboard Query Resolvers
func (r *queryResolver) OnboardingProgress(ctx context.Context) (*model.OnboardingProgress, error) {
	userClaims, ok := ctx.Value(auth.UserContextKey).(*auth.Claims)
	if !ok {
		return nil, fmt.Errorf("unauthorized")
	}

	onboardingService := onboarding.NewOnboardingService(r.DB)
	progress, err := onboardingService.GetUserOnboardingProgress(ctx, userClaims.UserID)
	if err != nil {
		return nil, err
	}

	var steps []*model.OnboardingStep
	for _, progressStep := range progress.Steps {
		steps = append(steps, &model.OnboardingStep{
			ID:          progressStep.ID,
			Name:        progressStep.Name,
			Title:       progressStep.Title,
			Description: progressStep.Description,
			Required:    progressStep.Required,
			Order:       progressStep.Order,
			Completed:   progressStep.Completed,
			CompletedAt: progressStep.CompletedAt,
			Data:        progressStep.Data,
		})
	}

	return &model.OnboardingProgress{
		UserID:         progress.UserID,
		CurrentStep:    progress.CurrentStep,
		TotalSteps:     progress.TotalSteps,
		CompletedSteps: progress.CompletedSteps,
		IsCompleted:    progress.IsCompleted,
		CompletionRate: progress.CompletionRate,
		Steps:          steps,
	}, nil
}

// UserProfile is the resolver for the userProfile field.
func (r *queryResolver) UserProfile(ctx context.Context) (*model.UserProfile, error) {
	userClaims, ok := ctx.Value(auth.UserContextKey).(*auth.Claims)
	if !ok {
		return nil, fmt.Errorf("unauthorized")
	}

	// Get user profile from database
	var profile model.UserProfile
	query := `
		SELECT id, COALESCE(first_name, ''), COALESCE(last_name, ''), COALESCE(company, ''),
		       COALESCE(business_type, ''), COALESCE(industry, ''), COALESCE(team_size, ''),
		       COALESCE(primary_goal, ''), COALESCE(avatar_url, ''), 
		       COALESCE(timezone, 'UTC'), COALESCE(language, 'en')
		FROM users
		WHERE id = $1
	`

	err := r.DB.Pool.QueryRow(ctx, query, userClaims.UserID).Scan(
		&profile.UserID, &profile.FirstName, &profile.LastName, &profile.Company,
		&profile.BusinessType, &profile.Industry, &profile.TeamSize,
		&profile.PrimaryGoal, &profile.AvatarURL, &profile.Timezone, &profile.Language,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to get user profile: %w", err)
	}

	return &profile, nil
}

// UserSettings is the resolver for the userSettings field.
func (r *queryResolver) UserSettings(ctx context.Context) (*model.UserSettings, error) {
	userClaims, ok := ctx.Value(auth.UserContextKey).(*auth.Claims)
	if !ok {
		return nil, fmt.Errorf("unauthorized")
	}

	onboardingService := onboarding.NewOnboardingService(r.DB)
	settings, err := onboardingService.GetUserSettings(ctx, userClaims.UserID)
	if err != nil {
		return nil, err
	}

	return &model.UserSettings{
		ID:                   settings.ID,
		UserID:               settings.UserID,
		EmailNotifications:   settings.EmailNotifications,
		BrowserNotifications: settings.BrowserNotifications,
		MarketingEmails:      settings.MarketingEmails,
		WeeklyDigest:         settings.WeeklyDigest,
		SecurityAlerts:       settings.SecurityAlerts,
		Theme:                settings.Theme,
		SidebarCollapsed:     settings.SidebarCollapsed,
		DefaultDashboardView: settings.DefaultDashboardView,
		AutoSave:             settings.AutoSave,
		CreatedAt:            settings.CreatedAt,
		UpdatedAt:            settings.UpdatedAt,
	}, nil
}

// DashboardStats is the resolver for the dashboardStats field.
func (r *queryResolver) DashboardStats(ctx context.Context) (*model.DashboardStats, error) {
	userClaims, ok := ctx.Value(auth.UserContextKey).(*auth.Claims)
	if !ok {
		return nil, fmt.Errorf("unauthorized")
	}

	dashboardService := dashboard.NewDashboardService(r.DB)
	stats, err := dashboardService.GetDashboardStats(ctx, userClaims.UserID, userClaims.TenantID)
	if err != nil {
		return nil, err
	}

	var activities []*model.ActivityItem
	for _, activity := range stats.RecentActivity {
		activities = append(activities, &model.ActivityItem{
			ID:          activity.ID,
			Type:        activity.Type,
			Description: activity.Description,
			Timestamp:   activity.Timestamp,
			Metadata:    activity.Metadata,
		})
	}

	return &model.DashboardStats{
		TotalSites:       stats.TotalSites,
		TotalPages:       stats.TotalPages,
		TotalAddons:      stats.TotalAddons,
		CurrentUsage:     stats.CurrentUsage,
		SubscriptionTier: stats.SubscriptionTier,
		StorageUsed:      stats.StorageUsed,
		BandwidthUsed:    stats.BandwidthUsed,
		APICallsUsed:     stats.APICallsUsed,
		RecentActivity:   activities,
	}, nil
}

// Site Management Query Resolvers
func (r *queryResolver) MySites(ctx context.Context) ([]*model.Site, error) {
	userClaims, ok := ctx.Value(auth.UserContextKey).(*auth.Claims)
	if !ok {
		return nil, fmt.Errorf("unauthorized")
	}

	siteManager := sitemanager.NewSiteManager(r.DB)
	userID, err := strconv.Atoi(userClaims.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}
	sites, err := siteManager.GetUserSites(ctx, userID, userClaims.TenantID)
	if err != nil {
		return nil, err
	}

	var result []*model.Site
	for _, site := range sites {
		result = append(result, &model.Site{
			ID:           fmt.Sprintf("%d", site.ID),
			Name:         site.Name,
			Description:  &site.Description,
			UserID:       fmt.Sprintf("%d", site.UserID),
			TenantID:     site.TenantID,
			Status:       site.Status,
			CustomDomain: &site.CustomDomain,
			SslEnabled:   site.SSLEnabled,
			ThemeConfig:  site.ThemeConfig,
			SeoConfig:    site.SEOConfig,
			CreatedAt:    site.CreatedAt,
			UpdatedAt:    site.UpdatedAt,
			PageCount:    site.PageCount,
			IsPublished:  site.IsPublished,
			Pages:        []*model.SitePage{}, // Will be populated by field resolver if needed
		})
	}

	return result, nil
}

// Site is the resolver for the site field.
func (r *queryResolver) Site(ctx context.Context, id string) (*model.Site, error) {
	userClaims, ok := ctx.Value(auth.UserContextKey).(*auth.Claims)
	if !ok {
		return nil, fmt.Errorf("unauthorized")
	}

	siteID, err := strconv.Atoi(id)
	if err != nil {
		return nil, fmt.Errorf("invalid site ID")
	}

	siteManager := sitemanager.NewSiteManager(r.DB)
	userID, err := strconv.Atoi(userClaims.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}
	site, err := siteManager.GetSiteByID(ctx, siteID, userID, userClaims.TenantID)
	if err != nil {
		return nil, err
	}

	// Get site pages
	userIDInt, err := strconv.Atoi(userClaims.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}
	sitePages, err := siteManager.GetSitePages(ctx, site.ID, userIDInt, userClaims.TenantID)
	if err != nil {
		return nil, err
	}

	var pages []*model.SitePage
	for _, sp := range sitePages {
		page := &model.SitePage{
			ID:         sp.ID,
			SiteID:     fmt.Sprintf("%d", sp.SiteID),
			PageID:     sp.PageID,
			IsHomepage: sp.IsHomepage,
			Position:   sp.Position,
			TenantID:   sp.TenantID,
			CreatedAt:  sp.CreatedAt,
		}

		if sp.Page != nil {
			page.Page = &model.Page{
				ID:        sp.Page.ID,
				Title:     sp.Page.Title,
				Slug:      sp.Page.Slug,
				ParentID:  sp.Page.ParentID,
				Position:  sp.Page.Position,
				IsHidden:  sp.Page.IsHidden,
				CreatedAt: sp.Page.CreatedAt,
				UpdatedAt: sp.Page.UpdatedAt,
			}
		}

		pages = append(pages, page)
	}

	return &model.Site{
		ID:           fmt.Sprintf("%d", site.ID),
		Name:         site.Name,
		Description:  &site.Description,
		UserID:       fmt.Sprintf("%d", site.UserID),
		TenantID:     site.TenantID,
		Status:       site.Status,
		CustomDomain: &site.CustomDomain,
		SslEnabled:   site.SSLEnabled,
		ThemeConfig:  site.ThemeConfig,
		SeoConfig:    site.SEOConfig,
		CreatedAt:    site.CreatedAt,
		UpdatedAt:    site.UpdatedAt,
		PageCount:    site.PageCount,
		IsPublished:  site.IsPublished,
		Pages:        pages,
	}, nil
}

// SitePages is the resolver for the sitePages field.
func (r *queryResolver) SitePages(ctx context.Context, siteID string) ([]*model.SitePage, error) {
	userClaims, ok := ctx.Value(auth.UserContextKey).(*auth.Claims)
	if !ok {
		return nil, fmt.Errorf("unauthorized")
	}

	siteIDInt, err := strconv.Atoi(siteID)
	if err != nil {
		return nil, fmt.Errorf("invalid site ID")
	}

	siteManager := sitemanager.NewSiteManager(r.DB)
	userID, err := strconv.Atoi(userClaims.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}
	sitePages, err := siteManager.GetSitePages(ctx, siteIDInt, userID, userClaims.TenantID)
	if err != nil {
		return nil, err
	}

	var result []*model.SitePage
	for _, sp := range sitePages {
		page := &model.SitePage{
			ID:         sp.ID,
			SiteID:     fmt.Sprintf("%d", sp.SiteID),
			PageID:     sp.PageID,
			IsHomepage: sp.IsHomepage,
			Position:   sp.Position,
			TenantID:   sp.TenantID,
			CreatedAt:  sp.CreatedAt,
		}

		if sp.Page != nil {
			page.Page = &model.Page{
				ID:        sp.Page.ID,
				Title:     sp.Page.Title,
				Slug:      sp.Page.Slug,
				ParentID:  sp.Page.ParentID,
				Position:  sp.Page.Position,
				IsHidden:  sp.Page.IsHidden,
				CreatedAt: sp.Page.CreatedAt,
				UpdatedAt: sp.Page.UpdatedAt,
			}
		}

		result = append(result, page)
	}

	return result, nil
}

// Enhanced Page Management Query Resolvers
func (r *queryResolver) Page(ctx context.Context, id string) (*model.Page, error) {
	userClaims, ok := ctx.Value(auth.UserContextKey).(*auth.Claims)
	if !ok {
		return nil, fmt.Errorf("unauthorized")
	}

	enhancedPageManager := pagemanager.NewEnhancedPageManager(r.DB)
	userID, err := strconv.Atoi(userClaims.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}
	page, err := enhancedPageManager.GetPageByID(ctx, id, userID, userClaims.TenantID)
	if err != nil {
		return nil, err
	}

	return &model.Page{
		ID:              page.ID,
		Title:           page.Title,
		Slug:            page.Slug,
		Content:         page.Content,
		ParentID:        page.ParentID,
		Position:        page.Position,
		IsHidden:        page.IsHidden,
		CreatedAt:       page.CreatedAt,
		UpdatedAt:       page.UpdatedAt,
		MetaTitle:       page.MetaTitle,
		MetaDescription: page.MetaDescription,
		MetaKeywords:    page.MetaKeywords,
		CustomCSS:       page.CustomCSS,
		CustomJs:        page.CustomJS,
		FeaturedImage:   page.FeaturedImage,
		Status:          page.Status,
		PublishedAt:     page.PublishedAt,
		Template:        page.Template,
		Settings:        page.Settings,
		ContentVersion:  page.ContentVersion,
		IsPublished:     page.IsPublished,
		Children:        []*model.Page{}, // Will be populated by field resolver if needed
	}, nil
}

// PagesByParent is the resolver for the pagesByParent field.
func (r *queryResolver) PagesByParent(ctx context.Context, parentID *string, siteID string) ([]*model.Page, error) {
	userClaims, ok := ctx.Value(auth.UserContextKey).(*auth.Claims)
	if !ok {
		return nil, fmt.Errorf("unauthorized")
	}

	siteIDInt, err := strconv.Atoi(siteID)
	if err != nil {
		return nil, fmt.Errorf("invalid site ID")
	}

	enhancedPageManager := pagemanager.NewEnhancedPageManager(r.DB)
	userID, err := strconv.Atoi(userClaims.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}
	pages, err := enhancedPageManager.GetSitePages(ctx, siteIDInt, userID, userClaims.TenantID)
	if err != nil {
		return nil, err
	}

	// Filter pages by parent and convert to GraphQL model
	var filteredPages []*model.Page
	for _, page := range pages {
		if (parentID == nil && page.ParentID == nil) || (parentID != nil && page.ParentID != nil && *page.ParentID == *parentID) {
			modelPage := &model.Page{
				ID:              page.ID,
				Title:           page.Title,
				Slug:            page.Slug,
				Content:         page.Content,
				ParentID:        page.ParentID,
				Position:        page.Position,
				IsHidden:        page.IsHidden,
				CreatedAt:       page.CreatedAt,
				UpdatedAt:       page.UpdatedAt,
				MetaTitle:       page.MetaTitle,
				MetaDescription: page.MetaDescription,
				MetaKeywords:    page.MetaKeywords,
				CustomCSS:       page.CustomCSS,
				CustomJs:        page.CustomJS,
				FeaturedImage:   page.FeaturedImage,
				Status:          page.Status,
				PublishedAt:     page.PublishedAt,
				Template:        page.Template,
				Settings:        page.Settings,
				ContentVersion:  page.ContentVersion,
				IsPublished:     page.IsPublished,
				Children:        []*model.Page{},
			}
			filteredPages = append(filteredPages, modelPage)
		}
	}

	return filteredPages, nil
}

// Site Settings Query Resolver
func (r *queryResolver) SiteSettings(ctx context.Context, siteID string) (*model.SiteSettings, error) {
	userClaims, ok := ctx.Value(auth.UserContextKey).(*auth.Claims)
	if !ok {
		return nil, fmt.Errorf("unauthorized")
	}

	siteManager := sitemanager.NewSiteManager(r.DB)
	siteIDInt, err := strconv.Atoi(siteID)
	if err != nil {
		return nil, fmt.Errorf("invalid site ID")
	}

	// Verify user owns this site
	userIDInt, err := strconv.Atoi(userClaims.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}
	site, err := siteManager.GetSiteByID(ctx, siteIDInt, userIDInt, userClaims.TenantID)
	if err != nil {
		return nil, err
	}

	return &model.SiteSettings{
		ID:              fmt.Sprintf("%d", site.ID),
		SiteName:        site.Name,
		SiteDescription: &site.Description,
		CustomDomain:    &site.CustomDomain,
		SslEnabled:      site.SSLEnabled,
		IsPublished:     site.IsPublished,
		ThemeConfig:     site.ThemeConfig,
		SeoConfig:       site.SEOConfig,
	}, nil
}

// SitePublishingHistory is the resolver for the sitePublishingHistory field.
func (r *queryResolver) SitePublishingHistory(ctx context.Context, siteID string, limit *int) ([]*model.PublishingHistory, error) {
	panic("not implemented")
}

// SiteVersions is the resolver for the siteVersions field.
func (r *queryResolver) SiteVersions(ctx context.Context, siteID string) ([]*model.PublishingVersion, error) {
	panic("not implemented")
}

// ExpertApplications is the resolver for the expertApplications field.
func (r *queryResolver) ExpertApplications(ctx context.Context, status *string, limit *int) ([]*model.ExpertApplication, error) {
	panic("not implemented")
}

// ExpertApplication is the resolver for the expertApplication field.
func (r *queryResolver) ExpertApplication(ctx context.Context, id string) (*model.ExpertApplication, error) {
	panic("not implemented")
}

// MyExpertApplication is the resolver for the myExpertApplication field.
func (r *queryResolver) MyExpertApplication(ctx context.Context) (*model.ExpertApplication, error) {
	panic("not implemented")
}

// ClientRequirements is the resolver for the clientRequirements field.
func (r *queryResolver) ClientRequirements(ctx context.Context, status *string, limit *int) ([]*model.ClientRequirement, error) {
	panic("not implemented")
}

// ClientRequirement is the resolver for the clientRequirement field.
func (r *queryResolver) ClientRequirement(ctx context.Context, id string) (*model.ClientRequirement, error) {
	panic("not implemented")
}

// MyClientRequirements is the resolver for the myClientRequirements field.
func (r *queryResolver) MyClientRequirements(ctx context.Context) ([]*model.ClientRequirement, error) {
	panic("not implemented")
}

// ExpertMatches is the resolver for the expertMatches field.
func (r *queryResolver) ExpertMatches(ctx context.Context, requirementID string) ([]*model.ExpertClientMatch, error) {
	panic("not implemented")
}

// MyMatches is the resolver for the myMatches field.
func (r *queryResolver) MyMatches(ctx context.Context) ([]*model.ExpertClientMatch, error) {
	panic("not implemented")
}

// ProjectEngagements is the resolver for the projectEngagements field.
func (r *queryResolver) ProjectEngagements(ctx context.Context, status *string, limit *int) ([]*model.ProjectEngagement, error) {
	panic("not implemented")
}

// ProjectEngagement is the resolver for the projectEngagement field.
func (r *queryResolver) ProjectEngagement(ctx context.Context, id string) (*model.ProjectEngagement, error) {
	panic("not implemented")
}

// MyProjectEngagements is the resolver for the myProjectEngagements field.
func (r *queryResolver) MyProjectEngagements(ctx context.Context) ([]*model.ProjectEngagement, error) {
	panic("not implemented")
}

// MyConversations is the resolver for the myConversations field.
func (r *queryResolver) MyConversations(ctx context.Context) ([]*model.Conversation, error) {
	panic("not implemented")
}

// Conversation is the resolver for the conversation field.
func (r *queryResolver) Conversation(ctx context.Context, id string) (*model.Conversation, error) {
	panic("not implemented")
}

// ConversationMessages is the resolver for the conversationMessages field.
func (r *queryResolver) ConversationMessages(ctx context.Context, conversationID string, limit *int) ([]*model.Message, error) {
	panic("not implemented")
}

// ExternalAPIs is the resolver for the externalAPIs field.
func (r *queryResolver) ExternalAPIs(ctx context.Context, status *model.APIStatus) ([]*model.ExternalAPI, error) {
	panic("not implemented")
}

// ExternalAPI is the resolver for the externalAPI field.
func (r *queryResolver) ExternalAPI(ctx context.Context, id string) (*model.ExternalAPI, error) {
	panic("not implemented")
}

// MyAPICredentials is the resolver for the myAPICredentials field.
func (r *queryResolver) MyAPICredentials(ctx context.Context, apiConfigID *string) ([]*model.APICredentials, error) {
	panic("not implemented")
}

// APIConnections is the resolver for the apiConnections field.
func (r *queryResolver) APIConnections(ctx context.Context, apiConfigID *string) ([]*model.APIConnection, error) {
	panic("not implemented")
}

// Me is the resolver for the me field.
func (r *queryResolver) Me(ctx context.Context) (*model.User, error) {
	panic("not implemented")
}

// Notifications is the resolver for the notifications field.
func (r *queryResolver) Notifications(ctx context.Context, limit *int, unreadOnly *bool) ([]*model.Notification, error) {
	panic("not implemented")
}

// SiteAnalytics is the resolver for the siteAnalytics field.
func (r *queryResolver) SiteAnalytics(ctx context.Context, siteID string, period *string) (*model.SiteAnalytics, error) {
	panic("not implemented")
}

// ExpertProfiles is the resolver for the expertProfiles field.
func (r *queryResolver) ExpertProfiles(ctx context.Context, filters *model.ExpertFiltersInput, limit *int, offset *int) ([]*model.ExpertProfile, error) {
	panic("not implemented")
}

// ExpertProfile is the resolver for the expertProfile field.
func (r *queryResolver) ExpertProfile(ctx context.Context, id string) (*model.ExpertProfile, error) {
	panic("not implemented")
}

// MyExpertProfile is the resolver for the myExpertProfile field.
func (r *queryResolver) MyExpertProfile(ctx context.Context) (*model.ExpertProfile, error) {
	panic("not implemented")
}

// Projects is the resolver for the projects field.
func (r *queryResolver) Projects(ctx context.Context, status *string, limit *int) ([]*model.Project, error) {
	panic("not implemented")
}

// Project is the resolver for the project field.
func (r *queryResolver) Project(ctx context.Context, id string) (*model.Project, error) {
	panic("not implemented")
}

// MyTickets is the resolver for the myTickets field.
func (r *queryResolver) MyTickets(ctx context.Context, status *string, limit *int) ([]*model.SupportTicket, error) {
	panic("not implemented")
}

// Ticket is the resolver for the ticket field.
func (r *queryResolver) Ticket(ctx context.Context, id string) (*model.SupportTicket, error) {
	panic("not implemented")
}

// BusinessPlan is the resolver for the businessPlan field.
func (r *queryResolver) BusinessPlan(ctx context.Context) (*model.BusinessPlan, error) {
	panic("not implemented")
}

// FinancialPlan is the resolver for the financialPlan field.
func (r *queryResolver) FinancialPlan(ctx context.Context) (*model.FinancialPlan, error) {
	panic("not implemented")
}

// CompetitorResearch is the resolver for the competitorResearch field.
func (r *queryResolver) CompetitorResearch(ctx context.Context, industry *string, location *string) (*model.CompetitorResearch, error) {
	panic("not implemented")
}

// ComplianceCheck is the resolver for the complianceCheck field.
func (r *queryResolver) ComplianceCheck(ctx context.Context, regulation string, industry *string, location *string) (*model.ComplianceCheck, error) {
	panic("not implemented")
}

// AdminDashboardKPIs is the resolver for the adminDashboardKPIs field.
func (r *queryResolver) AdminDashboardKPIs(ctx context.Context) (*model.AdminDashboardKPIs, error) {
	panic("not implemented")
}

// AdminUsers is the resolver for the adminUsers field.
func (r *queryResolver) AdminUsers(ctx context.Context, filters *model.UserSearchFiltersInput, page *int, pageSize *int) (*model.UserListResponse, error) {
	panic("not implemented")
}

// AdminUser is the resolver for the adminUser field.
func (r *queryResolver) AdminUser(ctx context.Context, id string) (*model.AdminUser, error) {
	panic("not implemented")
}

// AdminUserStats is the resolver for the adminUserStats field.
func (r *queryResolver) AdminUserStats(ctx context.Context) (*model.UserStats, error) {
	panic("not implemented")
}

// AdminExpertApplications is the resolver for the adminExpertApplications field.
func (r *queryResolver) AdminExpertApplications(ctx context.Context, filters *model.ExpertApplicationFiltersInput, page *int, pageSize *int) (*model.ExpertApplicationListResponse, error) {
	panic("not implemented")
}

// AdminExpertApplication is the resolver for the adminExpertApplication field.
func (r *queryResolver) AdminExpertApplication(ctx context.Context, id string) (*model.AdminExpertApplication, error) {
	panic("not implemented")
}

// AdminExpertProfiles is the resolver for the adminExpertProfiles field.
func (r *queryResolver) AdminExpertProfiles(ctx context.Context, filters any, page *int, pageSize *int) (*model.ExpertProfileListResponse, error) {
	panic("not implemented")
}

// AdminModerationQueue is the resolver for the adminModerationQueue field.
func (r *queryResolver) AdminModerationQueue(ctx context.Context, filters *model.ModerationFiltersInput, page *int, pageSize *int) (*model.ModerationQueue, error) {
	panic("not implemented")
}

// AdminModerationStats is the resolver for the adminModerationStats field.
func (r *queryResolver) AdminModerationStats(ctx context.Context) (*model.ModerationStats, error) {
	panic("not implemented")
}

// AdminAddonReviews is the resolver for the adminAddonReviews field.
func (r *queryResolver) AdminAddonReviews(ctx context.Context, filters *model.AddonReviewFiltersInput, page *int, pageSize *int) (*model.AddonReviewListResponse, error) {
	panic("not implemented")
}

// AdminAddonReview is the resolver for the adminAddonReview field.
func (r *queryResolver) AdminAddonReview(ctx context.Context, id string) (*model.AdminAddonReview, error) {
	panic("not implemented")
}

// AdminAddonReviewStats is the resolver for the adminAddonReviewStats field.
func (r *queryResolver) AdminAddonReviewStats(ctx context.Context) (*model.AddonReviewStats, error) {
	panic("not implemented")
}

// SystemHealth is the resolver for the systemHealth field.
func (r *queryResolver) SystemHealth(ctx context.Context) (*model.SystemHealth, error) {
	panic("not implemented")
}

// Mutation returns generated.MutationResolver implementation.
func (r *Resolver) Mutation() generated.MutationResolver { return &mutationResolver{r} }

// Query returns generated.QueryResolver implementation.
func (r *Resolver) Query() generated.QueryResolver { return &queryResolver{r} }

type mutationResolver struct{ *Resolver }
type queryResolver struct{ *Resolver }
