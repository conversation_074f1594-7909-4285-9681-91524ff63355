directive @auth on FIELD_DEFINITION

scalar Time
scalar JSON

type Tenant {
    id: ID!
    name: String!
    slug: String!
    domain: String
    status: String!
    planType: String!
    maxUsers: Int!
    maxSites: Int!
    settings: JSON!
    createdAt: Time!
    updatedAt: Time!
    stats: TenantStats
}

type TenantStats {
    userCount: Int!
    siteCount: Int!
    pageCount: Int!
    addonCount: Int!
}

type TenantLimits {
    usersWithinLimit: Boolean!
    sitesWithinLimit: Boolean!
}

input CreateTenantInput {
    name: String!
    slug: String!
    domain: String
    planType: String!
}

input UpdateTenantInput {
    name: String
    slug: String
    domain: String
    status: String
    planType: String
    maxUsers: Int
    maxSites: Int
    settings: JSON
}

type SubscriptionTier {
    id: ID!
    name: String!
    priceMonthly: Float!
    features: JSON!
    limits: JSON!
    isActive: Boolean!
    createdAt: Time!
    updatedAt: Time!
}

type UserSubscription {
    id: ID!
    userID: String!
    tier: SubscriptionTier!
    status: String!
    currentPeriodStart: Time!
    currentPeriodEnd: Time!
    stripeSubscriptionId: String
    createdAt: Time!
    updatedAt: Time!
}

type UsageTracking {
    id: ID!
    userID: String!
    resourceType: String!
    usageAmount: Int!
    periodStart: Time!
    periodEnd: Time!
    createdAt: Time!
}

type Invoice {
    id: ID!
    userID: String!
    subscriptionID: String!
    amount: Float!
    currency: String!
    status: String!
    dueDate: Time!
    paidAt: Time
    stripeInvoiceId: String
    downloadUrl: String
    createdAt: Time!
}

input CreateSubscriptionTierInput {
    name: String!
    priceMonthly: Float!
    features: JSON!
    limits: JSON!
    isActive: Boolean = true
}

input UpdateSubscriptionTierInput {
    name: String
    priceMonthly: Float
    features: JSON
    limits: JSON
    isActive: Boolean
}

input UpgradeSubscriptionInput {
    tierID: String!
    paymentMethodId: String
}

type OnboardingStep {
    id: ID!
    name: String!
    title: String!
    description: String!
    required: Boolean!
    order: Int!
    completed: Boolean!
    completedAt: Time
    data: JSON
}

type OnboardingProgress {
    userID: String!
    currentStep: Int!
    totalSteps: Int!
    completedSteps: Int!
    isCompleted: Boolean!
    completionRate: Float!
    steps: [OnboardingStep!]!
}

type UserProfile {
    userID: String!
    firstName: String
    lastName: String
    company: String
    businessType: String
    industry: String
    teamSize: String
    primaryGoal: String
    avatarUrl: String
    timezone: String!
    language: String!
}

type UserSettings {
    id: ID!
    userID: String!
    emailNotifications: Boolean!
    browserNotifications: Boolean!
    marketingEmails: Boolean!
    weeklyDigest: Boolean!
    securityAlerts: Boolean!
    theme: String!
    sidebarCollapsed: Boolean!
    defaultDashboardView: String!
    autoSave: Boolean!
    createdAt: Time!
    updatedAt: Time!
}

type DashboardStats {
    totalSites: Int!
    totalPages: Int!
    totalAddons: Int!
    currentUsage: JSON!
    subscriptionTier: String!
    storageUsed: Float!
    bandwidthUsed: Float!
    apiCallsUsed: Int!
    recentActivity: [ActivityItem!]!
}

type ActivityItem {
    id: ID!
    type: String!
    description: String!
    timestamp: Time!
    metadata: JSON
}

input UpdateUserProfileInput {
    firstName: String
    lastName: String
    company: String
    businessType: String
    industry: String
    teamSize: String
    primaryGoal: String
    avatarUrl: String
    timezone: String
    language: String
}

input UpdateUserSettingsInput {
    emailNotifications: Boolean
    browserNotifications: Boolean
    marketingEmails: Boolean
    weeklyDigest: Boolean
    securityAlerts: Boolean
    theme: String
    sidebarCollapsed: Boolean
    defaultDashboardView: String
    autoSave: Boolean
}

input CompleteOnboardingStepInput {
    stepName: String!
    data: JSON
}

type Site {
    id: ID!
    name: String!
    description: String
    userID: String!
    tenantID: String!
    status: String!
    customDomain: String
    sslEnabled: Boolean!
    themeConfig: JSON!
    seoConfig: JSON!
    createdAt: Time!
    updatedAt: Time!
    pageCount: Int!
    isPublished: Boolean!
    pages: [SitePage!]!
}

type SitePage {
    id: ID!
    siteID: String!
    pageID: String!
    isHomepage: Boolean!
    position: Int!
    tenantID: String!
    createdAt: Time!
    page: Page
}

type SiteNavigation {
    id: ID!
    siteID: String!
    pageID: String
    parentID: String
    title: String!
    url: String!
    position: Int!
    isExternal: Boolean!
    isVisible: Boolean!
    tenantID: String!
    createdAt: Time!
    updatedAt: Time!
}

input CreateSiteInput {
    name: String!
    description: String
    customDomain: String
    themeConfig: JSON
    seoConfig: JSON
}

input UpdateSiteInput {
    name: String
    description: String
    status: String
    customDomain: String
    sslEnabled: Boolean
    themeConfig: JSON
    seoConfig: JSON
}

input CreatePageInput {
    title: String!
    slug: String
    content: String = ""
    parentID: String
    isHidden: Boolean = false
    metaTitle: String
    metaDescription: String
    metaKeywords: String
    customCSS: String
    customJS: String
    featuredImage: String
    template: String = "default"
    settings: JSON
}

input UpdatePageInput {
    title: String
    slug: String
    content: String
    parentID: String
    position: Int
    isHidden: Boolean
    metaTitle: String
    metaDescription: String
    metaKeywords: String
    customCSS: String
    customJS: String
    featuredImage: String
    status: String
    template: String
    settings: JSON
}

input PublishPageInput {
    pageID: String!
    publishNow: Boolean = true
    scheduledAt: Time
}

input MovePageInput {
    pageID: String!
    newParentID: String
    newPosition: Int!
}

type PageContent {
    id: ID!
    pageID: String!
    content: JSON!
    version: Int!
    isPublished: Boolean!
    createdAt: Time!
    updatedAt: Time!
}

type MediaAsset {
    id: ID!
    filename: String!
    originalFilename: String!
    filePath: String!
    fileSize: Int!
    mimeType: String!
    altText: String
    isPublic: Boolean!
    createdAt: Time!
}

type SiteSettings {
    id: ID!
    siteName: String!
    siteDescription: String
    customDomain: String
    sslEnabled: Boolean!
    isPublished: Boolean!
    themeConfig: JSON!
    seoConfig: JSON!
}

input PageContentInput {
    pageID: String!
    content: JSON!
    isPublished: Boolean
}

input SiteSettingsInput {
    siteName: String!
    siteDescription: String
    customDomain: String
    themeConfig: JSON
    seoConfig: JSON
}

type CompilationResult {
    html: String!
    css: String!
    js: String!
    assets: JSON!
    metadata: CompilationMetadata!
    success: Boolean!
    message: String
}

type CompilationMetadata {
    compiledAt: Time!
    version: String!
    pageCount: Int!
    componentCount: Int!
    themeConfig: JSON!
}

type PublishResult {
    success: Boolean!
    message: String!
    publishedUrl: String
    deploymentId: String
    publishedAt: Time
}

input PublishSiteInput {
    domain: String
    generateApiKey: Boolean = false
    versionMessage: String
}

type PublishingHistory {
    id: ID!
    siteId: ID!
    version: String!
    versionMessage: String
    publishedUrl: String!
    deploymentId: String!
    status: String!
    publishedAt: Time!
    publishedBy: String!
    compilationData: JSON
}

type PublishingVersion {
    version: String!
    publishedAt: Time!
    message: String
    status: String!
    url: String!
}

# Expert System Types
type ExpertApplication {
    id: ID!
    userId: ID!
    tenantId: ID!
    applicationStatus: String!
    submittedAt: Time!
    reviewedAt: Time
    professionalTitle: String!
    yearsExperience: Int!
    expertiseAreas: [String!]!
    hourlyRate: Float
    currency: String!
    qualifications: JSON!
    motivationStatement: String
    portfolioUrl: String
    linkedinUrl: String
    websiteUrl: String
    adminNotes: String
    rejectionReason: String
    createdAt: Time!
    updatedAt: Time!
}

type ExpertProfile {
    id: ID!
    userId: ID!
    user: User
    tenantId: ID!
    profileStatus: String!
    professionalTitle: String!
    bio: String
    expertiseAreas: [String!]!
    yearsExperience: Int!
    hourlyRate: Float!
    currency: String!
    availabilityStatus: String!
    portfolioUrl: String
    linkedinUrl: String
    websiteUrl: String
    totalProjects: Int!
    completedProjects: Int!
    averageRating: Float!
    totalReviews: Int!
    acceptsNewProjects: Boolean!
    responseTimeHours: Int!
    specializations: [ExpertSpecialization!]!
    createdAt: Time!
    updatedAt: Time!
}

type ExpertSpecialization {
    id: ID!
    expertProfileId: ID!
    specializationName: String!
    proficiencyLevel: String!
    yearsExperience: Int!
    createdAt: Time!
}

input CreateExpertApplicationInput {
    professionalTitle: String!
    yearsExperience: Int!
    expertiseAreas: [String!]!
    hourlyRate: Float
    currency: String = "GBP"
    qualifications: JSON
    motivationStatement: String
    portfolioUrl: String
    linkedinUrl: String
    websiteUrl: String
    availabilityHours: Int = 20
    preferredProjectTypes: [String!]
}

input UpdateExpertProfileInput {
    professionalTitle: String
    bio: String
    expertiseAreas: [String!]
    hourlyRate: Float
    availabilityStatus: String
    portfolioUrl: String
    linkedinUrl: String
    websiteUrl: String
    acceptsNewProjects: Boolean
    responseTimeHours: Int
}

input ExpertSearchFilters {
    expertiseAreas: [String!]
    minRating: Float
    maxHourlyRate: Float
    availabilityStatus: String
    yearsExperience: Int
    searchTerm: String
}

# Expert-Client Matching System
type ClientRequirement {
    id: ID!
    userId: ID!
    tenantId: ID!
    projectTitle: String!
    projectDescription: String!
    projectType: String!
    requiredExpertise: [String!]!
    projectScope: String!
    budgetMin: Float
    budgetMax: Float
    currency: String!
    timelineWeeks: Int
    startDate: Time
    urgency: String!
    status: String!
    matches: [ExpertClientMatch!]!
    createdAt: Time!
    updatedAt: Time!
}

type ExpertClientMatch {
    id: ID!
    clientRequirementId: ID!
    expertProfileId: ID!
    expertProfile: ExpertProfile!
    compatibilityScore: Float!
    expertiseMatchScore: Float!
    budgetMatchScore: Float!
    availabilityMatchScore: Float!
    experienceMatchScore: Float!
    matchStatus: String!
    suggestedAt: Time!
    contactedAt: Time
    respondedAt: Time
    clientMessage: String
    expertResponse: String
    createdAt: Time!
    updatedAt: Time!
}

type ProjectEngagement {
    id: ID!
    clientRequirementId: ID!
    expertProfileId: ID!
    expertProfile: ExpertProfile!
    projectTitle: String!
    projectDescription: String!
    agreedScope: String
    agreedRate: Float!
    currency: String!
    rateType: String!
    estimatedHours: Int
    totalBudget: Float
    startDate: Time!
    endDate: Time
    estimatedDurationWeeks: Int
    engagementStatus: String!
    progressPercentage: Int!
    milestones: [ProjectMilestone!]!
    communications: [ProjectCommunication!]!
    createdAt: Time!
    updatedAt: Time!
}

type ProjectMilestone {
    id: ID!
    engagementId: ID!
    milestoneTitle: String!
    milestoneDescription: String
    milestoneOrder: Int!
    dueDate: Time!
    completedDate: Time
    milestoneValue: Float
    paymentStatus: String!
    milestoneStatus: String!
    completionNotes: String
    createdAt: Time!
    updatedAt: Time!
}

type ProjectCommunication {
    id: ID!
    engagementId: ID!
    senderId: ID!
    sender: User!
    messageType: String!
    subject: String
    messageContent: String!
    attachments: JSON!
    isRead: Boolean!
    readAt: Time
    priority: String!
    createdAt: Time!
}

input CreateClientRequirementInput {
    projectTitle: String!
    projectDescription: String!
    projectType: String!
    requiredExpertise: [String!]!
    projectScope: String!
    budgetMin: Float
    budgetMax: Float
    currency: String = "GBP"
    timelineWeeks: Int
    startDate: Time
    urgency: String = "normal"
}

input CreateProjectEngagementInput {
    clientRequirementId: ID!
    expertProfileId: ID!
    projectTitle: String!
    projectDescription: String!
    agreedScope: String
    agreedRate: Float!
    rateType: String = "hourly"
    estimatedHours: Int
    totalBudget: Float
    startDate: Time!
    endDate: Time
    estimatedDurationWeeks: Int
}

# Messaging System
type Conversation {
    id: ID!
    participants: [User!]!
    lastMessage: Message
    unreadCount: Int!
    createdAt: Time!
    updatedAt: Time!
}

type Message {
    id: ID!
    conversationId: ID!
    senderId: ID!
    sender: User!
    content: String!
    messageType: String!
    attachments: JSON!
    isRead: Boolean!
    readAt: Time
    sentAt: Time!
    editedAt: Time
}

input SendMessageInput {
    conversationId: ID!
    content: String!
    messageType: String = "text"
    attachments: JSON
}

# Review System
type ExpertReview {
    id: ID!
    expertProfileId: ID!
    expertProfile: ExpertProfile!
    clientUserId: ID!
    clientUser: User!
    projectEngagementId: ID
    overallRating: Float!
    communicationRating: Float
    expertiseRating: Float
    timelinessRating: Float
    valueRating: Float
    reviewTitle: String
    reviewText: String!
    pros: String
    cons: String
    wouldRecommend: Boolean!
    wouldHireAgain: Boolean!
    reviewStatus: String!
    isVerified: Boolean!
    expertResponse: String
    expertRespondedAt: Time
    helpfulVotes: Int!
    totalVotes: Int!
    createdAt: Time!
    updatedAt: Time!
}

input CreateReviewInput {
    expertProfileId: ID!
    projectEngagementId: ID
    overallRating: Float!
    communicationRating: Float
    expertiseRating: Float
    timelinessRating: Float
    valueRating: Float
    reviewTitle: String
    reviewText: String!
    pros: String
    cons: String
    wouldRecommend: Boolean = true
    wouldHireAgain: Boolean = true
}

# Compliance System
type ComplianceFramework {
    id: ID!
    frameworkName: String!
    frameworkCode: String!
    description: String
    regulatoryBody: String
    industrySector: String
    frameworkType: String!
    isMandatory: Boolean!
    effectiveDate: Time
    reviewFrequencyMonths: Int!
    requirements: [ComplianceRequirement!]!
    createdAt: Time!
    updatedAt: Time!
}

type ComplianceRequirement {
    id: ID!
    frameworkId: ID!
    requirementCode: String!
    requirementTitle: String!
    requirementDescription: String!
    complianceLevel: String!
    category: String
    subcategory: String
    evidenceRequired: [String!]!
    reviewFrequencyMonths: Int!
    penaltyDescription: String
    createdAt: Time!
    updatedAt: Time!
}

type BusinessCompliance {
    id: ID!
    tenantId: ID!
    userId: ID!
    frameworkId: ID!
    framework: ComplianceFramework!
    complianceStatus: String!
    assignedTo: User
    priorityLevel: String!
    targetCompletionDate: Time
    lastReviewDate: Time
    nextReviewDate: Time
    complianceAchievedDate: Time
    notes: String
    evidenceDocuments: JSON!
    actionItems: JSON!
    requirementTracking: [ComplianceRequirementTracking!]!
    alerts: [ComplianceAlert!]!
    createdAt: Time!
    updatedAt: Time!
}

type ComplianceRequirementTracking {
    id: ID!
    businessComplianceId: ID!
    requirementId: ID!
    requirement: ComplianceRequirement!
    requirementStatus: String!
    completionPercentage: Int!
    startedDate: Time
    completedDate: Time
    dueDate: Time
    evidenceProvided: JSON!
    implementationNotes: String
    assessorNotes: String
    riskLevel: String!
    impactDescription: String
    createdAt: Time!
    updatedAt: Time!
}

type ComplianceAlert {
    id: ID!
    tenantId: ID!
    businessComplianceId: ID
    requirementTrackingId: ID
    alertType: String!
    alertTitle: String!
    alertMessage: String!
    severity: String!
    alertStatus: String!
    acknowledgedBy: User
    acknowledgedAt: Time
    resolvedAt: Time
    triggerDate: Time!
    expiryDate: Time
    createdAt: Time!
}

input CreateBusinessComplianceInput {
    frameworkId: ID!
    assignedTo: ID
    priorityLevel: String = "medium"
    targetCompletionDate: Time
    notes: String
}

input UpdateComplianceStatusInput {
    complianceStatus: String!
    notes: String
    evidenceDocuments: JSON
    actionItems: JSON
}


type WebAddon {
    id: ID!
    name: String!
    description: String
    reteConfig: String!
    version: String!
    dependencies: String
    status: String!
    createdAt: String!
    updatedAt: String!
}

type PredefinedSnippet {
    id: ID!
    name: String!
    category: String!
    htmlContent: String!
    cssContent: String
    jsContent: String
    version: String!
    createdAt: String!
    updatedAt: String!
}

input WebAddonInput {
    name: String!
    description: String
    reteConfig: String!
    version: String!
    dependencies: String
}

input PredefinedSnippetInput {
    name: String!
    category: String!
    htmlContent: String!
    cssContent: String
    jsContent: String
    version: String!
}

type Query {
    health: String! @auth
    getAddonsInReview: [AddonConfig!]!
    
    # Addon testing queries
    getAddonTestSession(sessionId: ID!): AddonTestSession
    getAddonTestHistory(addonId: ID!): [AddonTestSession!]!
    vehicles: [Vehicle!]! @auth
    vehicleByVIN(vin: String!): Vehicle @auth
    webAddons(status: String): [WebAddon!]! @auth
    predefinedSnippets(category: String): [PredefinedSnippet!]! @auth
    component(id: ID!, type: ComponentType!): ComponentResult! @auth
    getNavigation: [Page!]!
    
    # Tenant queries (admin only)
    tenants(limit: Int = 25, offset: Int = 0, status: String): [Tenant!]! @auth
    tenant(id: ID!): Tenant @auth
    tenantStats(tenantId: ID!): TenantStats @auth
    tenantLimits(tenantId: ID!): TenantLimits @auth
    
    # Subscription queries
    subscriptionTiers: [SubscriptionTier!]!
    subscriptionTier(id: ID!): SubscriptionTier
    userSubscription: UserSubscription @auth
    userSubscriptionHistory: [UserSubscription!]! @auth
    currentUsage(resourceType: String!): Int! @auth
    usageHistory(resourceType: String, limit: Int = 10): [UsageTracking!]! @auth
    userInvoices(limit: Int = 10, offset: Int = 0): [Invoice!]! @auth
    invoice(id: ID!): Invoice @auth
    
    # Onboarding & Dashboard queries
    onboardingProgress: OnboardingProgress @auth
    userProfile: UserProfile @auth
    userSettings: UserSettings @auth
    dashboardStats: DashboardStats @auth
    
    # Site Management queries
    mySites: [Site!]! @auth
    site(id: ID!): Site @auth
    sitePages(siteId: ID!): [SitePage!]! @auth
    page(id: ID!): Page @auth
    pagesByParent(parentId: ID, siteId: ID!): [Page!]! @auth
    
    # Site Settings queries
    siteSettings(siteId: ID!): SiteSettings @auth
    
    # Publishing History queries
    sitePublishingHistory(siteId: ID!, limit: Int = 10): [PublishingHistory!]! @auth
    siteVersions(siteId: ID!): [PublishingVersion!]! @auth
    
    # Expert System queries
    expertApplications(status: String, limit: Int = 25): [ExpertApplication!]! @auth
    expertApplication(id: ID!): ExpertApplication @auth
    myExpertApplication: ExpertApplication @auth
    
    # Matching System queries
    clientRequirements(status: String, limit: Int = 25): [ClientRequirement!]! @auth
    clientRequirement(id: ID!): ClientRequirement @auth
    myClientRequirements: [ClientRequirement!]! @auth
    expertMatches(requirementId: ID!): [ExpertClientMatch!]! @auth
    myMatches: [ExpertClientMatch!]! @auth
    
    # Project Management queries
    projectEngagements(status: String, limit: Int = 25): [ProjectEngagement!]! @auth
    projectEngagement(id: ID!): ProjectEngagement @auth
    myProjectEngagements: [ProjectEngagement!]! @auth
    
    # Messaging queries
    myConversations: [Conversation!]! @auth
    conversation(id: ID!): Conversation @auth
    conversationMessages(conversationId: ID!, limit: Int = 50): [Message!]! @auth
    
    # External API Management queries
    externalAPIs(status: APIStatus): [ExternalAPI!]! @auth
    externalAPI(id: ID!): ExternalAPI @auth
    myAPICredentials(apiConfigId: ID): [APICredentials!]! @auth
    apiConnections(apiConfigId: ID): [APIConnection!]! @auth
    
    # User Management queries
    me: User @auth
    notifications(limit: Int = 20, unreadOnly: Boolean = false): [Notification!]! @auth
    
    # Site Management queries
    siteAnalytics(siteId: ID!, period: String = "30d"): SiteAnalytics @auth
    
    # Expert Network queries
    expertProfiles(filters: ExpertFiltersInput, limit: Int = 20, offset: Int = 0): [ExpertProfile!]!
    expertProfile(id: ID!): ExpertProfile
    myExpertProfile: ExpertProfile @auth
    projects(status: String, limit: Int = 20): [Project!]! @auth
    project(id: ID!): Project @auth
    
    # Support queries
    myTickets(status: String, limit: Int = 20): [SupportTicket!]! @auth
    ticket(id: ID!): SupportTicket @auth
    
    # Business Tools queries
    businessPlan: BusinessPlan @auth
    financialPlan: FinancialPlan @auth
    competitorResearch(industry: String, location: String): CompetitorResearch @auth
    complianceCheck(regulation: String!, industry: String, location: String): ComplianceCheck @auth
    
    # Admin Panel queries (admin only)
    adminDashboardKPIs: AdminDashboardKPIs! @auth
    adminUsers(filters: UserSearchFiltersInput, page: Int = 1, pageSize: Int = 25): UserListResponse! @auth
    adminUser(id: ID!): AdminUser @auth
    adminUserStats: UserStats! @auth
    adminExpertApplications(filters: ExpertApplicationFiltersInput, page: Int = 1, pageSize: Int = 25): ExpertApplicationListResponse! @auth
    adminExpertApplication(id: ID!): AdminExpertApplication @auth
    adminExpertProfiles(filters: JSON, page: Int = 1, pageSize: Int = 25): ExpertProfileListResponse! @auth
    adminModerationQueue(filters: ModerationFiltersInput, page: Int = 1, pageSize: Int = 25): ModerationQueue! @auth
    adminModerationStats: ModerationStats! @auth
    adminAddonReviews(filters: AddonReviewFiltersInput, page: Int = 1, pageSize: Int = 25): AddonReviewListResponse! @auth
    adminAddonReview(id: ID!): AdminAddonReview @auth
    adminAddonReviewStats: AddonReviewStats! @auth
    systemHealth: SystemHealth! @auth
}

input CreatePredefinedSnippetInput {
    name: String!
    category: String!
    description: String
    htmlContent: String!
    cssContent: String
    jsContent: String
    version: String!
}

type Page {
    id: ID!
    title: String!
    slug: String!
    content: String!
    parentId: ID
    parent: Page
    children: [Page!]!
    position: Int!
    isHidden: Boolean!
    createdAt: Time!
    updatedAt: Time!
    
    # Enhanced fields
    metaTitle: String!
    metaDescription: String!
    metaKeywords: String!
    customCSS: String!
    customJS: String!
    featuredImage: String!
    status: String!
    publishedAt: Time
    template: String!
    settings: JSON!
    contentVersion: Int!
    isPublished: Boolean!
}


input UpdatePredefinedSnippetInput {
    id: ID!
    name: String
    category: String
    description: String
    htmlContent: String
    cssContent: String
    jsContent: String
    version: String
}


union ComponentResult = WebAddon | PredefinedSnippet

enum ComponentType {
    ADDON
    SNIPPET
}

type Addon {
    id: ID!
    name: String!
    description: String
    version: String!
    status: AddonState!
    author: String!
    category: String!
    tags: [String!]!
    isPublic: Boolean!
    downloads: Int!
    rating: Float!
    reviewCount: Int!
    config: JSON
    state: AddonState!
    createdAt: Time!
    updatedAt: Time!
}

type AddonConfig {
    id: ID!
    name: String!
    config: String!
    state: AddonState!
    createdAt: Time!
    updatedAt: Time!
}

enum AddonState {
    DEVELOPMENT
    REVIEW
    PRODUCTION
    DISABLED
}

input AddonConfigInput {
    name: String!
    config: String!
}

input AddonMetadataInput {
    name: String!
    description: String!
    category: String!
    version: String!
    tags: [String!]!
    icon: String
    isPublic: Boolean
    properties: JSON
}

input AddonTestOptionsInput {
    timeout: Int!
    enablePerformance: Boolean!
    enableSecurity: Boolean!
    enableCompatibility: Boolean!
    testData: JSON
    mockServices: JSON
    environment: String
}

input CreateAddonInput {
    name: String!
    description: String
    version: String!
    category: String!
    tags: [String!]!
    isPublic: Boolean = true
    config: JSON
}

input UpdateAddonInput {
    name: String
    description: String
    version: String
    category: String
    tags: [String!]
    isPublic: Boolean
    config: JSON
}

type AddonReview {
    id: ID!
    addonID: String!
    userID: String!
    rating: Float!
    comment: String!
    createdAt: Time!
    updatedAt: Time!
}

input CreateAddonReviewInput {
    addonID: String!
    rating: Float!
    comment: String!
}

type AddonInstallation {
    id: ID!
    addonID: String!
    userID: String!
    siteID: String!
    status: String!
    installedAt: Time!
    updatedAt: Time!
}

type AddonMetadata {
    id: String!
    name: String!
    description: String!
    category: String!
    author: String!
    version: String!
    tags: [String!]!
    icon: String!
    isPublic: Boolean!
    createdAt: Time!
    updatedAt: Time!
    properties: JSON
}

type AddonPreviewData {
    generatedAt: Time!
    previewHTML: String!
    previewCSS: String!
    previewJS: String!
    screenshots: [String!]!
    demoData: JSON
}

type AddonTestSession {
    id: ID!
    addonId: String!
    status: AddonTestStatus!
    startedAt: Time!
    completedAt: Time
    results: AddonTestResults
    logs: [AddonTestLog!]!
}

type AddonTestResults {
    success: Boolean!
    executionTime: String!
    nodesExecuted: Int!
    nodesTotal: Int!
    errors: [AddonTestError!]!
    warnings: [AddonTestWarning!]!
    performance: AddonPerformanceMetrics
    securityChecks: AddonSecurityCheckResults
}

type AddonTestError {
    code: String!
    message: String!
    nodeId: String
    severity: String!
    timestamp: Time!
}

type AddonTestWarning {
    code: String!
    message: String!
    nodeId: String
    timestamp: Time!
}

type AddonTestLog {
    level: String!
    message: String!
    nodeId: String
    timestamp: Time!
}

type AddonPerformanceMetrics {
    cpuUsage: Float!
    memoryUsage: Int!
    networkCalls: Int!
    databaseQueries: Int!
    responseTime: String!
    throughput: Float!
}

type AddonSecurityCheckResults {
    passed: Boolean!
    sandboxViolations: [String!]!
    permissionIssues: [String!]!
    dataLeaks: [String!]!
    maliciousPatterns: [String!]!
}

enum AddonTestStatus {
    PENDING
    RUNNING
    COMPLETED
    FAILED
    TIMEDOUT
    CANCELLED
}

type Mutation {
    login(email: String!, password: String!): AuthPayload!
    updateAddonStatus(id: ID!, status: AddonState!): Addon!
    saveAddonConfig(input: AddonConfigInput!): AddonConfig!
    submitToReview(id: ID!): AddonConfig! @auth
    approveAddon(id: ID!): AddonConfig! @auth
    revertAddonToDevelopment(id: ID!): AddonConfig! @auth
    disableAddon(id: ID!): AddonConfig! @auth
    enableAddon(id: ID!): AddonConfig! @auth
    
    # Addon metadata management
    updateAddonMetadata(id: ID!, metadata: AddonMetadataInput!): AddonConfig! @auth
    
    # Addon preview system
    generateAddonPreview(id: ID!): AddonPreviewData! @auth
    getAddonPreview(id: ID!): AddonPreviewData @auth
    
    # Addon testing environment
    startAddonTest(id: ID!, options: AddonTestOptionsInput!): AddonTestSession! @auth
    register(email: String!, password: String!): AuthPayload!
    createVehicle(input: VehicleInput!): Vehicle! @auth
    updateVehicle(vin: String!, input: VehicleInput!): Vehicle! @auth
    deleteVehicle(vin: String!): Boolean! @auth
    generateSiteAPIKey(siteId: ID!, permissions: Int!): APIKey! @auth
    revokeAPIKey(keyId: ID!): Boolean! @auth
    rotateAPIKey(keyId: ID!): APIKey! @auth
    createWebAddon(input: WebAddonInput!): WebAddon! @auth
    updateWebAddon(id: ID!, input: WebAddonInput!): WebAddon! @auth
    createPredefinedSnippet(input: PredefinedSnippetInput!): PredefinedSnippet! @auth
    updatePredefinedSnippet(id: ID!, input: PredefinedSnippetInput!): PredefinedSnippet! @auth
    movePage(id: ID!, parentId: ID, position: Int!): Page!
    createSnippet(input: CreatePredefinedSnippetInput!): PredefinedSnippet! @auth
    updateSnippet(input: UpdatePredefinedSnippetInput!): PredefinedSnippet! @auth
    deleteSnippet(id: ID!): Boolean! @auth
    callExternalAPI(
        name: String!
        payload: String!
        headers: [HeaderInput!]
    ): String! @auth
    
    # External API Configuration Management
    createExternalAPI(input: CreateExternalAPIInput!): ExternalAPI! @auth
    updateExternalAPI(id: ID!, input: UpdateExternalAPIInput!): ExternalAPI! @auth
    deleteExternalAPI(id: ID!): Boolean! @auth
    
    # API Credentials Management
    createAPICredentials(input: CreateAPICredentialsInput!): APICredentials! @auth
    updateAPICredentials(id: ID!, input: UpdateAPICredentialsInput!): APICredentials! @auth
    deleteAPICredentials(id: ID!): Boolean! @auth
    
    # API Connection Testing
    testAPIConnection(input: TestAPIConnectionInput!): APIConnection! @auth
    
    # API Proxy Calls
    proxyAPICall(
        apiConfigId: ID!
        credentialsId: ID!
        endpoint: String!
        method: HTTPMethod!
        payload: JSON
        headers: [HeaderInput!]
    ): JSON! @auth
    
    # Admin Panel mutations (admin only)
    adminUpdateUserStatus(userId: ID!, status: String!, reason: String!): Boolean! @auth
    adminUpdateUserRole(userId: ID!, role: String!, reason: String!): Boolean! @auth
    adminAddUserNote(userId: ID!, note: String!): Boolean! @auth
    adminSuspendUser(userId: ID!, reason: String!): Boolean! @auth
    adminUnsuspendUser(userId: ID!, reason: String!): Boolean! @auth
    adminDeleteUser(userId: ID!, reason: String!): Boolean! @auth
    
    # Expert management mutations
    adminReviewExpertApplication(applicationId: ID!, notes: String!): Boolean! @auth
    adminApproveExpertApplication(applicationId: ID!, notes: String!): Boolean! @auth
    adminRejectExpertApplication(applicationId: ID!, reason: String!): Boolean! @auth
    
    # Content moderation mutations
    adminApproveContent(itemId: ID!, itemType: String!, notes: String!): Boolean! @auth
    adminRejectContent(itemId: ID!, itemType: String!, reason: String!): Boolean! @auth
    adminHideContent(itemId: ID!, itemType: String!, reason: String!): Boolean! @auth
    adminDeleteContent(itemId: ID!, itemType: String!, reason: String!): Boolean! @auth
    
    # Addon review mutations
    adminApproveAddon(addonId: ID!, notes: String!): Boolean! @auth
    adminRejectAddon(addonId: ID!, reason: String!): Boolean! @auth
    adminRequestAddonChanges(addonId: ID!, feedback: String!): Boolean! @auth
    
    # User Management mutations
    updateProfile(input: UpdateProfileInput!): UserProfile! @auth
    updateSettings(input: UpdateSettingsInput!): UserSettings! @auth
    changePassword(currentPassword: String!, newPassword: String!): Boolean! @auth
    uploadAvatar(file: Upload!): String! @auth
    deleteAccount(password: String!): Boolean! @auth
    
    # Site Management mutations
    createSite(input: CreateSiteInput!): Site! @auth
    updateSite(id: ID!, input: UpdateSiteInput!): Site! @auth
    deleteSite(id: ID!): Boolean! @auth
    publishSite(id: ID!): Site! @auth
    unpublishSite(id: ID!): Site! @auth
    
    # Page Management mutations
    createPage(input: CreatePageInput!): Page! @auth
    updatePage(id: ID!, input: UpdatePageInput!): Page! @auth
    deletePage(id: ID!): Boolean! @auth
    publishPage(id: ID!): Page! @auth
    unpublishPage(id: ID!): Page! @auth
    
    # Site Settings mutations
    updateSiteSettings(siteId: ID!, input: UpdateSiteSettingsInput!): SiteSettings! @auth
    
    # Expert Network mutations
    submitExpertApplication(input: ExpertApplicationInput!): ExpertApplication! @auth
    updateExpertProfile(input: UpdateExpertProfileInput!): ExpertProfile! @auth
    addPortfolioItem(input: PortfolioItemInput!): PortfolioItem! @auth
    removePortfolioItem(id: ID!): Boolean! @auth
    addCertification(input: CertificationInput!): Certification! @auth
    removeCertification(id: ID!): Boolean! @auth
    
    # Project Management mutations
    createProject(input: CreateProjectInput!): Project! @auth
    updateProject(id: ID!, input: UpdateProjectInput!): Project! @auth
    acceptProject(id: ID!): Project! @auth
    completeProject(id: ID!): Project! @auth
    addProjectMessage(projectId: ID!, content: String!, attachments: [Upload!]): ProjectMessage! @auth
    
    # Review mutations
    createExpertReview(input: CreateExpertReviewInput!): ExpertReview! @auth
    updateExpertReview(id: ID!, input: UpdateExpertReviewInput!): ExpertReview! @auth
    
    # Support mutations
    createSupportTicket(input: CreateSupportTicketInput!): SupportTicket! @auth
    updateSupportTicket(id: ID!, input: UpdateSupportTicketInput!): SupportTicket! @auth
    addTicketMessage(ticketId: ID!, content: String!, attachments: [Upload!]): SupportMessage! @auth
    closeSupportTicket(id: ID!): SupportTicket! @auth
    
    # Notification mutations
    markNotificationRead(id: ID!): Notification! @auth
    markAllNotificationsRead: Boolean! @auth
    deleteNotification(id: ID!): Boolean! @auth
    
    # Business Tools mutations
    createBusinessPlan(input: CreateBusinessPlanInput!): BusinessPlan! @auth
    updateBusinessPlan(id: ID!, input: UpdateBusinessPlanInput!): BusinessPlan! @auth
    updateBusinessPlanSection(id: ID!, input: UpdateBusinessPlanSectionInput!): BusinessPlanSection! @auth
    createFinancialPlan(input: CreateFinancialPlanInput!): FinancialPlan! @auth
    updateFinancialPlan(id: ID!, input: UpdateFinancialPlanInput!): FinancialPlan! @auth
    createComplianceCheck(input: CreateComplianceCheckInput!): ComplianceCheck! @auth
    
    # Tenant mutations (admin only)
    createTenant(input: CreateTenantInput!): Tenant! @auth
    updateTenant(id: ID!, input: UpdateTenantInput!): Tenant! @auth
    deleteTenant(id: ID!): Boolean! @auth
    
    # Subscription mutations
    createSubscriptionTier(input: CreateSubscriptionTierInput!): SubscriptionTier! @auth
    updateSubscriptionTier(id: ID!, input: UpdateSubscriptionTierInput!): SubscriptionTier! @auth
    deleteSubscriptionTier(id: ID!): Boolean! @auth
    upgradeSubscription(input: UpgradeSubscriptionInput!): UserSubscription! @auth
    downgradeSubscription(tierID: String!): UserSubscription! @auth
    cancelSubscription: Boolean! @auth
    trackUsage(resourceType: String!, amount: Int!): Boolean! @auth
    generateInvoice(subscriptionID: String!): Invoice! @auth
    
    # Onboarding & Dashboard mutations
    completeOnboardingStep(input: CompleteOnboardingStepInput!): OnboardingProgress! @auth
    updateUserProfile(input: UpdateUserProfileInput!): UserProfile! @auth
    updateUserSettings(input: UpdateUserSettingsInput!): UserSettings! @auth
    
    # Site Management mutations
    duplicatePage(pageID: String!, newTitle: String): Page! @auth
    
    # Site Settings mutations
    getSiteSettings(siteId: ID!): SiteSettings @auth
    
    # Site Compilation mutations
    compileSite(siteId: ID!): CompilationResult! @auth
    
    # Expert System mutations
    createExpertApplication(input: CreateExpertApplicationInput!): ExpertApplication! @auth
    reviewExpertApplication(id: ID!, status: String!, notes: String): ExpertApplication! @auth
    
    # Matching System mutations
    createClientRequirement(input: CreateClientRequirementInput!): ClientRequirement! @auth
    findExpertMatches(requirementId: ID!): [ExpertClientMatch!]! @auth
    contactExpert(matchId: ID!, message: String!): ExpertClientMatch! @auth
    respondToMatch(matchId: ID!, response: String!, accept: Boolean!): ExpertClientMatch! @auth
    
    # Project Management mutations
    createProjectEngagement(input: CreateProjectEngagementInput!): ProjectEngagement! @auth
    updateEngagementStatus(id: ID!, status: String!): ProjectEngagement! @auth
    addProjectMilestone(engagementId: ID!, title: String!, description: String, dueDate: Time!, value: Float): ProjectMilestone! @auth
    completeProjectMilestone(id: ID!, notes: String): ProjectMilestone! @auth
    
    # Messaging mutations
    createConversation(participantIds: [ID!]!): Conversation! @auth
    sendMessage(input: SendMessageInput!): Message! @auth
    markMessageAsRead(messageId: ID!): Message! @auth
    markConversationAsRead(conversationId: ID!): Boolean! @auth
    updateMilestoneProgress(id: ID!, progressPercentage: Int!, notes: String): ProjectMilestone! @auth
}

type ExternalAPI {
    id: ID!
    name: String!
    description: String
    baseUrl: String!
    version: String
    authenticationType: AuthType!
    endpoints: [APIEndpoint!]!
    headers: JSON
    rateLimit: RateLimitConfig
    security: APISecurityConfig
    documentation: String
    status: APIStatus!
    cacheTTL: Int
    createdAt: Time!
    updatedAt: Time!
    metadata: JSON
}

type APIEndpoint {
    id: ID!
    path: String!
    method: HTTPMethod!
    description: String
    parameters: [APIParameter!]!
    headers: JSON
    rateLimit: RateLimitConfig
    timeout: Int
    retries: Int
}

type APIParameter {
    name: String!
    type: String!
    required: Boolean!
    default: String
    description: String
    validation: String
}

type APISecurityConfig {
    requireHttps: Boolean!
    allowedDomains: [String!]
    blockedDomains: [String!]
    validateSsl: Boolean!
    maxRequestSize: Int!
    allowedMethods: [HTTPMethod!]!
}

type APICredentials {
    id: ID!
    name: String!
    apiConfigId: ID!
    authType: AuthType!
    isActive: Boolean!
    expiresAt: Time
    lastUsed: Time
    createdAt: Time!
    updatedAt: Time!
}

type APIConnection {
    id: ID!
    apiConfigId: ID!
    credentialsId: ID!
    status: ConnectionStatus!
    lastPing: Time!
    responseTime: Int!
    errorCount: Int!
    successCount: Int!
    totalRequests: Int!
    createdAt: Time!
}

enum HTTPMethod {
    GET
    POST
    PUT
    DELETE
    PATCH
    HEAD
    OPTIONS
}

enum APIStatus {
    ACTIVE
    INACTIVE
    DEPRECATED
    MAINTENANCE
}

enum ConnectionStatus {
    CONNECTED
    DISCONNECTED
    ERROR
    TESTING
}

input HeaderInput {
    key: String!
    value: String!
}

input CreateExternalAPIInput {
    name: String!
    description: String
    baseUrl: String!
    version: String
    authenticationType: AuthType!
    endpoints: [CreateAPIEndpointInput!]!
    headers: JSON
    rateLimit: RateLimitConfigInput
    security: APISecurityConfigInput
    documentation: String
    cacheTTL: Int
    metadata: JSON
}

input UpdateExternalAPIInput {
    name: String
    description: String
    baseUrl: String
    version: String
    authenticationType: AuthType
    endpoints: [CreateAPIEndpointInput!]
    headers: JSON
    rateLimit: RateLimitConfigInput
    security: APISecurityConfigInput
    documentation: String
    status: APIStatus
    cacheTTL: Int
    metadata: JSON
}

input CreateAPIEndpointInput {
    path: String!
    method: HTTPMethod!
    description: String
    parameters: [APIParameterInput!]!
    headers: JSON
    rateLimit: RateLimitConfigInput
    timeout: Int
    retries: Int
}

input APIParameterInput {
    name: String!
    type: String!
    required: Boolean!
    default: String
    description: String
    validation: String
}

input APISecurityConfigInput {
    requireHttps: Boolean!
    allowedDomains: [String!]
    blockedDomains: [String!]
    validateSsl: Boolean!
    maxRequestSize: Int!
    allowedMethods: [HTTPMethod!]!
}

input RateLimitConfigInput {
    requestsPerMinute: Int!
    burst: Int!
}

input CreateAPICredentialsInput {
    name: String!
    apiConfigId: ID!
    authType: AuthType!
    credentials: JSON!
    expiresAt: Time
}

input UpdateAPICredentialsInput {
    name: String
    credentials: JSON
    isActive: Boolean
    expiresAt: Time
}

input TestAPIConnectionInput {
    apiConfigId: ID!
    credentialsId: ID!
    endpoint: String
    testData: JSON
}

type RateLimitConfig {
    requestsPerMinute: Int!
    burst: Int!
}

enum AuthType {
    NONE
    API_KEY
    OAUTH2
    BEARER
}

type APIKey {
    id: ID!
    siteId: ID!
    key: String!
    permissions: Int!
    expiresAt: String!
    revoked: Boolean!
    createdAt: String!
    updatedAt: String!
}

type Vehicle {
    vin: String!
    make: String!
    model: String!
    year: Int!
    owner: User!
    createdAt: String!
    updatedAt: String!
}

input VehicleInput {
    vin: String!
    make: String!
    model: String!
    year: Int!
}

type AuthPayload {
    token: String!
    user: User!
}

type User {
    id: ID!
    email: String!
    role: String!
    status: String!
    firstName: String
    lastName: String
    company: String
    emailVerified: Boolean!
    lastLoginAt: Time
    createdAt: Time!
    updatedAt: Time!
    profile: UserProfile
    settings: UserSettings
    subscription: UserSubscription
    notes: String
}

# User Profile and Settings Types - using the definition from above
# type UserProfile defined above

# type UserSettings defined above

type NotificationSettings {
    email: Boolean!
    push: Boolean!
    sms: Boolean!
    marketing: Boolean!
    updates: Boolean!
    security: Boolean!
}

type PrivacySettings {
    profileVisibility: String!
    showEmail: Boolean!
    showLocation: Boolean!
    allowMessages: Boolean!
    allowConnections: Boolean!
}

type AppearanceSettings {
    theme: String!
    language: String!
    timezone: String!
    dateFormat: String!
    currency: String!
}

type SocialLink {
    platform: String!
    url: String!
    verified: Boolean!
}

# UserSubscription defined above

# SubscriptionTier defined above

# Site Management Types
# Site defined above

# Page defined above

type PageComponent {
    id: ID!
    pageId: ID!
    type: String!
    name: String!
    config: JSON!
    position: Int!
    isVisible: Boolean!
    createdAt: Time!
    updatedAt: Time!
}

# SiteSettings defined above

type GeneralSettings {
    title: String!
    tagline: String
    description: String
    language: String!
    timezone: String!
    dateFormat: String!
    timeFormat: String!
}

type SEOSettings {
    metaTitle: String
    metaDescription: String
    keywords: [String!]
    ogImage: String
    twitterCard: String
    robotsTxt: String
    sitemap: Boolean!
}

type SocialSettings {
    facebook: String
    twitter: String
    instagram: String
    linkedin: String
    youtube: String
    github: String
}

type AnalyticsSettings {
    googleAnalytics: String
    googleTagManager: String
    facebookPixel: String
    hotjar: String
    customScripts: [String!]
}

type SecuritySettings {
    forceHttps: Boolean!
    enableCaptcha: Boolean!
    allowedDomains: [String!]
    blockedIps: [String!]
    passwordProtected: Boolean!
}

type PerformanceSettings {
    caching: Boolean!
    compression: Boolean!
    minification: Boolean!
    lazyLoading: Boolean!
    cdnEnabled: Boolean!
}

type SiteCollaborator {
    id: ID!
    siteId: ID!
    user: User!
    role: String!
    permissions: [String!]!
    invitedBy: User!
    invitedAt: Time!
    acceptedAt: Time
    status: String!
}

type SiteAnalytics {
    id: ID!
    siteId: ID!
    pageViews: Int!
    uniqueVisitors: Int!
    bounceRate: Float!
    averageSessionDuration: Int!
    topPages: [PageAnalytics!]!
    trafficSources: [TrafficSource!]!
    deviceStats: DeviceStats!
    locationStats: [LocationStat!]!
    period: String!
    startDate: Time!
    endDate: Time!
}

type PageAnalytics {
    pageId: ID!
    page: Page!
    views: Int!
    uniqueViews: Int!
    bounceRate: Float!
    averageTime: Int!
}

type TrafficSource {
    source: String!
    medium: String!
    campaign: String
    visitors: Int!
    percentage: Float!
}

type DeviceStats {
    desktop: Int!
    mobile: Int!
    tablet: Int!
    browsers: [BrowserStat!]!
    operatingSystems: [OSStat!]!
}

type BrowserStat {
    browser: String!
    version: String!
    count: Int!
    percentage: Float!
}

type OSStat {
    os: String!
    version: String!
    count: Int!
    percentage: Float!
}

type LocationStat {
    country: String!
    countryCode: String!
    city: String
    visitors: Int!
    percentage: Float!
}

# Expert Network Types
# ExpertProfile defined above

# ExpertApplication defined above

type PortfolioItem {
    id: ID!
    title: String!
    description: String!
    url: String
    imageUrl: String
    technologies: [String!]!
    year: Int!
    category: String
    featured: Boolean!
}

type Certification {
    id: ID!
    name: String!
    issuer: String!
    issueDate: Time!
    expiryDate: Time
    credentialId: String
    url: String
    verified: Boolean!
}

type Education {
    id: ID!
    institution: String!
    degree: String!
    fieldOfStudy: String!
    startYear: Int!
    endYear: Int
    gpa: String
    description: String
}

type Skill {
    id: ID!
    name: String!
    category: String!
    level: String!
    yearsOfExperience: Int!
    endorsed: Boolean!
    endorsements: Int!
}

# ExpertReview defined above

type Project {
    id: ID!
    title: String!
    description: String!
    status: String!
    client: User!
    expert: ExpertProfile
    category: String!
    budget: Float
    hourlyRate: Float
    estimatedHours: Int
    actualHours: Int
    startDate: Time
    endDate: Time
    completedAt: Time
    deliverables: [Deliverable!]!
    milestones: [Milestone!]!
    messages: [ProjectMessage!]!
    attachments: [ProjectAttachment!]!
    createdAt: Time!
    updatedAt: Time!
}

type Deliverable {
    id: ID!
    projectId: ID!
    title: String!
    description: String!
    status: String!
    dueDate: Time
    completedAt: Time
    attachments: [String!]
    feedback: String
}

type Milestone {
    id: ID!
    projectId: ID!
    title: String!
    description: String!
    amount: Float!
    status: String!
    dueDate: Time
    completedAt: Time
    paidAt: Time
}

type ProjectMessage {
    id: ID!
    projectId: ID!
    sender: User!
    content: String!
    attachments: [ProjectAttachment!]
    isRead: Boolean!
    createdAt: Time!
}

type ProjectAttachment {
    id: ID!
    filename: String!
    fileSize: Int!
    mimeType: String!
    url: String!
    uploadedBy: User!
    uploadedAt: Time!
}

# Support System Types
type SupportTicket {
    id: ID!
    subject: String!
    description: String!
    status: String!
    priority: String!
    category: String!
    user: User!
    assignedTo: User
    assignedBy: User
    assignedAt: Time
    firstResponse: Time
    resolvedAt: Time
    closedAt: Time
    tags: [String!]!
    messages: [SupportMessage!]!
    attachments: [SupportAttachment!]!
    sla: SLAInfo
    createdAt: Time!
    updatedAt: Time!
}

type SupportMessage {
    id: ID!
    ticketId: ID!
    author: User!
    authorType: String!
    content: String!
    isInternal: Boolean!
    attachments: [SupportAttachment!]
    createdAt: Time!
}

type SupportAttachment {
    id: ID!
    filename: String!
    fileSize: Int!
    mimeType: String!
    url: String!
    uploadedBy: User!
    uploadedAt: Time!
}

type SLAInfo {
    responseTime: Int!
    resolutionTime: Int!
    isBreached: Boolean!
    timeRemaining: Int!
    breachReason: String
}

# Notification Types
type Notification {
    id: ID!
    user: User!
    type: String!
    title: String!
    message: String!
    data: JSON
    isRead: Boolean!
    readAt: Time
    actionUrl: String
    createdAt: Time!
    expiresAt: Time
}

# Business Tools Types
type BusinessPlan {
    id: ID!
    userId: ID!
    name: String!
    description: String
    status: String!
    sections: [BusinessPlanSection!]!
    createdAt: Time!
    updatedAt: Time!
}

type BusinessPlanSection {
    id: ID!
    type: String!
    title: String!
    content: String!
    order: Int!
    isComplete: Boolean!
}

type FinancialPlan {
    id: ID!
    userId: ID!
    name: String!
    revenueProjections: [RevenueProjection!]!
    expenseProjections: [ExpenseProjection!]!
    fundingRequirements: [FundingRequirement!]!
    createdAt: Time!
    updatedAt: Time!
}

type RevenueProjection {
    id: ID!
    year: Int!
    month: Int!
    amount: Float!
    source: String!
}

type ExpenseProjection {
    id: ID!
    year: Int!
    month: Int!
    amount: Float!
    category: String!
}

type FundingRequirement {
    id: ID!
    amount: Float!
    purpose: String!
    timeline: String!
    type: String!
    description: String
}

type CompetitorResearch {
    id: ID!
    userId: ID!
    industry: String
    location: String
    competitors: [Competitor!]!
    marketAnalysis: MarketAnalysis!
    createdAt: Time!
    updatedAt: Time!
}

type Competitor {
    id: ID!
    name: String!
    website: String
    description: String
    strengths: [String!]!
    weaknesses: [String!]!
    marketShare: Float
    revenue: Float
    employees: Int
    founded: Int
}

type MarketAnalysis {
    totalMarketSize: Float!
    growthRate: Float!
    keyTrends: [String!]!
    opportunities: [String!]!
    threats: [String!]!
    barriersToEntry: [String!]!
}

type ComplianceCheck {
    id: ID!
    userId: ID!
    regulation: String!
    industry: String
    location: String
    requirements: [ComplianceRequirement!]!
    complianceScore: Float!
    recommendations: [String!]!
    createdAt: Time!
    updatedAt: Time!
}

# ComplianceRequirement defined above

# Admin Panel Types
type AdminDashboardKPIs {
    totalUsers: Int!
    activeUsers: Int!
    newUsersToday: Int!
    newUsersThisWeek: Int!
    newUsersThisMonth: Int!
    totalExperts: Int!
    activeExperts: Int!
    pendingExpertApps: Int!
    expertApprovalRate: Float!
    totalSites: Int!
    publishedSites: Int!
    totalPages: Int!
    totalAddons: Int!
    addonsInReview: Int!
    totalRevenue: Float!
    monthlyRevenue: Float!
    averageRevenuePerUser: Float!
    systemUptime: String!
    databaseSize: String!
    storageUsed: String!
    apiCallsToday: Int!
    errorRate: Float!
    recentActivity: [AdminActivityItem!]!
    topPerformingAddons: [AddonPerformance!]!
    topActiveUsers: [UserActivity!]!
    lastUpdated: Time!
}

type AdminActivityItem {
    id: ID!
    type: String!
    description: String!
    userEmail: String!
    timestamp: Time!
    severity: String!
}

type AddonPerformance {
    id: ID!
    name: String!
    author: String!
    downloads: Int!
    rating: Float!
    revenue: Float!
    activeUsers: Int!
}

type UserActivity {
    userID: ID!
    email: String!
    role: String!
    lastActive: Time!
    sitesCreated: Int!
    pagesCreated: Int!
    apiCallsUsed: Int!
}

type AdminUser {
    id: ID!
    email: String!
    role: String!
    status: String!
    firstName: String!
    lastName: String!
    company: String!
    tenantID: ID!
    tenantName: String!
    emailVerified: Boolean!
    lastLoginAt: Time
    createdAt: Time!
    updatedAt: Time!
    subscriptionTier: String!
    sitesCount: Int!
    pagesCount: Int!
    addonsCount: Int!
    storageUsed: Int!
    apiCallsUsed: Int!
    totalRevenue: Float!
    flags: [String!]!
    notes: String!
}

type UserListResponse {
    users: [AdminUser!]!
    total: Int!
    page: Int!
    pageSize: Int!
    totalPages: Int!
}

type UserStats {
    byRole: JSON!
    byStatus: JSON!
    newUsersLast30Days: Int!
    activeUsersLast30Days: Int!
}

type AdminExpertApplication {
    id: ID!
    userID: ID!
    userEmail: String!
    firstName: String!
    lastName: String!
    company: String!
    status: String!
    expertiseAreas: [String!]!
    yearsOfExperience: Int!
    hourlyRate: Float!
    currency: String!
    bio: String!
    coverLetter: String!
    reviewNotes: String!
    reviewedBy: String!
    reviewedAt: Time
    approvedBy: String!
    approvedAt: Time
    rejectionReason: String!
    createdAt: Time!
    updatedAt: Time!
    score: Float!
    flags: [String!]!
}

type ExpertApplicationListResponse {
    applications: [AdminExpertApplication!]!
    total: Int!
    page: Int!
    pageSize: Int!
    totalPages: Int!
}

type ExpertProfileListResponse {
    profiles: [AdminExpertProfile!]!
    total: Int!
    page: Int!
    pageSize: Int!
    totalPages: Int!
}

type AdminExpertProfile {
    id: ID!
    userID: ID!
    userEmail: String!
    firstName: String!
    lastName: String!
    company: String!
    status: String!
    tier: String!
    expertiseAreas: [String!]!
    yearsOfExperience: Int!
    hourlyRate: Float!
    currency: String!
    rating: Float!
    reviewCount: Int!
    completedProjects: Int!
    activeProjects: Int!
    totalEarnings: Float!
    verificationStatus: String!
    createdAt: Time!
    updatedAt: Time!
    flags: [String!]!
    notes: String!
}

type ModerationQueue {
    items: [ModerationItem!]!
    total: Int!
    page: Int!
    pageSize: Int!
    totalPages: Int!
}

type ModerationItem {
    id: ID!
    type: String!
    title: String!
    content: String!
    authorID: ID!
    authorEmail: String!
    status: String!
    flags: [ModerationFlag!]!
    reports: [ModerationReport!]!
    reviewNotes: String!
    reviewedBy: String!
    reviewedAt: Time
    createdAt: Time!
    updatedAt: Time!
    riskScore: Float!
    autoModerated: Boolean!
    url: String!
}

type ModerationFlag {
    id: ID!
    type: String!
    severity: String!
    description: String!
    reportedBy: String!
    reportedAt: Time!
    status: String!
}

type ModerationReport {
    id: ID!
    reporterID: ID!
    reason: String!
    description: String!
    evidence: [String!]!
    status: String!
    createdAt: Time!
}

type ModerationStats {
    totalPending: Int!
    highRiskItems: Int!
    autoModeratedToday: Int!
    byType: JSON!
    byStatus: JSON!
    averageReviewTime: Float!
}

type AddonReviewListResponse {
    addons: [AdminAddonReview!]!
    total: Int!
    page: Int!
    pageSize: Int!
    totalPages: Int!
}

type AdminAddonReview {
    id: ID!
    name: String!
    description: String!
    version: String!
    authorID: ID!
    authorEmail: String!
    authorName: String!
    state: String!
    category: String!
    tags: [String!]!
    config: String!
    dependencies: [String!]!
    permissions: [String!]!
    securityScore: Float!
    qualityScore: Float!
    overallScore: Float!
    reviewNotes: String!
    reviewedBy: String!
    reviewedAt: Time
    approvedBy: String!
    approvedAt: Time
    rejectionReason: String!
    securityFlags: [SecurityFlag!]!
    qualityFlags: [QualityFlag!]!
    testResults: [TestResult!]!
    performanceMetrics: PerformanceMetrics!
    createdAt: Time!
    updatedAt: Time!
    submittedAt: Time
    previewURL: String!
    downloadCount: Int!
    rating: Float!
    reviewCount: Int!
    revenueGenerated: Float!
    installationCount: Int!
    uninstallationCount: Int!
    errorReports: Int!
    supportTickets: Int!
}

type SecurityFlag {
    id: ID!
    type: String!
    severity: String!
    description: String!
    location: String!
    suggestion: String!
    autoDetected: Boolean!
    status: String!
    createdAt: Time!
}

type QualityFlag {
    id: ID!
    type: String!
    severity: String!
    description: String!
    location: String!
    suggestion: String!
    autoDetected: Boolean!
    status: String!
    createdAt: Time!
}

type TestResult {
    id: ID!
    testType: String!
    status: String!
    score: Float!
    details: String!
    duration: Int!
    runAt: Time!
}

type PerformanceMetrics {
    loadTime: Int!
    memoryUsage: Int!
    cpuUsage: Float!
    networkRequests: Int!
    domNodes: Int!
    bundleSize: Int!
    dependencies: Int!
    complexity: Float!
}

type AddonReviewStats {
    pendingReview: Int!
    inDevelopment: Int!
    approved: Int!
    rejected: Int!
    byState: JSON!
    averageReviewTimeDays: Float!
    highSecurityRiskCount: Int!
    lowQualityCount: Int!
}

type SystemHealth {
    status: String!
    timestamp: Time!
    services: JSON!
}

# Admin Input Types
input UserSearchFiltersInput {
    email: String
    role: String
    status: String
    tenantID: String
    subscriptionTier: String
    createdAfter: Time
    createdBefore: Time
    lastActiveAfter: Time
    hasFlags: [String!]
    minRevenue: Float
    maxRevenue: Float
}

input ExpertApplicationFiltersInput {
    status: String
    expertiseArea: String
    minExperience: Int
    maxExperience: Int
    minHourlyRate: Float
    maxHourlyRate: Float
    submittedAfter: Time
    submittedBefore: Time
    reviewedBy: String
    hasFlags: [String!]
    minScore: Float
}

input ModerationFiltersInput {
    type: String
    status: String
    flagType: String
    minRiskScore: Float
    maxRiskScore: Float
    authorID: String
    reportedAfter: Time
    reportedBefore: Time
    autoModerated: Boolean
    hasReports: Boolean
}

input AddonReviewFiltersInput {
    state: String
    category: String
    authorID: String
    minSecurityScore: Float
    maxSecurityScore: Float
    minQualityScore: Float
    maxQualityScore: Float
    hasSecurityFlags: Boolean
    hasQualityFlags: Boolean
    submittedAfter: Time
    submittedBefore: Time
    reviewedBy: String
    priority: String
}

# User Management Input Types
input UpdateProfileInput {
    firstName: String
    lastName: String
    company: String
    bio: String
    website: String
    location: String
    timezone: String
    language: String
    socialLinks: [SocialLinkInput!]
}

input SocialLinkInput {
    platform: String!
    url: String!
}

input UpdateSettingsInput {
    notifications: NotificationSettingsInput
    privacy: PrivacySettingsInput
    appearance: AppearanceSettingsInput
}

input NotificationSettingsInput {
    email: Boolean
    push: Boolean
    sms: Boolean
    marketing: Boolean
    updates: Boolean
    security: Boolean
}

input PrivacySettingsInput {
    profileVisibility: String
    showEmail: Boolean
    showLocation: Boolean
    allowMessages: Boolean
    allowConnections: Boolean
}

input AppearanceSettingsInput {
    theme: String
    language: String
    timezone: String
    dateFormat: String
    currency: String
}

# Site Management Input Types
# CreateSiteInput defined above

# UpdateSiteInput defined above

# CreatePageInput defined above

# UpdatePageInput defined above

input UpdateSiteSettingsInput {
    general: GeneralSettingsInput
    seo: SEOSettingsInput
    social: SocialSettingsInput
    analytics: AnalyticsSettingsInput
    security: SecuritySettingsInput
    performance: PerformanceSettingsInput
}

input GeneralSettingsInput {
    title: String
    tagline: String
    description: String
    language: String
    timezone: String
    dateFormat: String
    timeFormat: String
}

input SEOSettingsInput {
    metaTitle: String
    metaDescription: String
    keywords: [String!]
    ogImage: String
    twitterCard: String
    robotsTxt: String
    sitemap: Boolean
}

input SocialSettingsInput {
    facebook: String
    twitter: String
    instagram: String
    linkedin: String
    youtube: String
    github: String
}

input AnalyticsSettingsInput {
    googleAnalytics: String
    googleTagManager: String
    facebookPixel: String
    hotjar: String
    customScripts: [String!]
}

input SecuritySettingsInput {
    forceHttps: Boolean
    enableCaptcha: Boolean
    allowedDomains: [String!]
    blockedIps: [String!]
    passwordProtected: Boolean
}

input PerformanceSettingsInput {
    caching: Boolean
    compression: Boolean
    minification: Boolean
    lazyLoading: Boolean
    cdnEnabled: Boolean
}

# Expert Network Input Types
input ExpertFiltersInput {
    expertiseAreas: [String!]
    minHourlyRate: Float
    maxHourlyRate: Float
    minRating: Float
    availability: String
    languages: [String!]
    tier: String
    location: String
}

input ExpertApplicationInput {
    expertiseAreas: [String!]!
    yearsOfExperience: Int!
    hourlyRate: Float!
    currency: String!
    bio: String!
    portfolio: [PortfolioItemInput!]
    certifications: [CertificationInput!]
    education: [EducationInput!]
    languages: [String!]!
    availability: String
    preferredProjectTypes: [String!]
    linkedinUrl: String
    websiteUrl: String
    resumeUrl: String
    coverLetter: String!
}

# UpdateExpertProfileInput defined above

input PortfolioItemInput {
    title: String!
    description: String!
    url: String
    imageUrl: String
    technologies: [String!]!
    year: Int!
    category: String
    featured: Boolean
}

input CertificationInput {
    name: String!
    issuer: String!
    issueDate: Time!
    expiryDate: Time
    credentialId: String
    url: String
}

input EducationInput {
    institution: String!
    degree: String!
    fieldOfStudy: String!
    startYear: Int!
    endYear: Int
    gpa: String
    description: String
}

input CreateProjectInput {
    title: String!
    description: String!
    category: String!
    budget: Float
    hourlyRate: Float
    estimatedHours: Int
    expertId: ID
    startDate: Time
    endDate: Time
}

input UpdateProjectInput {
    title: String
    description: String
    status: String
    budget: Float
    hourlyRate: Float
    estimatedHours: Int
    actualHours: Int
    endDate: Time
}

input CreateExpertReviewInput {
    expertId: ID!
    projectId: ID
    rating: Float!
    title: String!
    comment: String!
    pros: [String!]
    cons: [String!]
    wouldRecommend: Boolean!
}

input UpdateExpertReviewInput {
    rating: Float
    title: String
    comment: String
    pros: [String!]
    cons: [String!]
    wouldRecommend: Boolean
}

# Support Input Types
input CreateSupportTicketInput {
    subject: String!
    description: String!
    priority: String!
    category: String!
    attachments: [Upload!]
}

input UpdateSupportTicketInput {
    subject: String
    description: String
    priority: String
    category: String
    status: String
}

# Business Tools Input Types
input CreateBusinessPlanInput {
    name: String!
    description: String
    template: String
}

input UpdateBusinessPlanInput {
    name: String
    description: String
    status: String
}

input UpdateBusinessPlanSectionInput {
    title: String
    content: String
    isComplete: Boolean
}

input CreateFinancialPlanInput {
    name: String!
    description: String
}

input UpdateFinancialPlanInput {
    name: String
    description: String
}

input CreateComplianceCheckInput {
    regulation: String!
    industry: String
    location: String
}

# Additional Types
scalar Upload