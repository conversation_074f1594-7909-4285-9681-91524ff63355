package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"path/filepath"

	"goVwPlatformAPI/graph"
	"goVwPlatformAPI/graph/generated"
	"goVwPlatformAPI/internal/auth"
	"goVwPlatformAPI/internal/database"

	"github.com/99designs/gqlgen/graphql/handler"
	"github.com/99designs/gqlgen/graphql/handler/extension"
	"github.com/99designs/gqlgen/graphql/handler/lru"
	"github.com/99designs/gqlgen/graphql/handler/transport"
	"github.com/99designs/gqlgen/graphql/playground"
	"github.com/golang-jwt/jwt/v5"
	"github.com/joho/godotenv"
)

const (
	defaultPort = "8080"
	themesDir   = "themes"
	addonsDir   = "addons"
)

type Theme struct {
	Variables map[string]string `json:"variables"`
}

func saveThemeHandler(w http.ResponseWriter, r *http.Request) {
	var theme Theme
	if err := json.NewDecoder(r.Body).Decode(&theme); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	data, err := json.Marshal(theme)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	if err := os.MkdirAll(themesDir, 0755); err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	path := filepath.Join(themesDir, "current-theme.json")
	if err := os.WriteFile(path, data, 0644); err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusCreated)
}

func loadThemeHandler(w http.ResponseWriter, r *http.Request) {
	path := filepath.Join(themesDir, "current-theme.json")
	data, err := os.ReadFile(path)
	if err != nil {
		if os.IsNotExist(err) {
			http.Error(w, "Theme not found", http.StatusNotFound)
			return
		}
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.Write(data)
}

func cssHandler(w http.ResponseWriter, r *http.Request) {
	path := filepath.Join(themesDir, "current-theme.json")
	data, err := os.ReadFile(path)
	if err != nil {
		if os.IsNotExist(err) {
			http.Error(w, ":root{}", http.StatusOK)
			return
		}
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	var theme Theme
	if err := json.Unmarshal(data, &theme); err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	css := ":root {\n"
	for name, value := range theme.Variables {
		css += "  " + name + ": " + value + ";\n"
	}
	css += "}\n"

	w.Header().Set("Content-Type", "text/css")
	w.Write([]byte(css))
}

func main() {
	// Load environment variables from .env file
	err := godotenv.Load()
	if err != nil {
		log.Printf("Warning: Error loading .env file: %v", err)
	}

	// Override password with correct value (temporary fix for special characters)
	os.Setenv("PGPASSWORD", "$Jf6sSkfyPb&v7r1")

	// Log database connection details for debugging
	log.Printf("Database connection details:")
	log.Printf("  PGHOST: %s", os.Getenv("PGHOST"))
	log.Printf("  PGPORT: %s", os.Getenv("PGPORT"))
	log.Printf("  PGDATABASE: %s", os.Getenv("PGDATABASE"))
	log.Printf("  PGUSER: %s", os.Getenv("PGUSER"))
	log.Printf("  PGPASSWORD length: %d", len(os.Getenv("PGPASSWORD")))

	port := os.Getenv("PORT")
	if port == "" {
		port = defaultPort
	}

	dbPool, err := database.NewConnection()
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer dbPool.Close()

	// Test database connection
	log.Println("Testing database connection...")
	err = dbPool.Pool.Ping(context.Background())
	if err != nil {
		log.Fatalf("Failed to ping database: %v", err)
	}
	log.Println("Database connection successful!")

	// Run migrations with our fix
	err = dbPool.RunMigrations()
	if err != nil {
		log.Fatalf("Failed to run migrations: %v", err)
	}

	// Seed database with initial data
	err = dbPool.SeedDatabase()
	if err != nil {
		log.Printf("Warning: Failed to seed database: %v", err)
	}

	// Theme endpoints
	http.HandleFunc("/api/theme", func(w http.ResponseWriter, r *http.Request) {
		switch r.Method {
		case http.MethodPost:
			saveThemeHandler(w, r)
		case http.MethodGet:
			loadThemeHandler(w, r)
		default:
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		}
	})
	http.HandleFunc("/api/theme.css", cssHandler)

	// Addon configuration endpoints
	http.HandleFunc("/api/addon", func(w http.ResponseWriter, r *http.Request) {
		switch r.Method {
		case http.MethodPost:
			var config map[string]interface{}
			if err := json.NewDecoder(r.Body).Decode(&config); err != nil {
				http.Error(w, err.Error(), http.StatusBadRequest)
				return
			}

			data, err := json.Marshal(config)
			if err != nil {
				http.Error(w, err.Error(), http.StatusInternalServerError)
				return
			}

			if err := os.MkdirAll(addonsDir, 0755); err != nil {
				http.Error(w, err.Error(), http.StatusInternalServerError)
				return
			}

			path := filepath.Join(addonsDir, "current-addon.json")
			if err := os.WriteFile(path, data, 0644); err != nil {
				http.Error(w, err.Error(), http.StatusInternalServerError)
				return
			}

			w.WriteHeader(http.StatusCreated)
		case http.MethodGet:
			path := filepath.Join(addonsDir, "current-addon.json")
			data, err := os.ReadFile(path)
			if err != nil {
				if os.IsNotExist(err) {
					http.Error(w, "Addon not found", http.StatusNotFound)
					return
				}
				http.Error(w, err.Error(), http.StatusInternalServerError)
				return
			}

			w.Header().Set("Content-Type", "application/json")
			w.Write(data)
		default:
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		}
	})

	// Addon generation endpoint
	http.HandleFunc("/api/addon/generate", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != http.MethodPost {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		var req struct {
			Config    map[string]interface{} `json:"config"`
			OutputDir string                 `json:"outputDir"`
		}

		if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
			http.Error(w, err.Error(), http.StatusBadRequest)
			return
		}

		if req.OutputDir == "" {
			http.Error(w, "outputDir is required", http.StatusBadRequest)
			return
		}

		// TODO: Implement actual addon generation logic
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(map[string]string{
			"message": fmt.Sprintf("Addon generated to %s", req.OutputDir),
			"path":    req.OutputDir,
		})
	})

	// Compilation endpoint
	http.HandleFunc("/api/compile", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != http.MethodPost {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		var req struct {
			OutputDir string `json:"outputDir"`
		}

		if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
			http.Error(w, err.Error(), http.StatusBadRequest)
			return
		}

		if req.OutputDir == "" {
			http.Error(w, "outputDir is required", http.StatusBadRequest)
			return
		}

		// Temporarily disable database connection for debugging
		log.Println("Skipping database connection in compile endpoint for debugging...")
		http.Error(w, "Compilation temporarily disabled for debugging", http.StatusServiceUnavailable)
		return

		// db, err := database.NewConnection()
		// if err != nil {
		// 	http.Error(w, "Failed to connect to database", http.StatusInternalServerError)
		// 	return
		// }
		// defer db.Close()

		// comp := compiler.NewCompiler(db, req.OutputDir)
		// if err := comp.CompileSite(); err != nil {
		// 	http.Error(w, err.Error(), http.StatusInternalServerError)
		// 	return
		// }

		// w.Header().Set("Content-Type", "application/json")
		// json.NewEncoder(w).Encode(map[string]string{
		// 	"message": fmt.Sprintf("Site compiled to %s", req.OutputDir),
		// 	"path":    req.OutputDir,
		// })
	})

	// Static file serving
	http.Handle("/static/", http.StripPrefix("/static/", http.FileServer(http.Dir("static"))))

	// Public-facing routes (serve marketing pages)
	http.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path == "/" {
			http.ServeFile(w, r, "templates/homepage.html")
			return
		}
		http.NotFound(w, r)
	})

	// Features page
	http.HandleFunc("/features", func(w http.ResponseWriter, r *http.Request) {
		http.ServeFile(w, r, "templates/features.html")
	})

	// Pricing page
	http.HandleFunc("/pricing", func(w http.ResponseWriter, r *http.Request) {
		http.ServeFile(w, r, "templates/pricing.html")
	})

	// Experts page
	http.HandleFunc("/experts", func(w http.ResponseWriter, r *http.Request) {
		http.ServeFile(w, r, "templates/experts.html")
	})

	// About page
	http.HandleFunc("/about", func(w http.ResponseWriter, r *http.Request) {
		http.ServeFile(w, r, "templates/about.html")
	})

	// Contact page
	http.HandleFunc("/contact", func(w http.ResponseWriter, r *http.Request) {
		http.ServeFile(w, r, "templates/contact.html")
	})

	// Contact form handler
	http.HandleFunc("/api/contact", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "POST" {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		// Parse form data
		firstName := r.FormValue("firstName")
		lastName := r.FormValue("lastName")
		email := r.FormValue("email")
		subject := r.FormValue("subject")
		message := r.FormValue("message")

		// Basic validation
		if firstName == "" || lastName == "" || email == "" || subject == "" || message == "" {
			w.Header().Set("Content-Type", "text/html")
			w.WriteHeader(http.StatusBadRequest)
			w.Write([]byte(`
				<div class="alert alert-danger">
					<i class="bi bi-exclamation-triangle me-2"></i>
					Please fill in all required fields.
				</div>
			`))
			return
		}

		// TODO: Implement actual email sending logic
		// For now, just return success message
		w.Header().Set("Content-Type", "text/html")
		w.Write([]byte(`
			<div class="alert alert-success">
				<i class="bi bi-check-circle me-2"></i>
				Thank you for your message! We'll get back to you within 24 hours.
			</div>
		`))
	})

	// Blog page
	http.HandleFunc("/blog", func(w http.ResponseWriter, r *http.Request) {
		http.ServeFile(w, r, "templates/blog.html")
	})

	// Legal pages
	http.HandleFunc("/terms", func(w http.ResponseWriter, r *http.Request) {
		http.ServeFile(w, r, "templates/terms.html")
	})

	http.HandleFunc("/privacy", func(w http.ResponseWriter, r *http.Request) {
		http.ServeFile(w, r, "templates/privacy.html")
	})

	http.HandleFunc("/cookies", func(w http.ResponseWriter, r *http.Request) {
		http.ServeFile(w, r, "templates/cookies.html")
	})

	http.HandleFunc("/aup", func(w http.ResponseWriter, r *http.Request) {
		http.ServeFile(w, r, "templates/aup.html")
	})

	// Addons marketplace page
	http.HandleFunc("/addons", func(w http.ResponseWriter, r *http.Request) {
		http.ServeFile(w, r, "templates/addons.html")
	})

	// Expert directory page
	http.HandleFunc("/expert-directory", func(w http.ResponseWriter, r *http.Request) {
		http.ServeFile(w, r, "templates/expert-directory.html")
	})

	// Authentication pages
	http.HandleFunc("/auth/register", func(w http.ResponseWriter, r *http.Request) {
		http.ServeFile(w, r, "templates/auth/register.html")
	})

	http.HandleFunc("/auth/login", func(w http.ResponseWriter, r *http.Request) {
		http.ServeFile(w, r, "templates/auth/login.html")
	})

	http.HandleFunc("/auth/forgot-password", func(w http.ResponseWriter, r *http.Request) {
		http.ServeFile(w, r, "templates/auth/forgot-password.html")
	})

	// Initialize database
	db, err := database.NewConnection()
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}
	defer db.Close()

	// Authentication API endpoints
	authService := auth.NewAuthService(db)

	http.HandleFunc("/api/auth/register", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "POST" {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		// Parse form data
		firstName := r.FormValue("firstName")
		lastName := r.FormValue("lastName")
		email := r.FormValue("email")
		company := r.FormValue("company")
		accountType := r.FormValue("accountType")
		password := r.FormValue("password")
		confirmPassword := r.FormValue("confirmPassword")
		agreeTerms := r.FormValue("agreeTerms") == "on"
		marketingEmails := r.FormValue("marketingEmails") == "on"

		// Validate password confirmation
		if password != confirmPassword {
			w.Header().Set("Content-Type", "text/html")
			w.WriteHeader(http.StatusBadRequest)
			w.Write([]byte(`
				<div class="alert alert-danger">
					<i class="bi bi-exclamation-triangle me-2"></i>
					Passwords do not match.
				</div>
			`))
			return
		}

		// Create registration request
		req := &auth.RegisterRequest{
			FirstName:       firstName,
			LastName:        lastName,
			Email:           email,
			Company:         company,
			AccountType:     accountType,
			Password:        password,
			AgreeTerms:      agreeTerms,
			MarketingEmails: marketingEmails,
		}

		// Process registration
		response, err := authService.Register(req)
		if err != nil {
			w.Header().Set("Content-Type", "text/html")
			w.WriteHeader(http.StatusInternalServerError)
			w.Write([]byte(`
				<div class="alert alert-danger">
					<i class="bi bi-exclamation-triangle me-2"></i>
					An error occurred. Please try again.
				</div>
			`))
			return
		}

		w.Header().Set("Content-Type", "text/html")
		if response.Success {
			w.Write([]byte(`
				<div class="alert alert-success">
					<i class="bi bi-check-circle me-2"></i>
					` + response.Message + `
				</div>
				<script>
					setTimeout(function() {
						window.location.href = '/auth/login';
					}, 2000);
				</script>
			`))
		} else {
			w.WriteHeader(http.StatusBadRequest)
			w.Write([]byte(`
				<div class="alert alert-danger">
					<i class="bi bi-exclamation-triangle me-2"></i>
					` + response.Message + `
				</div>
			`))
		}
	})

	http.HandleFunc("/api/auth/login", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "POST" {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		// Parse form data
		email := r.FormValue("email")
		password := r.FormValue("password")
		rememberMe := r.FormValue("rememberMe") == "on"

		// Create login request
		req := &auth.LoginRequest{
			Email:      email,
			Password:   password,
			RememberMe: rememberMe,
		}

		// Process login
		response, err := authService.Login(req)
		if err != nil {
			w.Header().Set("Content-Type", "text/html")
			w.WriteHeader(http.StatusInternalServerError)
			w.Write([]byte(`
				<div class="alert alert-danger">
					<i class="bi bi-exclamation-triangle me-2"></i>
					An error occurred. Please try again.
				</div>
			`))
			return
		}

		w.Header().Set("Content-Type", "text/html")
		if response.Success {
			// Set JWT token as HTTP-only cookie
			cookie := &http.Cookie{
				Name:     "auth_token",
				Value:    response.Token,
				Path:     "/",
				HttpOnly: true,
				Secure:   false, // Set to true in production with HTTPS
				SameSite: http.SameSiteLaxMode,
			}

			if rememberMe {
				cookie.MaxAge = 30 * 24 * 60 * 60 // 30 days
			} else {
				cookie.MaxAge = 24 * 60 * 60 // 24 hours
			}

			http.SetCookie(w, cookie)

			w.Write([]byte(`
				<div class="alert alert-success">
					<i class="bi bi-check-circle me-2"></i>
					Login successful! Redirecting to dashboard...
				</div>
				<script>
					setTimeout(function() {
						window.location.href = '/dashboard';
					}, 1000);
				</script>
			`))
		} else {
			w.WriteHeader(http.StatusBadRequest)
			w.Write([]byte(`
				<div class="alert alert-danger">
					<i class="bi bi-exclamation-triangle me-2"></i>
					` + response.Message + `
				</div>
			`))
		}
	})

	// Password reset endpoints
	http.HandleFunc("/api/auth/forgot-password", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "POST" {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		email := r.FormValue("email")

		req := &auth.ForgotPasswordRequest{
			Email: email,
		}

		response, err := authService.ForgotPassword(req)
		if err != nil {
			w.Header().Set("Content-Type", "text/html")
			w.WriteHeader(http.StatusInternalServerError)
			w.Write([]byte(`
				<div class="alert alert-danger">
					<i class="bi bi-exclamation-triangle me-2"></i>
					An error occurred. Please try again.
				</div>
			`))
			return
		}

		w.Header().Set("Content-Type", "text/html")
		if response.Success {
			w.Write([]byte(`
				<div class="alert alert-success">
					<i class="bi bi-check-circle me-2"></i>
					` + response.Message + `
				</div>
			`))
		} else {
			w.WriteHeader(http.StatusBadRequest)
			w.Write([]byte(`
				<div class="alert alert-danger">
					<i class="bi bi-exclamation-triangle me-2"></i>
					` + response.Message + `
				</div>
			`))
		}
	})

	// Logout endpoint
	http.HandleFunc("/api/auth/logout", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "POST" {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		// Clear the auth cookie
		cookie := &http.Cookie{
			Name:     "auth_token",
			Value:    "",
			Path:     "/",
			HttpOnly: true,
			Secure:   false, // Set to true in production with HTTPS
			SameSite: http.SameSiteLaxMode,
			MaxAge:   -1, // Delete the cookie
		}

		http.SetCookie(w, cookie)

		w.Header().Set("Content-Type", "text/html")
		w.Write([]byte(`
			<div class="alert alert-success">
				<i class="bi bi-check-circle me-2"></i>
				Logged out successfully. Redirecting...
			</div>
			<script>
				setTimeout(function() {
					window.location.href = '/';
				}, 1000);
			</script>
		`))
	})

	// Authentication middleware for cookie-based auth
	requireAuth := func(next http.HandlerFunc) http.HandlerFunc {
		return func(w http.ResponseWriter, r *http.Request) {
			// Check for auth token in cookie
			cookie, err := r.Cookie("auth_token")
			if err != nil || cookie.Value == "" {
				// Redirect to login if not authenticated
				http.Redirect(w, r, "/auth/login", http.StatusSeeOther)
				return
			}

			// Validate JWT token
			jwtSecret := os.Getenv("JWT_SECRET")
			if jwtSecret == "" {
				jwtSecret = "your_strong_secret_here"
			}

			token, err := jwt.ParseWithClaims(cookie.Value, &auth.Claims{}, func(token *jwt.Token) (interface{}, error) {
				return []byte(jwtSecret), nil
			})

			if err != nil || !token.Valid {
				// Clear invalid cookie and redirect
				http.SetCookie(w, &http.Cookie{
					Name:   "auth_token",
					Value:  "",
					Path:   "/",
					MaxAge: -1,
				})
				http.Redirect(w, r, "/auth/login", http.StatusSeeOther)
				return
			}

			// Add user context and continue
			if claims, ok := token.Claims.(*auth.Claims); ok {
				ctx := context.WithValue(r.Context(), auth.UserContextKey, claims)
				next.ServeHTTP(w, r.WithContext(ctx))
			} else {
				http.Redirect(w, r, "/auth/login", http.StatusSeeOther)
			}
		}
	}

	// Role-based dashboard routes
	http.HandleFunc("/dashboard", requireAuth(func(w http.ResponseWriter, r *http.Request) {
		// Client dashboard - only for client role
		claims, _ := r.Context().Value(auth.UserContextKey).(*auth.Claims)
		if claims == nil || claims.Role != "client" {
			// Redirect to appropriate dashboard based on role
			if claims != nil {
				switch claims.Role {
				case "admin":
					http.Redirect(w, r, "/admin/dashboard", http.StatusSeeOther)
				case "expert":
					http.Redirect(w, r, "/expert/dashboard", http.StatusSeeOther)
				default:
					http.Redirect(w, r, "/auth/login", http.StatusSeeOther)
				}
			} else {
				http.Redirect(w, r, "/auth/login", http.StatusSeeOther)
			}
			return
		}
		http.ServeFile(w, r, "templates/dashboard.html")
	}))

	// Expert dashboard
	http.HandleFunc("/expert/dashboard", requireAuth(func(w http.ResponseWriter, r *http.Request) {
		claims, _ := r.Context().Value(auth.UserContextKey).(*auth.Claims)
		if claims == nil || claims.Role != "expert" {
			if claims != nil {
				switch claims.Role {
				case "admin":
					http.Redirect(w, r, "/admin/dashboard", http.StatusSeeOther)
				case "client":
					http.Redirect(w, r, "/dashboard", http.StatusSeeOther)
				default:
					http.Redirect(w, r, "/auth/login", http.StatusSeeOther)
				}
			} else {
				http.Redirect(w, r, "/auth/login", http.StatusSeeOther)
			}
			return
		}
		http.ServeFile(w, r, "templates/expert/dashboard.html")
	}))

	// Admin dashboard
	http.HandleFunc("/admin/dashboard", requireAuth(func(w http.ResponseWriter, r *http.Request) {
		claims, _ := r.Context().Value(auth.UserContextKey).(*auth.Claims)
		if claims == nil || claims.Role != "admin" {
			if claims != nil {
				switch claims.Role {
				case "expert":
					http.Redirect(w, r, "/expert/dashboard", http.StatusSeeOther)
				case "client":
					http.Redirect(w, r, "/dashboard", http.StatusSeeOther)
				default:
					http.Redirect(w, r, "/auth/login", http.StatusSeeOther)
				}
			} else {
				http.Redirect(w, r, "/auth/login", http.StatusSeeOther)
			}
			return
		}
		http.ServeFile(w, r, "templates/admin/dashboard.html")
	}))

	// Profile management route (accessible to all authenticated users)
	http.HandleFunc("/profile", requireAuth(func(w http.ResponseWriter, r *http.Request) {
		http.ServeFile(w, r, "templates/profile.html")
	}))

	// Builder routes
	http.HandleFunc("/builder", func(w http.ResponseWriter, r *http.Request) {
		http.ServeFile(w, r, "templates/builder/website-builder-enhanced.html")
	})
	http.HandleFunc("/addon-builder", func(w http.ResponseWriter, r *http.Request) {
		http.ServeFile(w, r, "templates/builder/addon-builder.html")
	})
	http.HandleFunc("/snippets", func(w http.ResponseWriter, r *http.Request) {
		http.ServeFile(w, r, "templates/builder/snippets.html")
	})
	http.HandleFunc("/theme-editor", func(w http.ResponseWriter, r *http.Request) {
		http.ServeFile(w, r, "templates/builder/theme-editor.html")
	})

	// Admin routes
	http.HandleFunc("/admin", func(w http.ResponseWriter, r *http.Request) {
		http.ServeFile(w, r, "templates/admin/dashboard.html")
	})
	http.HandleFunc("/admin/", func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case "/admin/dashboard":
			http.ServeFile(w, r, "templates/admin/dashboard.html")
		case "/admin/addon-review":
			http.ServeFile(w, r, "templates/admin/addon-review.html")
		default:
			http.NotFound(w, r)
		}
	})

	// Admin API endpoints
	http.HandleFunc("/api/admin/activity", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "text/html")
		w.Write([]byte(`
			<div class="list-group list-group-flush">
				<div class="list-group-item border-0 py-2">
					<div class="d-flex align-items-center">
						<div class="bg-success bg-opacity-10 rounded-circle p-2 me-3">
							<i class="bi bi-check-circle text-success"></i>
						</div>
						<div>
							<small class="fw-medium">Approved Weather Widget addon</small>
							<br><small class="text-muted">2 minutes ago</small>
						</div>
					</div>
				</div>
				<div class="list-group-item border-0 py-2">
					<div class="d-flex align-items-center">
						<div class="bg-primary bg-opacity-10 rounded-circle p-2 me-3">
							<i class="bi bi-person-plus text-primary"></i>
						</div>
						<div>
							<small class="fw-medium">New user registered</small>
							<br><small class="text-muted">15 minutes ago</small>
						</div>
					</div>
				</div>
				<div class="list-group-item border-0 py-2">
					<div class="d-flex align-items-center">
						<div class="bg-warning bg-opacity-10 rounded-circle p-2 me-3">
							<i class="bi bi-exclamation-triangle text-warning"></i>
						</div>
						<div>
							<small class="fw-medium">Addon submitted for review</small>
							<br><small class="text-muted">1 hour ago</small>
						</div>
					</div>
				</div>
			</div>
		`))
	})

	http.HandleFunc("/api/admin/addons/review", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "text/html")
		w.Write([]byte(`
			<div class="row g-4">
				<div class="col-lg-4">
					<div class="card border-0 shadow-sm">
						<div class="card-body">
							<div class="d-flex justify-content-between align-items-start mb-3">
								<h6 class="mb-0">Social Media Feed</h6>
								<span class="badge bg-warning">Pending</span>
							</div>
							<p class="text-muted small">Dynamic social media integration addon with real-time updates.</p>
							<div class="mb-3">
								<small class="text-muted">
									<i class="bi bi-person me-1"></i>Created by: <EMAIL><br>
									<i class="bi bi-clock me-1"></i>Submitted: 2 hours ago
								</small>
							</div>
							<div class="d-flex gap-2">
								<button class="btn btn-success btn-sm" onclick="approveAddon('social-feed')">
									<i class="bi bi-check me-1"></i>Approve
								</button>
								<button class="btn btn-danger btn-sm" onclick="rejectAddon('social-feed')">
									<i class="bi bi-x me-1"></i>Reject
								</button>
								<button class="btn btn-outline-primary btn-sm" onclick="viewAddon('social-feed')">
									<i class="bi bi-eye me-1"></i>Review
								</button>
							</div>
						</div>
					</div>
				</div>
				<div class="col-lg-4">
					<div class="card border-0 shadow-sm">
						<div class="card-body">
							<div class="d-flex justify-content-between align-items-start mb-3">
								<h6 class="mb-0">Analytics Dashboard</h6>
								<span class="badge bg-warning">Pending</span>
							</div>
							<p class="text-muted small">Comprehensive analytics addon with charts and metrics.</p>
							<div class="mb-3">
								<small class="text-muted">
									<i class="bi bi-person me-1"></i>Created by: <EMAIL><br>
									<i class="bi bi-clock me-1"></i>Submitted: 5 hours ago
								</small>
							</div>
							<div class="d-flex gap-2">
								<button class="btn btn-success btn-sm" onclick="approveAddon('analytics')">
									<i class="bi bi-check me-1"></i>Approve
								</button>
								<button class="btn btn-danger btn-sm" onclick="rejectAddon('analytics')">
									<i class="bi bi-x me-1"></i>Reject
								</button>
								<button class="btn btn-outline-primary btn-sm" onclick="viewAddon('analytics')">
									<i class="bi bi-eye me-1"></i>Review
								</button>
							</div>
						</div>
					</div>
				</div>
				<div class="col-lg-4">
					<div class="card border-0 shadow-sm">
						<div class="card-body">
							<div class="d-flex justify-content-between align-items-start mb-3">
								<h6 class="mb-0">Payment Gateway</h6>
								<span class="badge bg-warning">Pending</span>
							</div>
							<p class="text-muted small">Secure payment processing addon with multiple providers.</p>
							<div class="mb-3">
								<small class="text-muted">
									<i class="bi bi-person me-1"></i>Created by: <EMAIL><br>
									<i class="bi bi-clock me-1"></i>Submitted: 1 day ago
								</small>
							</div>
							<div class="d-flex gap-2">
								<button class="btn btn-success btn-sm" onclick="approveAddon('payment')">
									<i class="bi bi-check me-1"></i>Approve
								</button>
								<button class="btn btn-danger btn-sm" onclick="rejectAddon('payment')">
									<i class="bi bi-x me-1"></i>Reject
								</button>
								<button class="btn btn-outline-primary btn-sm" onclick="viewAddon('payment')">
									<i class="bi bi-eye me-1"></i>Review
								</button>
							</div>
						</div>
					</div>
				</div>
			</div>
		`))
	})

	http.HandleFunc("/api/admin/users", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "text/html")
		w.Write([]byte(`
			<div class="table-responsive">
				<table class="table table-hover">
					<thead>
						<tr>
							<th>User</th>
							<th>Email</th>
							<th>Websites</th>
							<th>Addons</th>
							<th>Status</th>
							<th>Actions</th>
						</tr>
					</thead>
					<tbody>
						<tr>
							<td>
								<div class="d-flex align-items-center">
									<div class="bg-primary bg-opacity-10 rounded-circle p-2 me-3">
										<i class="bi bi-person text-primary"></i>
									</div>
									<div>
										<div class="fw-medium">John Doe</div>
										<small class="text-muted">Joined 2 months ago</small>
									</div>
								</div>
							</td>
							<td><EMAIL></td>
							<td><span class="badge bg-success">5</span></td>
							<td><span class="badge bg-warning">2</span></td>
							<td><span class="badge bg-success">Active</span></td>
							<td>
								<div class="btn-group btn-group-sm">
									<button class="btn btn-outline-primary">View</button>
									<button class="btn btn-outline-secondary">Edit</button>
								</div>
							</td>
						</tr>
						<tr>
							<td>
								<div class="d-flex align-items-center">
									<div class="bg-success bg-opacity-10 rounded-circle p-2 me-3">
										<i class="bi bi-person text-success"></i>
									</div>
									<div>
										<div class="fw-medium">Jane Smith</div>
										<small class="text-muted">Joined 1 month ago</small>
									</div>
								</div>
							</td>
							<td><EMAIL></td>
							<td><span class="badge bg-success">12</span></td>
							<td><span class="badge bg-warning">7</span></td>
							<td><span class="badge bg-success">Active</span></td>
							<td>
								<div class="btn-group btn-group-sm">
									<button class="btn btn-outline-primary">View</button>
									<button class="btn btn-outline-secondary">Edit</button>
								</div>
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		`))
	})

	// Auth routes
	http.HandleFunc("/auth/", func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case "/auth/login":
			http.ServeFile(w, r, "templates/auth/login.html")
		case "/auth/register":
			http.ServeFile(w, r, "templates/auth/register.html")
		default:
			http.NotFound(w, r)
		}
	})

	// API Auth endpoints
	http.HandleFunc("/api/auth/login", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "POST" {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		// Parse form data
		email := r.FormValue("email")
		password := r.FormValue("password")

		// Simple validation (in production, use proper authentication)
		if email != "" && password != "" {
			w.Header().Set("Content-Type", "text/html")
			w.Write([]byte(`
				<div class="alert alert-success">
					<i class="bi bi-check-circle me-2"></i>
					Login successful! Redirecting...
				</div>
			`))
		} else {
			w.WriteHeader(http.StatusBadRequest)
			w.Write([]byte(`
				<div class="alert alert-danger">
					<i class="bi bi-exclamation-triangle me-2"></i>
					Invalid credentials. Please try again.
				</div>
			`))
		}
	})

	http.HandleFunc("/api/auth/register", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "POST" {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		// Parse form data
		email := r.FormValue("email")
		password := r.FormValue("password")
		firstName := r.FormValue("firstName")
		lastName := r.FormValue("lastName")

		// Simple validation (in production, use proper user creation)
		if email != "" && password != "" && firstName != "" && lastName != "" {
			w.Header().Set("Content-Type", "text/html")
			w.Write([]byte(`
				<div class="alert alert-success">
					<i class="bi bi-check-circle me-2"></i>
					Account created successfully!
				</div>
			`))
		} else {
			w.WriteHeader(http.StatusBadRequest)
			w.Write([]byte(`
				<div class="alert alert-danger">
					<i class="bi bi-exclamation-triangle me-2"></i>
					Please fill in all required fields.
				</div>
			`))
		}
	})

	// API endpoints for HTMX
	http.HandleFunc("/api/addons", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "text/html")
		w.Write([]byte(`
			<div class="component-item" draggable="true" data-component="weather-widget">
				<i class="bi bi-cloud-sun me-2"></i>Weather Widget
			</div>
			<div class="component-item" draggable="true" data-component="contact-form">
				<i class="bi bi-envelope me-2"></i>Contact Form
			</div>
			<div class="component-item" draggable="true" data-component="image-gallery">
				<i class="bi bi-images me-2"></i>Image Gallery
			</div>
		`))
	})

	// Dashboard API endpoints
	http.HandleFunc("/api/dashboard/welcome", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "text/html")

		// Temporarily disable database queries for debugging
		userEmail := "Developer"
		projectCount := 0

		response := fmt.Sprintf(`
			<h1 class="display-5 fw-bold mb-2">Welcome back, %s!</h1>
			<p class="lead mb-0">Ready to build something amazing? You have %d active projects.</p>
		`, userEmail, projectCount)

		w.Write([]byte(response))
	})

	http.HandleFunc("/api/dashboard/stats/websites", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "text/html")

		// Temporarily disable database queries for debugging
		websiteCount := 0

		response := fmt.Sprintf(`
			<i class="bi bi-globe display-4 text-velocity-primary mb-2"></i>
			<h3 class="mb-1">%d</h3>
			<small class="text-muted">Websites Built</small>
		`, websiteCount)

		w.Write([]byte(response))
	})

	http.HandleFunc("/api/dashboard/stats/addons", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "text/html")

		// Temporarily disable database queries for debugging
		addonCount := 0

		response := fmt.Sprintf(`
			<i class="bi bi-puzzle display-4 text-success mb-2"></i>
			<h3 class="mb-1">%d</h3>
			<small class="text-muted">Addons Created</small>
		`, addonCount)

		w.Write([]byte(response))
	})

	http.HandleFunc("/api/dashboard/stats/snippets", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "text/html")

		// Temporarily disable database queries for debugging
		snippetCount := 0

		response := fmt.Sprintf(`
			<i class="bi bi-code-square display-4 text-warning mb-2"></i>
			<h3 class="mb-1">%d</h3>
			<small class="text-muted">Code Snippets</small>
		`, snippetCount)

		w.Write([]byte(response))
	})

	http.HandleFunc("/api/dashboard/stats/deployments", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "text/html")
		w.Write([]byte(`
			<i class="bi bi-cloud-upload display-4 text-info mb-2"></i>
			<h3 class="mb-1">5</h3>
			<small class="text-muted">Deployments</small>
		`))
	})

	http.HandleFunc("/api/snippets", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "text/html")
		w.Write([]byte(`
			<div class="component-item" draggable="true" data-component="button-snippet">
				<i class="bi bi-square me-2"></i>Button Styles
			</div>
			<div class="component-item" draggable="true" data-component="alert-snippet">
				<i class="bi bi-exclamation-triangle me-2"></i>Alert Messages
			</div>
			<div class="component-item" draggable="true" data-component="modal-snippet">
				<i class="bi bi-window me-2"></i>Modal Dialog
			</div>
		`))
	})

	http.HandleFunc("/api/dashboard/projects/recent", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "text/html")

		// Temporarily disable database queries for debugging
		w.Write([]byte(`<div class="text-center py-4"><p class="text-muted">No projects found (debugging mode)</p></div>`))
	})

	http.HandleFunc("/api/dashboard/activity/recent", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "text/html")
		w.Write([]byte(`
			<div class="list-group list-group-flush">
				<div class="list-group-item border-0 py-2">
					<div class="d-flex align-items-center">
						<div class="bg-primary bg-opacity-10 rounded-circle p-2 me-3">
							<i class="bi bi-plus-circle text-primary"></i>
						</div>
						<div class="flex-grow-1">
							<small class="fw-medium">Created new website</small>
							<br><small class="text-muted">2 hours ago</small>
						</div>
					</div>
				</div>
				<div class="list-group-item border-0 py-2">
					<div class="d-flex align-items-center">
						<div class="bg-success bg-opacity-10 rounded-circle p-2 me-3">
							<i class="bi bi-cloud-upload text-success"></i>
						</div>
						<div class="flex-grow-1">
							<small class="fw-medium">Published Corporate Website</small>
							<br><small class="text-muted">5 hours ago</small>
						</div>
					</div>
				</div>
				<div class="list-group-item border-0 py-2">
					<div class="d-flex align-items-center">
						<div class="bg-warning bg-opacity-10 rounded-circle p-2 me-3">
							<i class="bi bi-puzzle text-warning"></i>
						</div>
						<div class="flex-grow-1">
							<small class="fw-medium">Created Weather Widget addon</small>
							<br><small class="text-muted">Yesterday</small>
						</div>
					</div>
				</div>
			</div>
		`))
	})

	http.HandleFunc("/api/user/menu", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "text/html")
		// Check if user is authenticated (simplified for demo)
		w.Write([]byte(`
			<div class="navbar-nav">
				<div class="nav-item dropdown">
					<a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
						<i class="bi bi-person-circle me-1"></i><EMAIL>
					</a>
					<ul class="dropdown-menu">
						<li><a class="dropdown-item" href="/profile">
							<i class="bi bi-person me-2"></i>Profile
						</a></li>
						<li><a class="dropdown-item" href="/settings">
							<i class="bi bi-gear me-2"></i>Settings
						</a></li>
						<li><hr class="dropdown-divider"></li>
						<li><a class="dropdown-item" href="/admin">
							<i class="bi bi-shield-check me-2"></i>Admin Panel
						</a></li>
						<li><hr class="dropdown-divider"></li>
						<li><a class="dropdown-item" href="/logout">
							<i class="bi bi-box-arrow-right me-2"></i>Logout
						</a></li>
					</ul>
				</div>
			</div>
		`))
	})

	// Builder-specific API endpoints
	http.HandleFunc("/api/builder/project/current", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "text/html")
		w.Write([]byte(`<small class="opacity-75">My Website Project - Last saved 5 minutes ago</small>`))
	})

	http.HandleFunc("/api/builder/addons", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "text/html")

		// Temporarily disable database queries for debugging
		w.Write([]byte(`<div class="text-center py-3"><p class="text-muted">Addons disabled for debugging</p></div>`))
	})

	http.HandleFunc("/api/builder/snippets", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "text/html")

		// Temporarily disable database queries for debugging
		w.Write([]byte(`<div class="text-center py-3"><p class="text-muted">Snippets disabled for debugging</p></div>`))
	})

	// GraphQL endpoints with modern gqlgen setup
	log.Println("Setting up GraphQL server...")

	// Create resolver with all dependencies
	resolver := &graph.Resolver{
		DB:                  dbPool,
		SubscriptionService: nil, // Initialize if needed
		PageManager:         nil, // Initialize if needed
		OnboardingService:   nil, // Initialize if needed
		DashboardService:    nil, // Initialize if needed
	}

	// Create GraphQL server with modern handler
	srv := handler.New(generated.NewExecutableSchema(generated.Config{Resolvers: resolver}))

	// Add transports
	srv.AddTransport(transport.Options{})
	srv.AddTransport(transport.GET{})
	srv.AddTransport(transport.POST{})
	srv.AddTransport(transport.MultipartForm{})

	// Add extensions
	srv.Use(extension.Introspection{})
	srv.Use(extension.AutomaticPersistedQuery{
		Cache: lru.New[string](100),
	})

	// Setup routes
	http.Handle("/playground", playground.Handler("GraphQL playground", "/query"))
	http.Handle("/query", auth.Middleware(dbPool)(srv))

	log.Printf("Server running on http://localhost:%s", port)
	log.Fatal(http.ListenAndServe(":"+port, nil))
}
