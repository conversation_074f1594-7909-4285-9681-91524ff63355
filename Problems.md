before gqlgen generate

[{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "DuplicateDecl",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "DuplicateDecl"
		}
	},
	"severity": 8,
	"message": "duplicate method GenerateSiteAPIKey",
	"source": "compiler",
	"startLineNumber": 1934,
	"startColumn": 2,
	"endLineNumber": 1934,
	"endColumn": 20,
	"relatedInformation": [
		{
			"startLineNumber": 2029,
			"startColumn": 2,
			"endLineNumber": 2029,
			"endColumn": 20,
			"message": "other declaration of method GenerateSiteAPIKey",
			"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go"
		}
	]
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "DuplicateDecl",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "DuplicateDecl"
		}
	},
	"severity": 8,
	"message": "duplicate method GenerateSiteAPIKey (see details)",
	"source": "compiler",
	"startLineNumber": 2029,
	"startColumn": 2,
	"endLineNumber": 2029,
	"endColumn": 20,
	"relatedInformation": [
		{
			"startLineNumber": 1934,
			"startColumn": 2,
			"endLineNumber": 1934,
			"endColumn": 20,
			"message": "",
			"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go"
		}
	]
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use site.PageCount (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 682,
	"startColumn": 17,
	"endLineNumber": 682,
	"endColumn": 31
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use site.PageCount (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 750,
	"startColumn": 17,
	"endLineNumber": 750,
	"endColumn": 31
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use input.Position (variable of type *int32) as *int value in struct literal",
	"source": "compiler",
	"startLineNumber": 860,
	"startColumn": 20,
	"endLineNumber": 860,
	"endColumn": 34
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use page.Position (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 896,
	"startColumn": 20,
	"endLineNumber": 896,
	"endColumn": 33
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use page.ContentVersion (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 910,
	"startColumn": 20,
	"endLineNumber": 910,
	"endColumn": 39
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use tenant.MaxUsers (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 1191,
	"startColumn": 14,
	"endLineNumber": 1191,
	"endColumn": 29
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use tenant.MaxSites (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 1192,
	"startColumn": 14,
	"endLineNumber": 1192,
	"endColumn": 29
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use tenant.MaxUsers (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 1254,
	"startColumn": 14,
	"endLineNumber": 1254,
	"endColumn": 29
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use tenant.MaxSites (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 1255,
	"startColumn": 14,
	"endLineNumber": 1255,
	"endColumn": 29
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use progressStep.Order (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 1429,
	"startColumn": 17,
	"endLineNumber": 1429,
	"endColumn": 35
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use progress.CurrentStep (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 1438,
	"startColumn": 19,
	"endLineNumber": 1438,
	"endColumn": 39
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use progress.TotalSteps (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 1439,
	"startColumn": 19,
	"endLineNumber": 1439,
	"endColumn": 38
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use progress.CompletedSteps (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 1440,
	"startColumn": 19,
	"endLineNumber": 1440,
	"endColumn": 42
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use tenant.MaxUsers (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 1824,
	"startColumn": 15,
	"endLineNumber": 1824,
	"endColumn": 30
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use tenant.MaxSites (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 1825,
	"startColumn": 15,
	"endLineNumber": 1825,
	"endColumn": 30
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use tenant.MaxUsers (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 1860,
	"startColumn": 14,
	"endLineNumber": 1860,
	"endColumn": 29
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use tenant.MaxSites (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 1861,
	"startColumn": 14,
	"endLineNumber": 1861,
	"endColumn": 29
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use stats[\"user_count\"].(int) (comma, ok expression of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 1887,
	"startColumn": 15,
	"endLineNumber": 1887,
	"endColumn": 40
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use stats[\"site_count\"].(int) (comma, ok expression of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 1888,
	"startColumn": 15,
	"endLineNumber": 1888,
	"endColumn": 40
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use stats[\"page_count\"].(int) (comma, ok expression of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 1889,
	"startColumn": 15,
	"endLineNumber": 1889,
	"endColumn": 40
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use stats[\"addon_count\"].(int) (comma, ok expression of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 1890,
	"startColumn": 15,
	"endLineNumber": 1890,
	"endColumn": 41
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use progressStep.Order (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 2037,
	"startColumn": 17,
	"endLineNumber": 2037,
	"endColumn": 35
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use progress.CurrentStep (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 2046,
	"startColumn": 19,
	"endLineNumber": 2046,
	"endColumn": 39
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use progress.TotalSteps (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 2047,
	"startColumn": 19,
	"endLineNumber": 2047,
	"endColumn": 38
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use progress.CompletedSteps (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 2048,
	"startColumn": 19,
	"endLineNumber": 2048,
	"endColumn": 42
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use stats.TotalSites (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 2141,
	"startColumn": 21,
	"endLineNumber": 2141,
	"endColumn": 37
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use stats.TotalPages (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 2142,
	"startColumn": 21,
	"endLineNumber": 2142,
	"endColumn": 37
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use stats.TotalAddons (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 2143,
	"startColumn": 21,
	"endLineNumber": 2143,
	"endColumn": 38
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use stats.APICallsUsed (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 2148,
	"startColumn": 21,
	"endLineNumber": 2148,
	"endColumn": 39
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use site.PageCount (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 2185,
	"startColumn": 18,
	"endLineNumber": 2185,
	"endColumn": 32
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use sp.Position (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 2233,
	"startColumn": 16,
	"endLineNumber": 2233,
	"endColumn": 27
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use sp.Page.Position (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 2244,
	"startColumn": 16,
	"endLineNumber": 2244,
	"endColumn": 32
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use site.PageCount (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 2267,
	"startColumn": 17,
	"endLineNumber": 2267,
	"endColumn": 31
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use sp.Position (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 2302,
	"startColumn": 16,
	"endLineNumber": 2302,
	"endColumn": 27
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use sp.Page.Position (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 2313,
	"startColumn": 16,
	"endLineNumber": 2313,
	"endColumn": 32
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use page.Position (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 2349,
	"startColumn": 20,
	"endLineNumber": 2349,
	"endColumn": 33
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use page.ContentVersion (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 2363,
	"startColumn": 20,
	"endLineNumber": 2363,
	"endColumn": 39
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use page.Position (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 2401,
	"startColumn": 22,
	"endLineNumber": 2401,
	"endColumn": 35
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use page.ContentVersion (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 2415,
	"startColumn": 22,
	"endLineNumber": 2415,
	"endColumn": 41
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "InvalidIfaceAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "InvalidIfaceAssign"
		}
	},
	"severity": 8,
	"message": "cannot use &mutationResolver{…} (value of type *mutationResolver) as generated.MutationResolver value in return statement: *mutationResolver does not implement generated.MutationResolver (wrong type for method GenerateSiteAPIKey)\n\t\thave GenerateSiteAPIKey(context.Context, string, int) (*model.APIKey, error)\n\t\twant GenerateSiteAPIKey(context.Context, string, int32) (*model.APIKey, error)",
	"source": "compiler",
	"startLineNumber": 2697,
	"startColumn": 67,
	"endLineNumber": 2697,
	"endColumn": 87
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "InvalidIfaceAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "InvalidIfaceAssign"
		}
	},
	"severity": 8,
	"message": "cannot use &queryResolver{…} (value of type *queryResolver) as generated.QueryResolver value in return statement: *queryResolver does not implement generated.QueryResolver (wrong type for method AdminAddonReviews)\n\t\thave AdminAddonReviews(context.Context, *model.AddonReviewFiltersInput, *int, *int) (*model.AddonReviewListResponse, error)\n\t\twant AdminAddonReviews(context.Context, *model.AddonReviewFiltersInput, *int32, *int32) (*model.AddonReviewListResponse, error)",
	"source": "compiler",
	"startLineNumber": 2700,
	"startColumn": 61,
	"endLineNumber": 2700,
	"endColumn": 78
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/business_tools_resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use section.Order (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 268,
	"startColumn": 13,
	"endLineNumber": 268,
	"endColumn": 26
}]

after gqlgen generate

[{
	"resource": "/c:/Users/<USER>/Desktop/goVwPlatformAPI/cmd/api/main.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingLitField",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingLitField"
		}
	},
	"severity": 8,
	"message": "unknown field DB in struct literal of type graph.Resolver",
	"source": "compiler",
	"startLineNumber": 1278,
	"startColumn": 3,
	"endLineNumber": 1278,
	"endColumn": 5
},{
	"resource": "/c:/Users/<USER>/Desktop/goVwPlatformAPI/cmd/api/main.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingLitField",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingLitField"
		}
	},
	"severity": 8,
	"message": "unknown field SubscriptionService in struct literal of type graph.Resolver",
	"source": "compiler",
	"startLineNumber": 1279,
	"startColumn": 3,
	"endLineNumber": 1279,
	"endColumn": 22
},{
	"resource": "/c:/Users/<USER>/Desktop/goVwPlatformAPI/cmd/api/main.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingLitField",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingLitField"
		}
	},
	"severity": 8,
	"message": "unknown field PageManager in struct literal of type graph.Resolver",
	"source": "compiler",
	"startLineNumber": 1280,
	"startColumn": 3,
	"endLineNumber": 1280,
	"endColumn": 14
},{
	"resource": "/c:/Users/<USER>/Desktop/goVwPlatformAPI/cmd/api/main.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingLitField",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingLitField"
		}
	},
	"severity": 8,
	"message": "unknown field OnboardingService in struct literal of type graph.Resolver",
	"source": "compiler",
	"startLineNumber": 1281,
	"startColumn": 3,
	"endLineNumber": 1281,
	"endColumn": 20
},{
	"resource": "/c:/Users/<USER>/Desktop/goVwPlatformAPI/cmd/api/main.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingLitField",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingLitField"
		}
	},
	"severity": 8,
	"message": "unknown field DashboardService in struct literal of type graph.Resolver",
	"source": "compiler",
	"startLineNumber": 1282,
	"startColumn": 3,
	"endLineNumber": 1282,
	"endColumn": 19
},{
	"resource": "/c:/Users/<USER>/Desktop/goVwPlatformAPI/cmd/api/main.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "InvalidIfaceAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "InvalidIfaceAssign"
		}
	},
	"severity": 8,
	"message": "cannot use resolver (variable of type *graph.Resolver) as generated.ResolverRoot value in struct literal: *graph.Resolver does not implement generated.ResolverRoot (unexported method __InputValue)",
	"source": "compiler",
	"startLineNumber": 1286,
	"startColumn": 79,
	"endLineNumber": 1286,
	"endColumn": 87
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "InvalidIfaceAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "InvalidIfaceAssign"
		}
	},
	"severity": 8,
	"message": "cannot use &executableSchema{…} (value of type *executableSchema) as graphql.ExecutableSchema value in return statement: *executableSchema does not implement graphql.ExecutableSchema (wrong type for method Complexity)\n\t\thave Complexity(context.Context, string, string, int, map[string]any) (int, bool)\n\t\twant Complexity(string, string, int, map[string]interface{}) (int, bool)",
	"source": "compiler",
	"startLineNumber": 26,
	"startColumn": 9,
	"endLineNumber": 31,
	"endColumn": 3
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "ec.unmarshalInputJSON undefined (type *executionContext has no field or method unmarshalInputJSON)",
	"source": "compiler",
	"startLineNumber": 123176,
	"startColumn": 17,
	"endLineNumber": 123176,
	"endColumn": 35
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "ec._JSON undefined (type *executionContext has no field or method _JSON)",
	"source": "compiler",
	"startLineNumber": 123187,
	"startColumn": 12,
	"endLineNumber": 123187,
	"endColumn": 17
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "ec.unmarshalInputTime undefined (type *executionContext has no field or method unmarshalInputTime)",
	"source": "compiler",
	"startLineNumber": 125097,
	"startColumn": 17,
	"endLineNumber": 125097,
	"endColumn": 35
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "ec._Time undefined (type *executionContext has no field or method _Time)",
	"source": "compiler",
	"startLineNumber": 125102,
	"startColumn": 12,
	"endLineNumber": 125102,
	"endColumn": 17
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "ec.unmarshalInputJSON undefined (type *executionContext has no field or method unmarshalInputJSON)",
	"source": "compiler",
	"startLineNumber": 126339,
	"startColumn": 17,
	"endLineNumber": 126339,
	"endColumn": 35
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "ec._JSON undefined (type *executionContext has no field or method _JSON)",
	"source": "compiler",
	"startLineNumber": 126347,
	"startColumn": 12,
	"endLineNumber": 126347,
	"endColumn": 17
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "ec.unmarshalInputTime undefined (type *executionContext has no field or method unmarshalInputTime)",
	"source": "compiler",
	"startLineNumber": 126707,
	"startColumn": 17,
	"endLineNumber": 126707,
	"endColumn": 35
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "ec._Time undefined (type *executionContext has no field or method _Time)",
	"source": "compiler",
	"startLineNumber": 126715,
	"startColumn": 12,
	"endLineNumber": 126715,
	"endColumn": 17
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 31,
	"startColumn": 17,
	"endLineNumber": 31,
	"endColumn": 19
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 39,
	"startColumn": 10,
	"endLineNumber": 39,
	"endColumn": 12
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 75,
	"startColumn": 19,
	"endLineNumber": 75,
	"endColumn": 21
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 104,
	"startColumn": 11,
	"endLineNumber": 104,
	"endColumn": 13
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 118,
	"startColumn": 13,
	"endLineNumber": 118,
	"endColumn": 15
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 128,
	"startColumn": 11,
	"endLineNumber": 128,
	"endColumn": 13
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 142,
	"startColumn": 13,
	"endLineNumber": 142,
	"endColumn": 15
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 152,
	"startColumn": 11,
	"endLineNumber": 152,
	"endColumn": 13
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 166,
	"startColumn": 13,
	"endLineNumber": 166,
	"endColumn": 15
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 176,
	"startColumn": 11,
	"endLineNumber": 176,
	"endColumn": 13
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 190,
	"startColumn": 13,
	"endLineNumber": 190,
	"endColumn": 15
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 200,
	"startColumn": 11,
	"endLineNumber": 200,
	"endColumn": 13
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 214,
	"startColumn": 13,
	"endLineNumber": 214,
	"endColumn": 15
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 224,
	"startColumn": 11,
	"endLineNumber": 224,
	"endColumn": 13
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 254,
	"startColumn": 17,
	"endLineNumber": 254,
	"endColumn": 19
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 281,
	"startColumn": 11,
	"endLineNumber": 281,
	"endColumn": 13
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 291,
	"startColumn": 20,
	"endLineNumber": 291,
	"endColumn": 22
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 310,
	"startColumn": 13,
	"endLineNumber": 310,
	"endColumn": 15
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 325,
	"startColumn": 11,
	"endLineNumber": 325,
	"endColumn": 13
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 335,
	"startColumn": 10,
	"endLineNumber": 335,
	"endColumn": 12
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 346,
	"startColumn": 11,
	"endLineNumber": 346,
	"endColumn": 13
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 374,
	"startColumn": 11,
	"endLineNumber": 374,
	"endColumn": 13
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 392,
	"startColumn": 11,
	"endLineNumber": 392,
	"endColumn": 13
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 406,
	"startColumn": 11,
	"endLineNumber": 406,
	"endColumn": 13
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 420,
	"startColumn": 11,
	"endLineNumber": 420,
	"endColumn": 13
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 434,
	"startColumn": 11,
	"endLineNumber": 434,
	"endColumn": 13
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 448,
	"startColumn": 10,
	"endLineNumber": 448,
	"endColumn": 12
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 468,
	"startColumn": 13,
	"endLineNumber": 468,
	"endColumn": 15
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 631,
	"startColumn": 46,
	"endLineNumber": 631,
	"endColumn": 48
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use site.PageCount (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 677,
	"startColumn": 17,
	"endLineNumber": 677,
	"endColumn": 31
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 695,
	"startColumn": 46,
	"endLineNumber": 695,
	"endColumn": 48
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use site.PageCount (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 745,
	"startColumn": 17,
	"endLineNumber": 745,
	"endColumn": 31
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 763,
	"startColumn": 46,
	"endLineNumber": 763,
	"endColumn": 48
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 793,
	"startColumn": 54,
	"endLineNumber": 793,
	"endColumn": 56
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 848,
	"startColumn": 62,
	"endLineNumber": 848,
	"endColumn": 64
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use input.Position (variable of type *int32) as *int value in struct literal",
	"source": "compiler",
	"startLineNumber": 855,
	"startColumn": 20,
	"endLineNumber": 855,
	"endColumn": 34
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use page.Position (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 891,
	"startColumn": 20,
	"endLineNumber": 891,
	"endColumn": 33
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use page.ContentVersion (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 905,
	"startColumn": 20,
	"endLineNumber": 905,
	"endColumn": 39
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 918,
	"startColumn": 62,
	"endLineNumber": 918,
	"endColumn": 64
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 938,
	"startColumn": 54,
	"endLineNumber": 938,
	"endColumn": 56
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 976,
	"startColumn": 46,
	"endLineNumber": 976,
	"endColumn": 48
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 1168,
	"startColumn": 47,
	"endLineNumber": 1168,
	"endColumn": 49
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use tenant.MaxUsers (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 1186,
	"startColumn": 14,
	"endLineNumber": 1186,
	"endColumn": 29
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use tenant.MaxSites (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 1187,
	"startColumn": 14,
	"endLineNumber": 1187,
	"endColumn": 29
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 1202,
	"startColumn": 47,
	"endLineNumber": 1202,
	"endColumn": 49
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use tenant.MaxUsers (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 1249,
	"startColumn": 14,
	"endLineNumber": 1249,
	"endColumn": 29
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use tenant.MaxSites (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 1250,
	"startColumn": 14,
	"endLineNumber": 1250,
	"endColumn": 29
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 1270,
	"startColumn": 47,
	"endLineNumber": 1270,
	"endColumn": 49
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 1287,
	"startColumn": 63,
	"endLineNumber": 1287,
	"endColumn": 65
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.SubscriptionService undefined (type *mutationResolver has no field or method SubscriptionService)",
	"source": "compiler",
	"startLineNumber": 1337,
	"startColumn": 11,
	"endLineNumber": 1337,
	"endColumn": 30
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.SubscriptionService undefined (type *mutationResolver has no field or method SubscriptionService)",
	"source": "compiler",
	"startLineNumber": 1343,
	"startColumn": 25,
	"endLineNumber": 1343,
	"endColumn": 44
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 1377,
	"startColumn": 63,
	"endLineNumber": 1377,
	"endColumn": 65
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use amount (variable of type int32) as int value in argument to subscriptionService.TrackUsage",
	"source": "compiler",
	"startLineNumber": 1378,
	"startColumn": 78,
	"endLineNumber": 1378,
	"endColumn": 84
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 1398,
	"startColumn": 57,
	"endLineNumber": 1398,
	"endColumn": 59
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use progressStep.Order (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 1424,
	"startColumn": 17,
	"endLineNumber": 1424,
	"endColumn": 35
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use progress.CurrentStep (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 1433,
	"startColumn": 19,
	"endLineNumber": 1433,
	"endColumn": 39
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use progress.TotalSteps (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 1434,
	"startColumn": 19,
	"endLineNumber": 1434,
	"endColumn": 38
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use progress.CompletedSteps (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 1435,
	"startColumn": 19,
	"endLineNumber": 1435,
	"endColumn": 42
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 1449,
	"startColumn": 57,
	"endLineNumber": 1449,
	"endColumn": 59
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 1505,
	"startColumn": 57,
	"endLineNumber": 1505,
	"endColumn": 59
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 1660,
	"startColumn": 20,
	"endLineNumber": 1660,
	"endColumn": 22
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 1710,
	"startColumn": 11,
	"endLineNumber": 1710,
	"endColumn": 13
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 1719,
	"startColumn": 11,
	"endLineNumber": 1719,
	"endColumn": 13
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 1724,
	"startColumn": 11,
	"endLineNumber": 1724,
	"endColumn": 13
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 1729,
	"startColumn": 11,
	"endLineNumber": 1729,
	"endColumn": 13
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 1736,
	"startColumn": 20,
	"endLineNumber": 1736,
	"endColumn": 22
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 1750,
	"startColumn": 22,
	"endLineNumber": 1750,
	"endColumn": 24
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 1772,
	"startColumn": 18,
	"endLineNumber": 1772,
	"endColumn": 20
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 1788,
	"startColumn": 47,
	"endLineNumber": 1788,
	"endColumn": 49
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use *limit (variable of type int32) as int value in assignment",
	"source": "compiler",
	"startLineNumber": 1793,
	"startColumn": 14,
	"endLineNumber": 1793,
	"endColumn": 20
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use *offset (variable of type int32) as int value in assignment",
	"source": "compiler",
	"startLineNumber": 1797,
	"startColumn": 15,
	"endLineNumber": 1797,
	"endColumn": 22
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use tenant.MaxUsers (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 1819,
	"startColumn": 15,
	"endLineNumber": 1819,
	"endColumn": 30
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use tenant.MaxSites (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 1820,
	"startColumn": 15,
	"endLineNumber": 1820,
	"endColumn": 30
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 1842,
	"startColumn": 47,
	"endLineNumber": 1842,
	"endColumn": 49
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use tenant.MaxUsers (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 1855,
	"startColumn": 14,
	"endLineNumber": 1855,
	"endColumn": 29
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use tenant.MaxSites (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 1856,
	"startColumn": 14,
	"endLineNumber": 1856,
	"endColumn": 29
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 1875,
	"startColumn": 47,
	"endLineNumber": 1875,
	"endColumn": 49
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use stats[\"user_count\"].(int) (comma, ok expression of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 1882,
	"startColumn": 15,
	"endLineNumber": 1882,
	"endColumn": 40
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use stats[\"site_count\"].(int) (comma, ok expression of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 1883,
	"startColumn": 15,
	"endLineNumber": 1883,
	"endColumn": 40
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use stats[\"page_count\"].(int) (comma, ok expression of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 1884,
	"startColumn": 15,
	"endLineNumber": 1884,
	"endColumn": 40
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use stats[\"addon_count\"].(int) (comma, ok expression of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 1885,
	"startColumn": 15,
	"endLineNumber": 1885,
	"endColumn": 41
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 1901,
	"startColumn": 47,
	"endLineNumber": 1901,
	"endColumn": 49
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 1915,
	"startColumn": 63,
	"endLineNumber": 1915,
	"endColumn": 65
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 1950,
	"startColumn": 63,
	"endLineNumber": 1950,
	"endColumn": 65
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 1987,
	"startColumn": 63,
	"endLineNumber": 1987,
	"endColumn": 65
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use usage (variable of type int) as int32 value in return statement",
	"source": "compiler",
	"startLineNumber": 1993,
	"startColumn": 9,
	"endLineNumber": 1993,
	"endColumn": 14
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 2018,
	"startColumn": 57,
	"endLineNumber": 2018,
	"endColumn": 59
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use progressStep.Order (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 2032,
	"startColumn": 17,
	"endLineNumber": 2032,
	"endColumn": 35
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use progress.CurrentStep (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 2041,
	"startColumn": 19,
	"endLineNumber": 2041,
	"endColumn": 39
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use progress.TotalSteps (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 2042,
	"startColumn": 19,
	"endLineNumber": 2042,
	"endColumn": 38
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use progress.CompletedSteps (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 2043,
	"startColumn": 19,
	"endLineNumber": 2043,
	"endColumn": 42
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 2068,
	"startColumn": 11,
	"endLineNumber": 2068,
	"endColumn": 13
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 2088,
	"startColumn": 57,
	"endLineNumber": 2088,
	"endColumn": 59
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 2118,
	"startColumn": 54,
	"endLineNumber": 2118,
	"endColumn": 56
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use stats.TotalSites (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 2136,
	"startColumn": 21,
	"endLineNumber": 2136,
	"endColumn": 37
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use stats.TotalPages (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 2137,
	"startColumn": 21,
	"endLineNumber": 2137,
	"endColumn": 37
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use stats.TotalAddons (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 2138,
	"startColumn": 21,
	"endLineNumber": 2138,
	"endColumn": 38
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use stats.APICallsUsed (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 2143,
	"startColumn": 21,
	"endLineNumber": 2143,
	"endColumn": 39
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 2155,
	"startColumn": 46,
	"endLineNumber": 2155,
	"endColumn": 48
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use site.PageCount (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 2180,
	"startColumn": 18,
	"endLineNumber": 2180,
	"endColumn": 32
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 2201,
	"startColumn": 46,
	"endLineNumber": 2201,
	"endColumn": 48
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use sp.Position (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 2228,
	"startColumn": 16,
	"endLineNumber": 2228,
	"endColumn": 27
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use sp.Page.Position (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 2239,
	"startColumn": 16,
	"endLineNumber": 2239,
	"endColumn": 32
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use site.PageCount (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 2262,
	"startColumn": 17,
	"endLineNumber": 2262,
	"endColumn": 31
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 2280,
	"startColumn": 46,
	"endLineNumber": 2280,
	"endColumn": 48
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use sp.Position (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 2297,
	"startColumn": 16,
	"endLineNumber": 2297,
	"endColumn": 27
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use sp.Page.Position (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 2308,
	"startColumn": 16,
	"endLineNumber": 2308,
	"endColumn": 32
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 2328,
	"startColumn": 62,
	"endLineNumber": 2328,
	"endColumn": 64
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use page.Position (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 2344,
	"startColumn": 20,
	"endLineNumber": 2344,
	"endColumn": 33
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use page.ContentVersion (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 2358,
	"startColumn": 20,
	"endLineNumber": 2358,
	"endColumn": 39
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 2376,
	"startColumn": 62,
	"endLineNumber": 2376,
	"endColumn": 64
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use page.Position (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 2396,
	"startColumn": 22,
	"endLineNumber": 2396,
	"endColumn": 35
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use page.ContentVersion (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 2410,
	"startColumn": 22,
	"endLineNumber": 2410,
	"endColumn": 41
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 2428,
	"startColumn": 46,
	"endLineNumber": 2428,
	"endColumn": 48
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UnexportedName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UnexportedName"
		}
	},
	"severity": 8,
	"message": "name __InputValueResolver not exported by package generated",
	"source": "compiler",
	"startLineNumber": 2713,
	"startColumn": 45,
	"endLineNumber": 2713,
	"endColumn": 65
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UnexportedName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UnexportedName"
		}
	},
	"severity": 8,
	"message": "name __TypeResolver not exported by package generated",
	"source": "compiler",
	"startLineNumber": 2716,
	"startColumn": 39,
	"endLineNumber": 2716,
	"endColumn": 53
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/business_tools_resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "IncompatibleAssign",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "IncompatibleAssign"
		}
	},
	"severity": 8,
	"message": "cannot use section.Order (variable of type int) as int32 value in struct literal",
	"source": "compiler",
	"startLineNumber": 268,
	"startColumn": 13,
	"endLineNumber": 268,
	"endColumn": 26
}]