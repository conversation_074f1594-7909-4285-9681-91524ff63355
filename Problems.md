[{
	"resource": "/c:/Users/<USER>/Desktop/goVwPlatformAPI/cmd/api/main.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "BrokenImport",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "BrokenImport"
		}
	},
	"severity": 8,
	"message": "could not import goVwPlatformAPI/graph/generated (missing import goVwPlatformAPI/graph/generated)",
	"source": "compiler",
	"startLineNumber": 13,
	"startColumn": 2,
	"endLineNumber": 13,
	"endColumn": 35
},{
	"resource": "/c:/Users/<USER>/Desktop/goVwPlatformAPI/cmd/api/main.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "CannotInferTypeArgs",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "CannotInferTypeArgs"
		}
	},
	"severity": 8,
	"message": "in call to lru.New, cannot infer T (declared at C:\\Users\\<USER>\\go\\pkg\\mod\\github.com\\99designs\\gqlgen@v0.17.76\\graphql\\handler\\lru\\lru.go:17:10)",
	"source": "compiler",
	"startLineNumber": 1297,
	"startColumn": 10,
	"endLineNumber": 1297,
	"endColumn": 22
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "BrokenImport",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "BrokenImport"
		}
	},
	"severity": 8,
	"message": "could not import context (missing metadata for import of \"context\")",
	"source": "compiler",
	"startLineNumber": 8,
	"startColumn": 2,
	"endLineNumber": 8,
	"endColumn": 11
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "BrokenImport",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "BrokenImport"
		}
	},
	"severity": 8,
	"message": "could not import fmt (missing metadata for import of \"fmt\")",
	"source": "compiler",
	"startLineNumber": 9,
	"startColumn": 2,
	"endLineNumber": 9,
	"endColumn": 7
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"severity": 8,
	"message": "package goVwPlatformAPI/graph/generated is not in std (C:\\Program Files\\Go\\src\\goVwPlatformAPI\\graph\\generated)",
	"source": "go list",
	"startLineNumber": 10,
	"startColumn": 2,
	"endLineNumber": 10,
	"endColumn": 2
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "BrokenImport",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "BrokenImport"
		}
	},
	"severity": 8,
	"message": "could not import goVwPlatformAPI/graph/generated (missing import goVwPlatformAPI/graph/generated)",
	"source": "compiler",
	"startLineNumber": 10,
	"startColumn": 2,
	"endLineNumber": 10,
	"endColumn": 35
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "BrokenImport",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "BrokenImport"
		}
	},
	"severity": 8,
	"message": "could not import goVwPlatformAPI/graph/generated (missing metadata for import of \"goVwPlatformAPI/graph/generated\")",
	"source": "compiler",
	"startLineNumber": 10,
	"startColumn": 2,
	"endLineNumber": 10,
	"endColumn": 35
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "BrokenImport",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "BrokenImport"
		}
	},
	"severity": 8,
	"message": "could not import goVwPlatformAPI/graph/model (missing metadata for import of \"goVwPlatformAPI/graph/model\")",
	"source": "compiler",
	"startLineNumber": 11,
	"startColumn": 2,
	"endLineNumber": 11,
	"endColumn": 31
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "BrokenImport",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "BrokenImport"
		}
	},
	"severity": 8,
	"message": "could not import goVwPlatformAPI/internal/auth (missing metadata for import of \"goVwPlatformAPI/internal/auth\")",
	"source": "compiler",
	"startLineNumber": 12,
	"startColumn": 2,
	"endLineNumber": 12,
	"endColumn": 33
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "BrokenImport",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "BrokenImport"
		}
	},
	"severity": 8,
	"message": "could not import goVwPlatformAPI/internal/crypto (missing metadata for import of \"goVwPlatformAPI/internal/crypto\")",
	"source": "compiler",
	"startLineNumber": 13,
	"startColumn": 2,
	"endLineNumber": 13,
	"endColumn": 35
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "BrokenImport",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "BrokenImport"
		}
	},
	"severity": 8,
	"message": "could not import goVwPlatformAPI/internal/dashboard (missing metadata for import of \"goVwPlatformAPI/internal/dashboard\")",
	"source": "compiler",
	"startLineNumber": 14,
	"startColumn": 2,
	"endLineNumber": 14,
	"endColumn": 38
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "BrokenImport",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "BrokenImport"
		}
	},
	"severity": 8,
	"message": "could not import goVwPlatformAPI/internal/database (missing metadata for import of \"goVwPlatformAPI/internal/database\")",
	"source": "compiler",
	"startLineNumber": 15,
	"startColumn": 2,
	"endLineNumber": 15,
	"endColumn": 37
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "BrokenImport",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "BrokenImport"
		}
	},
	"severity": 8,
	"message": "could not import goVwPlatformAPI/internal/onboarding (missing metadata for import of \"goVwPlatformAPI/internal/onboarding\")",
	"source": "compiler",
	"startLineNumber": 16,
	"startColumn": 2,
	"endLineNumber": 16,
	"endColumn": 39
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "BrokenImport",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "BrokenImport"
		}
	},
	"severity": 8,
	"message": "could not import goVwPlatformAPI/internal/pagemanager (missing metadata for import of \"goVwPlatformAPI/internal/pagemanager\")",
	"source": "compiler",
	"startLineNumber": 17,
	"startColumn": 2,
	"endLineNumber": 17,
	"endColumn": 40
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "BrokenImport",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "BrokenImport"
		}
	},
	"severity": 8,
	"message": "could not import goVwPlatformAPI/internal/sitemanager (missing metadata for import of \"goVwPlatformAPI/internal/sitemanager\")",
	"source": "compiler",
	"startLineNumber": 18,
	"startColumn": 2,
	"endLineNumber": 18,
	"endColumn": 40
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "BrokenImport",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "BrokenImport"
		}
	},
	"severity": 8,
	"message": "could not import goVwPlatformAPI/internal/subscription (missing metadata for import of \"goVwPlatformAPI/internal/subscription\")",
	"source": "compiler",
	"startLineNumber": 19,
	"startColumn": 2,
	"endLineNumber": 19,
	"endColumn": 41
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "BrokenImport",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "BrokenImport"
		}
	},
	"severity": 8,
	"message": "could not import os (missing metadata for import of \"os\")",
	"source": "compiler",
	"startLineNumber": 20,
	"startColumn": 2,
	"endLineNumber": 20,
	"endColumn": 6
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "BrokenImport",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "BrokenImport"
		}
	},
	"severity": 8,
	"message": "could not import strconv (missing metadata for import of \"strconv\")",
	"source": "compiler",
	"startLineNumber": 21,
	"startColumn": 2,
	"endLineNumber": 21,
	"endColumn": 11
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "BrokenImport",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "BrokenImport"
		}
	},
	"severity": 8,
	"message": "could not import time (missing metadata for import of \"time\")",
	"source": "compiler",
	"startLineNumber": 22,
	"startColumn": 2,
	"endLineNumber": 22,
	"endColumn": 8
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "BrokenImport",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "BrokenImport"
		}
	},
	"severity": 8,
	"message": "could not import github.com/99designs/gqlgen/graphql (missing metadata for import of \"github.com/99designs/gqlgen/graphql\")",
	"source": "compiler",
	"startLineNumber": 24,
	"startColumn": 2,
	"endLineNumber": 24,
	"endColumn": 39
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "BrokenImport",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "BrokenImport"
		}
	},
	"severity": 8,
	"message": "could not import github.com/golang-jwt/jwt/v5 (missing metadata for import of \"github.com/golang-jwt/jwt/v5\")",
	"source": "compiler",
	"startLineNumber": 25,
	"startColumn": 6,
	"endLineNumber": 25,
	"endColumn": 36
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 30,
	"startColumn": 17,
	"endLineNumber": 30,
	"endColumn": 19
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 38,
	"startColumn": 10,
	"endLineNumber": 38,
	"endColumn": 12
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 74,
	"startColumn": 19,
	"endLineNumber": 74,
	"endColumn": 21
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 103,
	"startColumn": 11,
	"endLineNumber": 103,
	"endColumn": 13
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 117,
	"startColumn": 13,
	"endLineNumber": 117,
	"endColumn": 15
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 127,
	"startColumn": 11,
	"endLineNumber": 127,
	"endColumn": 13
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 141,
	"startColumn": 13,
	"endLineNumber": 141,
	"endColumn": 15
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 151,
	"startColumn": 11,
	"endLineNumber": 151,
	"endColumn": 13
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 165,
	"startColumn": 13,
	"endLineNumber": 165,
	"endColumn": 15
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 175,
	"startColumn": 11,
	"endLineNumber": 175,
	"endColumn": 13
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 189,
	"startColumn": 13,
	"endLineNumber": 189,
	"endColumn": 15
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 199,
	"startColumn": 11,
	"endLineNumber": 199,
	"endColumn": 13
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 213,
	"startColumn": 13,
	"endLineNumber": 213,
	"endColumn": 15
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 223,
	"startColumn": 11,
	"endLineNumber": 223,
	"endColumn": 13
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 253,
	"startColumn": 17,
	"endLineNumber": 253,
	"endColumn": 19
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 280,
	"startColumn": 11,
	"endLineNumber": 280,
	"endColumn": 13
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 290,
	"startColumn": 20,
	"endLineNumber": 290,
	"endColumn": 22
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 309,
	"startColumn": 13,
	"endLineNumber": 309,
	"endColumn": 15
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 324,
	"startColumn": 11,
	"endLineNumber": 324,
	"endColumn": 13
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 334,
	"startColumn": 10,
	"endLineNumber": 334,
	"endColumn": 12
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 345,
	"startColumn": 11,
	"endLineNumber": 345,
	"endColumn": 13
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 373,
	"startColumn": 11,
	"endLineNumber": 373,
	"endColumn": 13
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 391,
	"startColumn": 11,
	"endLineNumber": 391,
	"endColumn": 13
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 405,
	"startColumn": 11,
	"endLineNumber": 405,
	"endColumn": 13
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 419,
	"startColumn": 11,
	"endLineNumber": 419,
	"endColumn": 13
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 433,
	"startColumn": 11,
	"endLineNumber": 433,
	"endColumn": 13
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 447,
	"startColumn": 10,
	"endLineNumber": 447,
	"endColumn": 12
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 467,
	"startColumn": 13,
	"endLineNumber": 467,
	"endColumn": 15
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 630,
	"startColumn": 46,
	"endLineNumber": 630,
	"endColumn": 48
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 694,
	"startColumn": 46,
	"endLineNumber": 694,
	"endColumn": 48
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 762,
	"startColumn": 46,
	"endLineNumber": 762,
	"endColumn": 48
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 792,
	"startColumn": 54,
	"endLineNumber": 792,
	"endColumn": 56
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 847,
	"startColumn": 62,
	"endLineNumber": 847,
	"endColumn": 64
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 917,
	"startColumn": 62,
	"endLineNumber": 917,
	"endColumn": 64
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 937,
	"startColumn": 54,
	"endLineNumber": 937,
	"endColumn": 56
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 975,
	"startColumn": 46,
	"endLineNumber": 975,
	"endColumn": 48
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 1167,
	"startColumn": 47,
	"endLineNumber": 1167,
	"endColumn": 49
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 1201,
	"startColumn": 47,
	"endLineNumber": 1201,
	"endColumn": 49
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 1269,
	"startColumn": 47,
	"endLineNumber": 1269,
	"endColumn": 49
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 1286,
	"startColumn": 63,
	"endLineNumber": 1286,
	"endColumn": 65
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.SubscriptionService undefined (type *mutationResolver has no field or method SubscriptionService)",
	"source": "compiler",
	"startLineNumber": 1336,
	"startColumn": 11,
	"endLineNumber": 1336,
	"endColumn": 30
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.SubscriptionService undefined (type *mutationResolver has no field or method SubscriptionService)",
	"source": "compiler",
	"startLineNumber": 1342,
	"startColumn": 25,
	"endLineNumber": 1342,
	"endColumn": 44
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 1376,
	"startColumn": 63,
	"endLineNumber": 1376,
	"endColumn": 65
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 1397,
	"startColumn": 57,
	"endLineNumber": 1397,
	"endColumn": 59
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 1448,
	"startColumn": 57,
	"endLineNumber": 1448,
	"endColumn": 59
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *mutationResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 1504,
	"startColumn": 57,
	"endLineNumber": 1504,
	"endColumn": 59
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 1659,
	"startColumn": 20,
	"endLineNumber": 1659,
	"endColumn": 22
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 1709,
	"startColumn": 11,
	"endLineNumber": 1709,
	"endColumn": 13
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 1718,
	"startColumn": 11,
	"endLineNumber": 1718,
	"endColumn": 13
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 1723,
	"startColumn": 11,
	"endLineNumber": 1723,
	"endColumn": 13
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 1728,
	"startColumn": 11,
	"endLineNumber": 1728,
	"endColumn": 13
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 1735,
	"startColumn": 20,
	"endLineNumber": 1735,
	"endColumn": 22
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 1749,
	"startColumn": 22,
	"endLineNumber": 1749,
	"endColumn": 24
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 1771,
	"startColumn": 18,
	"endLineNumber": 1771,
	"endColumn": 20
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 1787,
	"startColumn": 47,
	"endLineNumber": 1787,
	"endColumn": 49
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 1841,
	"startColumn": 47,
	"endLineNumber": 1841,
	"endColumn": 49
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 1874,
	"startColumn": 47,
	"endLineNumber": 1874,
	"endColumn": 49
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 1900,
	"startColumn": 47,
	"endLineNumber": 1900,
	"endColumn": 49
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 1914,
	"startColumn": 63,
	"endLineNumber": 1914,
	"endColumn": 65
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 1949,
	"startColumn": 63,
	"endLineNumber": 1949,
	"endColumn": 65
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 1986,
	"startColumn": 63,
	"endLineNumber": 1986,
	"endColumn": 65
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 2017,
	"startColumn": 57,
	"endLineNumber": 2017,
	"endColumn": 59
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 2067,
	"startColumn": 11,
	"endLineNumber": 2067,
	"endColumn": 13
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 2087,
	"startColumn": 57,
	"endLineNumber": 2087,
	"endColumn": 59
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 2117,
	"startColumn": 54,
	"endLineNumber": 2117,
	"endColumn": 56
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 2154,
	"startColumn": 46,
	"endLineNumber": 2154,
	"endColumn": 48
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 2200,
	"startColumn": 46,
	"endLineNumber": 2200,
	"endColumn": 48
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 2279,
	"startColumn": 46,
	"endLineNumber": 2279,
	"endColumn": 48
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 2327,
	"startColumn": 62,
	"endLineNumber": 2327,
	"endColumn": 64
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 2375,
	"startColumn": 62,
	"endLineNumber": 2375,
	"endColumn": 64
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "MissingFieldOrMethod",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "MissingFieldOrMethod"
		}
	},
	"severity": 8,
	"message": "r.DB undefined (type *queryResolver has no field or method DB)",
	"source": "compiler",
	"startLineNumber": 2427,
	"startColumn": 46,
	"endLineNumber": 2427,
	"endColumn": 48
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: Resolver",
	"source": "compiler",
	"startLineNumber": 2691,
	"startColumn": 10,
	"endLineNumber": 2691,
	"endColumn": 18
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: Resolver",
	"source": "compiler",
	"startLineNumber": 2694,
	"startColumn": 10,
	"endLineNumber": 2694,
	"endColumn": 18
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: Resolver",
	"source": "compiler",
	"startLineNumber": 2696,
	"startColumn": 32,
	"endLineNumber": 2696,
	"endColumn": 40
},{
	"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go",
	"owner": "_generated_diagnostic_collection_name_#2",
	"code": {
		"value": "UndeclaredName",
		"target": {
			"$mid": 1,
			"path": "/golang.org/x/tools/internal/typesinternal",
			"scheme": "https",
			"authority": "pkg.go.dev",
			"fragment": "UndeclaredName"
		}
	},
	"severity": 8,
	"message": "undefined: Resolver",
	"source": "compiler",
	"startLineNumber": 2697,
	"startColumn": 29,
	"endLineNumber": 2697,
	"endColumn": 37
}]

gqlgen generate output 

Daniel@DESKTOP-NJ78PS9 MINGW64 ~/Desktop/goVwPlatformAPI
$ gqlgen generate
validation failed: packages.Load: -: # goVwPlatformAPI/graph/generated
graph\generated\generated.go:123074:17: ec.unmarshalInputJSON undefined (type *executionContext has no field or method unmarshalInputJSON)
graph\generated\generated.go:123085:12: ec._JSON undefined (type *executionContext has no field or method _JSON)
graph\generated\generated.go:124995:17: ec.unmarshalInputTime undefined (type *executionContext has no field or method unmarshalInputTime)
graph\generated\generated.go:125000:12: ec._Time undefined (type *executionContext has no field or method _Time)    
graph\generated\generated.go:126237:17: ec.unmarshalInputJSON undefined (type *executionContext has no field or method unmarshalInputJSON)
graph\generated\generated.go:126245:12: ec._JSON undefined (type *executionContext has no field or method _JSON)    
graph\generated\generated.go:126605:17: ec.unmarshalInputTime undefined (type *executionContext has no field or method unmarshalInputTime)
graph\generated\generated.go:126613:12: ec._Time undefined (type *executionContext has no field or method _Time)    
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\generated\generated.go:123074:17: ec.unmarshalInputJSON undefined (type *executionContext has no field or method unmarshalInputJSON)
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\generated\generated.go:123085:12: ec._JSON undefined (type *executionContext has no field or method _JSON)
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\generated\generated.go:124995:17: ec.unmarshalInputTime undefined (type *executionContext has no field or method unmarshalInputTime)
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\generated\generated.go:125000:12: ec._Time undefined (type *executionContext has no field or method _Time)
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\generated\generated.go:126237:17: ec.unmarshalInputJSON undefined (type *executionContext has no field or method unmarshalInputJSON)
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\generated\generated.go:126245:12: ec._JSON undefined (type *executionContext has no field or method _JSON)
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\generated\generated.go:126605:17: ec.unmarshalInputTime undefined (type *executionContext has no field or method unmarshalInputTime)
C:\Users\<USER>\Desktop\goVwPlatformAPI\graph\generated\generated.go:126613:12: ec._Time undefined (type *executionContext has no field or method _Time)