[{"resource": "/c:/Users/<USER>/Desktop/goVwPlatformAPI/cmd/api/main.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field DB in struct literal of type graph.Resolver", "source": "compiler", "startLineNumber": 1278, "startColumn": 3, "endLineNumber": 1278, "endColumn": 5}, {"resource": "/c:/Users/<USER>/Desktop/goVwPlatformAPI/cmd/api/main.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field SubscriptionService in struct literal of type graph.Resolver", "source": "compiler", "startLineNumber": 1279, "startColumn": 3, "endLineNumber": 1279, "endColumn": 22}, {"resource": "/c:/Users/<USER>/Desktop/goVwPlatformAPI/cmd/api/main.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field PageManager in struct literal of type graph.Resolver", "source": "compiler", "startLineNumber": 1280, "startColumn": 3, "endLineNumber": 1280, "endColumn": 14}, {"resource": "/c:/Users/<USER>/Desktop/goVwPlatformAPI/cmd/api/main.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field OnboardingService in struct literal of type graph.Resolver", "source": "compiler", "startLineNumber": 1281, "startColumn": 3, "endLineNumber": 1281, "endColumn": 20}, {"resource": "/c:/Users/<USER>/Desktop/goVwPlatformAPI/cmd/api/main.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingLitField", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingLitField"}}, "severity": 8, "message": "unknown field DashboardService in struct literal of type graph.Resolver", "source": "compiler", "startLineNumber": 1282, "startColumn": 3, "endLineNumber": 1282, "endColumn": 19}, {"resource": "/c:/Users/<USER>/Desktop/goVwPlatformAPI/cmd/api/main.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "CannotInferTypeArgs", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "CannotInferTypeArgs"}}, "severity": 8, "message": "in call to lru.<PERSON>, cannot infer T (declared at C:\\Users\\<USER>\\go\\pkg\\mod\\github.com\\99designs\\gqlgen@v0.17.76\\graphql\\handler\\lru\\lru.go:17:10)", "source": "compiler", "startLineNumber": 1297, "startColumn": 10, "endLineNumber": 1297, "endColumn": 22}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec.unmarshalInputJSON undefined (type *executionContext has no field or method unmarshalInputJSON)", "source": "compiler", "startLineNumber": 123074, "startColumn": 17, "endLineNumber": 123074, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec._JSON undefined (type *executionContext has no field or method _JSON)", "source": "compiler", "startLineNumber": 123085, "startColumn": 12, "endLineNumber": 123085, "endColumn": 17}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec.unmarshalInputTime undefined (type *executionContext has no field or method unmarshalInputTime)", "source": "compiler", "startLineNumber": 124995, "startColumn": 17, "endLineNumber": 124995, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec._Time undefined (type *executionContext has no field or method _Time)", "source": "compiler", "startLineNumber": 125000, "startColumn": 12, "endLineNumber": 125000, "endColumn": 17}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec.unmarshalInputJSON undefined (type *executionContext has no field or method unmarshalInputJSON)", "source": "compiler", "startLineNumber": 126237, "startColumn": 17, "endLineNumber": 126237, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec._JSON undefined (type *executionContext has no field or method _JSON)", "source": "compiler", "startLineNumber": 126245, "startColumn": 12, "endLineNumber": 126245, "endColumn": 17}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec.unmarshalInputTime undefined (type *executionContext has no field or method unmarshalInputTime)", "source": "compiler", "startLineNumber": 126605, "startColumn": 17, "endLineNumber": 126605, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "ec._Time undefined (type *executionContext has no field or method _Time)", "source": "compiler", "startLineNumber": 126613, "startColumn": 12, "endLineNumber": 126613, "endColumn": 17}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 25, "startColumn": 17, "endLineNumber": 25, "endColumn": 19}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 33, "startColumn": 10, "endLineNumber": 33, "endColumn": 12}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: crypto", "source": "compiler", "startLineNumber": 38, "startColumn": 6, "endLineNumber": 38, "endColumn": 12}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: jwt", "source": "compiler", "startLineNumber": 42, "startColumn": 11, "endLineNumber": 42, "endColumn": 14}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: jwt", "source": "compiler", "startLineNumber": 42, "startColumn": 29, "endLineNumber": 42, "endColumn": 32}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: jwt", "source": "compiler", "startLineNumber": 42, "startColumn": 53, "endLineNumber": 42, "endColumn": 56}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: jwt", "source": "compiler", "startLineNumber": 44, "startColumn": 14, "endLineNumber": 44, "endColumn": 17}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: os", "source": "compiler", "startLineNumber": 47, "startColumn": 48, "endLineNumber": 47, "endColumn": 50}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 60, "startColumn": 17, "endLineNumber": 60, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 65, "startColumn": 6, "endLineNumber": 65, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 69, "startColumn": 19, "endLineNumber": 69, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 89, "startColumn": 17, "endLineNumber": 89, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 94, "startColumn": 6, "endLineNumber": 94, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 98, "startColumn": 11, "endLineNumber": 98, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 103, "startColumn": 17, "endLineNumber": 103, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 108, "startColumn": 6, "endLineNumber": 108, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 112, "startColumn": 13, "endLineNumber": 112, "endColumn": 15}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 122, "startColumn": 11, "endLineNumber": 122, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 127, "startColumn": 17, "endLineNumber": 127, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 132, "startColumn": 6, "endLineNumber": 132, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 136, "startColumn": 13, "endLineNumber": 136, "endColumn": 15}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 146, "startColumn": 11, "endLineNumber": 146, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 151, "startColumn": 17, "endLineNumber": 151, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 156, "startColumn": 6, "endLineNumber": 156, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 160, "startColumn": 13, "endLineNumber": 160, "endColumn": 15}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 170, "startColumn": 11, "endLineNumber": 170, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 175, "startColumn": 17, "endLineNumber": 175, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 180, "startColumn": 6, "endLineNumber": 180, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 184, "startColumn": 13, "endLineNumber": 184, "endColumn": 15}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 194, "startColumn": 11, "endLineNumber": 194, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 199, "startColumn": 17, "endLineNumber": 199, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 204, "startColumn": 6, "endLineNumber": 204, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 208, "startColumn": 13, "endLineNumber": 208, "endColumn": 15}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 218, "startColumn": 11, "endLineNumber": 218, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: crypto", "source": "compiler", "startLineNumber": 243, "startColumn": 23, "endLineNumber": 243, "endColumn": 29}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 248, "startColumn": 17, "endLineNumber": 248, "endColumn": 19}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: jwt", "source": "compiler", "startLineNumber": 253, "startColumn": 11, "endLineNumber": 253, "endColumn": 14}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: jwt", "source": "compiler", "startLineNumber": 253, "startColumn": 29, "endLineNumber": 253, "endColumn": 32}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: jwt", "source": "compiler", "startLineNumber": 253, "startColumn": 53, "endLineNumber": 253, "endColumn": 56}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: jwt", "source": "compiler", "startLineNumber": 255, "startColumn": 14, "endLineNumber": 255, "endColumn": 17}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: os", "source": "compiler", "startLineNumber": 258, "startColumn": 48, "endLineNumber": 258, "endColumn": 50}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 271, "startColumn": 17, "endLineNumber": 271, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 275, "startColumn": 11, "endLineNumber": 275, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 280, "startColumn": 17, "endLineNumber": 280, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 285, "startColumn": 20, "endLineNumber": 285, "endColumn": 22}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 299, "startColumn": 17, "endLineNumber": 299, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 304, "startColumn": 13, "endLineNumber": 304, "endColumn": 15}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 314, "startColumn": 17, "endLineNumber": 314, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 319, "startColumn": 11, "endLineNumber": 319, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 324, "startColumn": 17, "endLineNumber": 324, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 329, "startColumn": 10, "endLineNumber": 329, "endColumn": 12}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 335, "startColumn": 17, "endLineNumber": 335, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 340, "startColumn": 11, "endLineNumber": 340, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 355, "startColumn": 17, "endLineNumber": 355, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 368, "startColumn": 11, "endLineNumber": 368, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 373, "startColumn": 17, "endLineNumber": 373, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 386, "startColumn": 11, "endLineNumber": 386, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 391, "startColumn": 17, "endLineNumber": 391, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 396, "startColumn": 6, "endLineNumber": 396, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 400, "startColumn": 11, "endLineNumber": 400, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 405, "startColumn": 17, "endLineNumber": 405, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 410, "startColumn": 6, "endLineNumber": 410, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 414, "startColumn": 11, "endLineNumber": 414, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 419, "startColumn": 17, "endLineNumber": 419, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 424, "startColumn": 6, "endLineNumber": 424, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 428, "startColumn": 11, "endLineNumber": 428, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 433, "startColumn": 17, "endLineNumber": 433, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 438, "startColumn": 6, "endLineNumber": 438, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 442, "startColumn": 10, "endLineNumber": 442, "endColumn": 12}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 452, "startColumn": 17, "endLineNumber": 452, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 457, "startColumn": 6, "endLineNumber": 457, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 462, "startColumn": 13, "endLineNumber": 462, "endColumn": 15}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 620, "startColumn": 30, "endLineNumber": 620, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 620, "startColumn": 53, "endLineNumber": 620, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: sitemanager", "source": "compiler", "startLineNumber": 625, "startColumn": 17, "endLineNumber": 625, "endColumn": 28}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 625, "startColumn": 46, "endLineNumber": 625, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: sitemanager", "source": "compiler", "startLineNumber": 627, "startColumn": 18, "endLineNumber": 627, "endColumn": 29}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 679, "startColumn": 30, "endLineNumber": 679, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 679, "startColumn": 53, "endLineNumber": 679, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: sitemanager", "source": "compiler", "startLineNumber": 689, "startColumn": 17, "endLineNumber": 689, "endColumn": 28}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 689, "startColumn": 46, "endLineNumber": 689, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: sitemanager", "source": "compiler", "startLineNumber": 691, "startColumn": 18, "endLineNumber": 691, "endColumn": 29}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 747, "startColumn": 30, "endLineNumber": 747, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 747, "startColumn": 53, "endLineNumber": 747, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: sitemanager", "source": "compiler", "startLineNumber": 757, "startColumn": 17, "endLineNumber": 757, "endColumn": 28}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 757, "startColumn": 46, "endLineNumber": 757, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 782, "startColumn": 30, "endLineNumber": 782, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 782, "startColumn": 53, "endLineNumber": 782, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 787, "startColumn": 54, "endLineNumber": 787, "endColumn": 56}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 837, "startColumn": 30, "endLineNumber": 837, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 837, "startColumn": 53, "endLineNumber": 837, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 842, "startColumn": 62, "endLineNumber": 842, "endColumn": 64}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 907, "startColumn": 30, "endLineNumber": 907, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 907, "startColumn": 53, "endLineNumber": 907, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 912, "startColumn": 62, "endLineNumber": 912, "endColumn": 64}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 927, "startColumn": 30, "endLineNumber": 927, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 927, "startColumn": 53, "endLineNumber": 927, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 932, "startColumn": 54, "endLineNumber": 932, "endColumn": 56}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 965, "startColumn": 30, "endLineNumber": 965, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 965, "startColumn": 53, "endLineNumber": 965, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: sitemanager", "source": "compiler", "startLineNumber": 970, "startColumn": 17, "endLineNumber": 970, "endColumn": 28}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 970, "startColumn": 46, "endLineNumber": 970, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: sitemanager", "source": "compiler", "startLineNumber": 977, "startColumn": 18, "endLineNumber": 977, "endColumn": 29}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1157, "startColumn": 30, "endLineNumber": 1157, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1157, "startColumn": 53, "endLineNumber": 1157, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1158, "startColumn": 13, "endLineNumber": 1158, "endColumn": 17}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1162, "startColumn": 47, "endLineNumber": 1162, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1191, "startColumn": 30, "endLineNumber": 1191, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1191, "startColumn": 53, "endLineNumber": 1191, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1192, "startColumn": 13, "endLineNumber": 1192, "endColumn": 17}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1196, "startColumn": 47, "endLineNumber": 1196, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1254, "startColumn": 30, "endLineNumber": 1254, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1254, "startColumn": 53, "endLineNumber": 1254, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1255, "startColumn": 13, "endLineNumber": 1255, "endColumn": 17}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1260, "startColumn": 11, "endLineNumber": 1260, "endColumn": 15}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1264, "startColumn": 47, "endLineNumber": 1264, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1276, "startColumn": 30, "endLineNumber": 1276, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1276, "startColumn": 53, "endLineNumber": 1276, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1277, "startColumn": 13, "endLineNumber": 1277, "endColumn": 17}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1281, "startColumn": 63, "endLineNumber": 1281, "endColumn": 65}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1325, "startColumn": 30, "endLineNumber": 1325, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1325, "startColumn": 53, "endLineNumber": 1325, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.SubscriptionService undefined (type *mutationResolver has no field or method SubscriptionService)", "source": "compiler", "startLineNumber": 1331, "startColumn": 11, "endLineNumber": 1331, "endColumn": 30}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.SubscriptionService undefined (type *mutationResolver has no field or method SubscriptionService)", "source": "compiler", "startLineNumber": 1337, "startColumn": 25, "endLineNumber": 1337, "endColumn": 44}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1366, "startColumn": 30, "endLineNumber": 1366, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1366, "startColumn": 53, "endLineNumber": 1366, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1371, "startColumn": 63, "endLineNumber": 1371, "endColumn": 65}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1387, "startColumn": 30, "endLineNumber": 1387, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1387, "startColumn": 53, "endLineNumber": 1387, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1392, "startColumn": 57, "endLineNumber": 1392, "endColumn": 59}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1438, "startColumn": 30, "endLineNumber": 1438, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1438, "startColumn": 53, "endLineNumber": 1438, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1443, "startColumn": 57, "endLineNumber": 1443, "endColumn": 59}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1494, "startColumn": 30, "endLineNumber": 1494, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1494, "startColumn": 53, "endLineNumber": 1494, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1499, "startColumn": 57, "endLineNumber": 1499, "endColumn": 59}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1645, "startColumn": 17, "endLineNumber": 1645, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1650, "startColumn": 6, "endLineNumber": 1650, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1654, "startColumn": 20, "endLineNumber": 1654, "endColumn": 22}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1700, "startColumn": 17, "endLineNumber": 1700, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1704, "startColumn": 11, "endLineNumber": 1704, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1709, "startColumn": 17, "endLineNumber": 1709, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1713, "startColumn": 11, "endLineNumber": 1713, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1718, "startColumn": 11, "endLineNumber": 1718, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1723, "startColumn": 11, "endLineNumber": 1723, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1730, "startColumn": 20, "endLineNumber": 1730, "endColumn": 22}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1744, "startColumn": 22, "endLineNumber": 1744, "endColumn": 24}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1761, "startColumn": 17, "endLineNumber": 1761, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1766, "startColumn": 18, "endLineNumber": 1766, "endColumn": 20}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1777, "startColumn": 30, "endLineNumber": 1777, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1777, "startColumn": 53, "endLineNumber": 1777, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1778, "startColumn": 13, "endLineNumber": 1778, "endColumn": 17}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1782, "startColumn": 47, "endLineNumber": 1782, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1827, "startColumn": 30, "endLineNumber": 1827, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1827, "startColumn": 53, "endLineNumber": 1827, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1832, "startColumn": 6, "endLineNumber": 1832, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1836, "startColumn": 47, "endLineNumber": 1836, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1860, "startColumn": 30, "endLineNumber": 1860, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1860, "startColumn": 53, "endLineNumber": 1860, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1865, "startColumn": 6, "endLineNumber": 1865, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1869, "startColumn": 47, "endLineNumber": 1869, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1886, "startColumn": 30, "endLineNumber": 1886, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1886, "startColumn": 53, "endLineNumber": 1886, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1891, "startColumn": 6, "endLineNumber": 1891, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1895, "startColumn": 47, "endLineNumber": 1895, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1909, "startColumn": 63, "endLineNumber": 1909, "endColumn": 65}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1939, "startColumn": 30, "endLineNumber": 1939, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1939, "startColumn": 53, "endLineNumber": 1939, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1944, "startColumn": 63, "endLineNumber": 1944, "endColumn": 65}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1976, "startColumn": 30, "endLineNumber": 1976, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1976, "startColumn": 53, "endLineNumber": 1976, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1981, "startColumn": 63, "endLineNumber": 1981, "endColumn": 65}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2007, "startColumn": 30, "endLineNumber": 2007, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2007, "startColumn": 53, "endLineNumber": 2007, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2012, "startColumn": 57, "endLineNumber": 2012, "endColumn": 59}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2046, "startColumn": 30, "endLineNumber": 2046, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2046, "startColumn": 53, "endLineNumber": 2046, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2062, "startColumn": 11, "endLineNumber": 2062, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2077, "startColumn": 30, "endLineNumber": 2077, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2077, "startColumn": 53, "endLineNumber": 2077, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2082, "startColumn": 57, "endLineNumber": 2082, "endColumn": 59}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2107, "startColumn": 30, "endLineNumber": 2107, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2107, "startColumn": 53, "endLineNumber": 2107, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2112, "startColumn": 54, "endLineNumber": 2112, "endColumn": 56}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2144, "startColumn": 30, "endLineNumber": 2144, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2144, "startColumn": 53, "endLineNumber": 2144, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: sitemanager", "source": "compiler", "startLineNumber": 2149, "startColumn": 17, "endLineNumber": 2149, "endColumn": 28}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2149, "startColumn": 46, "endLineNumber": 2149, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2185, "startColumn": 30, "endLineNumber": 2185, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2185, "startColumn": 53, "endLineNumber": 2185, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: sitemanager", "source": "compiler", "startLineNumber": 2195, "startColumn": 17, "endLineNumber": 2195, "endColumn": 28}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2195, "startColumn": 46, "endLineNumber": 2195, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2264, "startColumn": 30, "endLineNumber": 2264, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2264, "startColumn": 53, "endLineNumber": 2264, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: sitemanager", "source": "compiler", "startLineNumber": 2274, "startColumn": 17, "endLineNumber": 2274, "endColumn": 28}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2274, "startColumn": 46, "endLineNumber": 2274, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2317, "startColumn": 30, "endLineNumber": 2317, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2317, "startColumn": 53, "endLineNumber": 2317, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2322, "startColumn": 62, "endLineNumber": 2322, "endColumn": 64}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2360, "startColumn": 30, "endLineNumber": 2360, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2360, "startColumn": 53, "endLineNumber": 2360, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2370, "startColumn": 62, "endLineNumber": 2370, "endColumn": 64}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2417, "startColumn": 30, "endLineNumber": 2417, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2417, "startColumn": 53, "endLineNumber": 2417, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: sitemanager", "source": "compiler", "startLineNumber": 2422, "startColumn": 17, "endLineNumber": 2422, "endColumn": 28}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2422, "startColumn": 46, "endLineNumber": 2422, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateDecl", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateDecl"}}, "severity": 8, "message": "mutationResolver redeclared in this block (see details)", "source": "compiler", "startLineNumber": 2691, "startColumn": 6, "endLineNumber": 2691, "endColumn": 22, "relatedInformation": [{"startLineNumber": 2686, "startColumn": 6, "endLineNumber": 2686, "endColumn": 22, "message": "", "resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go"}]}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateDecl", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateDecl"}}, "severity": 8, "message": "queryResolver redeclared in this block (see details)", "source": "compiler", "startLineNumber": 2692, "startColumn": 6, "endLineNumber": 2692, "endColumn": 19, "relatedInformation": [{"startLineNumber": 2687, "startColumn": 6, "endLineNumber": 2687, "endColumn": 19, "message": "", "resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go"}]}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.<PERSON><PERSON> already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:24:28", "source": "compiler", "startLineNumber": 19, "startColumn": 28, "endLineNumber": 19, "endColumn": 33}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 20, "startColumn": 17, "endLineNumber": 20, "endColumn": 19}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 28, "startColumn": 10, "endLineNumber": 28, "endColumn": 12}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: crypto", "source": "compiler", "startLineNumber": 33, "startColumn": 6, "endLineNumber": 33, "endColumn": 12}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: jwt", "source": "compiler", "startLineNumber": 37, "startColumn": 11, "endLineNumber": 37, "endColumn": 14}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: jwt", "source": "compiler", "startLineNumber": 37, "startColumn": 29, "endLineNumber": 37, "endColumn": 32}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: jwt", "source": "compiler", "startLineNumber": 37, "startColumn": 53, "endLineNumber": 37, "endColumn": 56}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: jwt", "source": "compiler", "startLineNumber": 39, "startColumn": 14, "endLineNumber": 39, "endColumn": 17}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: os", "source": "compiler", "startLineNumber": 42, "startColumn": 48, "endLineNumber": 42, "endColumn": 50}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateAddonStatus already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:59:28", "source": "compiler", "startLineNumber": 54, "startColumn": 28, "endLineNumber": 54, "endColumn": 45}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 55, "startColumn": 17, "endLineNumber": 55, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 60, "startColumn": 6, "endLineNumber": 60, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 64, "startColumn": 19, "endLineNumber": 64, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.SaveAddonConfig already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:88:28", "source": "compiler", "startLineNumber": 83, "startColumn": 28, "endLineNumber": 83, "endColumn": 43}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 84, "startColumn": 17, "endLineNumber": 84, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 89, "startColumn": 6, "endLineNumber": 89, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 93, "startColumn": 11, "endLineNumber": 93, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.SubmitToReview already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:102:28", "source": "compiler", "startLineNumber": 97, "startColumn": 28, "endLineNumber": 97, "endColumn": 42}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 98, "startColumn": 17, "endLineNumber": 98, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 103, "startColumn": 6, "endLineNumber": 103, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 107, "startColumn": 13, "endLineNumber": 107, "endColumn": 15}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 117, "startColumn": 11, "endLineNumber": 117, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.ApproveAddon already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:126:28", "source": "compiler", "startLineNumber": 121, "startColumn": 28, "endLineNumber": 121, "endColumn": 40}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 122, "startColumn": 17, "endLineNumber": 122, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 127, "startColumn": 6, "endLineNumber": 127, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 131, "startColumn": 13, "endLineNumber": 131, "endColumn": 15}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 141, "startColumn": 11, "endLineNumber": 141, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.RevertAddonToDevelopment already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:150:28", "source": "compiler", "startLineNumber": 145, "startColumn": 28, "endLineNumber": 145, "endColumn": 52}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 146, "startColumn": 17, "endLineNumber": 146, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 151, "startColumn": 6, "endLineNumber": 151, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 155, "startColumn": 13, "endLineNumber": 155, "endColumn": 15}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 165, "startColumn": 11, "endLineNumber": 165, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.DisableAddon already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:174:28", "source": "compiler", "startLineNumber": 169, "startColumn": 28, "endLineNumber": 169, "endColumn": 40}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 170, "startColumn": 17, "endLineNumber": 170, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 175, "startColumn": 6, "endLineNumber": 175, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 179, "startColumn": 13, "endLineNumber": 179, "endColumn": 15}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 189, "startColumn": 11, "endLineNumber": 189, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.EnableAddon already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:198:28", "source": "compiler", "startLineNumber": 193, "startColumn": 28, "endLineNumber": 193, "endColumn": 39}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 194, "startColumn": 17, "endLineNumber": 194, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 199, "startColumn": 6, "endLineNumber": 199, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 203, "startColumn": 13, "endLineNumber": 203, "endColumn": 15}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 213, "startColumn": 11, "endLineNumber": 213, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateAddonMetadata already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:222:28", "source": "compiler", "startLineNumber": 217, "startColumn": 28, "endLineNumber": 217, "endColumn": 47}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.GenerateAddonPreview already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:227:28", "source": "compiler", "startLineNumber": 222, "startColumn": 28, "endLineNumber": 222, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.GetAddonPreview already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:232:28", "source": "compiler", "startLineNumber": 227, "startColumn": 28, "endLineNumber": 227, "endColumn": 43}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.StartAddonTest already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:237:28", "source": "compiler", "startLineNumber": 232, "startColumn": 28, "endLineNumber": 232, "endColumn": 42}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.Register already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:242:28", "source": "compiler", "startLineNumber": 237, "startColumn": 28, "endLineNumber": 237, "endColumn": 36}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: crypto", "source": "compiler", "startLineNumber": 238, "startColumn": 23, "endLineNumber": 238, "endColumn": 29}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 243, "startColumn": 17, "endLineNumber": 243, "endColumn": 19}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: jwt", "source": "compiler", "startLineNumber": 248, "startColumn": 11, "endLineNumber": 248, "endColumn": 14}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: jwt", "source": "compiler", "startLineNumber": 248, "startColumn": 29, "endLineNumber": 248, "endColumn": 32}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: jwt", "source": "compiler", "startLineNumber": 248, "startColumn": 53, "endLineNumber": 248, "endColumn": 56}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: jwt", "source": "compiler", "startLineNumber": 250, "startColumn": 14, "endLineNumber": 250, "endColumn": 17}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: os", "source": "compiler", "startLineNumber": 253, "startColumn": 48, "endLineNumber": 253, "endColumn": 50}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CreateVehicle already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:270:28", "source": "compiler", "startLineNumber": 265, "startColumn": 28, "endLineNumber": 265, "endColumn": 41}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 266, "startColumn": 17, "endLineNumber": 266, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 270, "startColumn": 11, "endLineNumber": 270, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateVehicle already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:279:28", "source": "compiler", "startLineNumber": 274, "startColumn": 28, "endLineNumber": 274, "endColumn": 41}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 275, "startColumn": 17, "endLineNumber": 275, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 280, "startColumn": 20, "endLineNumber": 280, "endColumn": 22}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.DeleteVehicle already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:298:28", "source": "compiler", "startLineNumber": 293, "startColumn": 28, "endLineNumber": 293, "endColumn": 41}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 294, "startColumn": 17, "endLineNumber": 294, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 299, "startColumn": 13, "endLineNumber": 299, "endColumn": 15}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.GenerateSiteAPIKey already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:313:28", "source": "compiler", "startLineNumber": 308, "startColumn": 28, "endLineNumber": 308, "endColumn": 46}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 309, "startColumn": 17, "endLineNumber": 309, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 314, "startColumn": 11, "endLineNumber": 314, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.RevokeAPIKey already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:323:28", "source": "compiler", "startLineNumber": 318, "startColumn": 28, "endLineNumber": 318, "endColumn": 40}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 319, "startColumn": 17, "endLineNumber": 319, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 324, "startColumn": 10, "endLineNumber": 324, "endColumn": 12}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.RotateAPIKey already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:334:28", "source": "compiler", "startLineNumber": 329, "startColumn": 28, "endLineNumber": 329, "endColumn": 40}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 330, "startColumn": 17, "endLineNumber": 330, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 335, "startColumn": 11, "endLineNumber": 335, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CreateWebAddon already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:344:28", "source": "compiler", "startLineNumber": 339, "startColumn": 28, "endLineNumber": 339, "endColumn": 42}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateWebAddon already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:349:28", "source": "compiler", "startLineNumber": 344, "startColumn": 28, "endLineNumber": 344, "endColumn": 42}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CreatePredefinedSnippet already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:354:28", "source": "compiler", "startLineNumber": 349, "startColumn": 28, "endLineNumber": 349, "endColumn": 51}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 350, "startColumn": 17, "endLineNumber": 350, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 363, "startColumn": 11, "endLineNumber": 363, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdatePredefinedSnippet already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:372:28", "source": "compiler", "startLineNumber": 367, "startColumn": 28, "endLineNumber": 367, "endColumn": 51}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 368, "startColumn": 17, "endLineNumber": 368, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 381, "startColumn": 11, "endLineNumber": 381, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.MovePage already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:390:28", "source": "compiler", "startLineNumber": 385, "startColumn": 28, "endLineNumber": 385, "endColumn": 36}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 386, "startColumn": 17, "endLineNumber": 386, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 391, "startColumn": 6, "endLineNumber": 391, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 395, "startColumn": 11, "endLineNumber": 395, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CreateSnippet already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:404:28", "source": "compiler", "startLineNumber": 399, "startColumn": 28, "endLineNumber": 399, "endColumn": 41}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 400, "startColumn": 17, "endLineNumber": 400, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 405, "startColumn": 6, "endLineNumber": 405, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 409, "startColumn": 11, "endLineNumber": 409, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateSnippet already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:418:28", "source": "compiler", "startLineNumber": 413, "startColumn": 28, "endLineNumber": 413, "endColumn": 41}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 414, "startColumn": 17, "endLineNumber": 414, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 419, "startColumn": 6, "endLineNumber": 419, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 423, "startColumn": 11, "endLineNumber": 423, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.DeleteSnippet already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:432:28", "source": "compiler", "startLineNumber": 427, "startColumn": 28, "endLineNumber": 427, "endColumn": 41}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 428, "startColumn": 17, "endLineNumber": 428, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 433, "startColumn": 6, "endLineNumber": 433, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 437, "startColumn": 10, "endLineNumber": 437, "endColumn": 12}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CallExternalAPI already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:451:28", "source": "compiler", "startLineNumber": 446, "startColumn": 28, "endLineNumber": 446, "endColumn": 43}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 447, "startColumn": 17, "endLineNumber": 447, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 452, "startColumn": 6, "endLineNumber": 452, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 457, "startColumn": 13, "endLineNumber": 457, "endColumn": 15}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CreateExternalAPI already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:474:28", "source": "compiler", "startLineNumber": 469, "startColumn": 28, "endLineNumber": 469, "endColumn": 45}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateExternalAPI already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:479:28", "source": "compiler", "startLineNumber": 474, "startColumn": 28, "endLineNumber": 474, "endColumn": 45}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.DeleteExternalAPI already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:484:28", "source": "compiler", "startLineNumber": 479, "startColumn": 28, "endLineNumber": 479, "endColumn": 45}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CreateAPICredentials already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:489:28", "source": "compiler", "startLineNumber": 484, "startColumn": 28, "endLineNumber": 484, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateAPICredentials already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:494:28", "source": "compiler", "startLineNumber": 489, "startColumn": 28, "endLineNumber": 489, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.DeleteAPICredentials already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:499:28", "source": "compiler", "startLineNumber": 494, "startColumn": 28, "endLineNumber": 494, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.TestAPIConnection already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:504:28", "source": "compiler", "startLineNumber": 499, "startColumn": 28, "endLineNumber": 499, "endColumn": 45}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.ProxyAPICall already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:509:28", "source": "compiler", "startLineNumber": 504, "startColumn": 28, "endLineNumber": 504, "endColumn": 40}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.AdminUpdateUserStatus already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:514:28", "source": "compiler", "startLineNumber": 509, "startColumn": 28, "endLineNumber": 509, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.AdminUpdateUserRole already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:519:28", "source": "compiler", "startLineNumber": 514, "startColumn": 28, "endLineNumber": 514, "endColumn": 47}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.AdminAddUserNote already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:524:28", "source": "compiler", "startLineNumber": 519, "startColumn": 28, "endLineNumber": 519, "endColumn": 44}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.AdminSuspendUser already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:529:28", "source": "compiler", "startLineNumber": 524, "startColumn": 28, "endLineNumber": 524, "endColumn": 44}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.AdminUnsuspendUser already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:534:28", "source": "compiler", "startLineNumber": 529, "startColumn": 28, "endLineNumber": 529, "endColumn": 46}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.AdminDeleteUser already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:539:28", "source": "compiler", "startLineNumber": 534, "startColumn": 28, "endLineNumber": 534, "endColumn": 43}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.AdminReviewExpertApplication already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:544:28", "source": "compiler", "startLineNumber": 539, "startColumn": 28, "endLineNumber": 539, "endColumn": 56}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.AdminApproveExpertApplication already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:549:28", "source": "compiler", "startLineNumber": 544, "startColumn": 28, "endLineNumber": 544, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.AdminRejectExpertApplication already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:554:28", "source": "compiler", "startLineNumber": 549, "startColumn": 28, "endLineNumber": 549, "endColumn": 56}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.AdminApproveContent already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:559:28", "source": "compiler", "startLineNumber": 554, "startColumn": 28, "endLineNumber": 554, "endColumn": 47}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.AdminRejectContent already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:564:28", "source": "compiler", "startLineNumber": 559, "startColumn": 28, "endLineNumber": 559, "endColumn": 46}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.AdminHideContent already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:569:28", "source": "compiler", "startLineNumber": 564, "startColumn": 28, "endLineNumber": 564, "endColumn": 44}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.AdminDeleteContent already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:574:28", "source": "compiler", "startLineNumber": 569, "startColumn": 28, "endLineNumber": 569, "endColumn": 46}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.AdminApproveAddon already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:579:28", "source": "compiler", "startLineNumber": 574, "startColumn": 28, "endLineNumber": 574, "endColumn": 45}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.AdminRejectAddon already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:584:28", "source": "compiler", "startLineNumber": 579, "startColumn": 28, "endLineNumber": 579, "endColumn": 44}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.AdminRequestAddonChanges already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:589:28", "source": "compiler", "startLineNumber": 584, "startColumn": 28, "endLineNumber": 584, "endColumn": 52}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateProfile already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:594:28", "source": "compiler", "startLineNumber": 589, "startColumn": 28, "endLineNumber": 589, "endColumn": 41}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateSettings already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:599:28", "source": "compiler", "startLineNumber": 594, "startColumn": 28, "endLineNumber": 594, "endColumn": 42}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.ChangePassword already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:604:28", "source": "compiler", "startLineNumber": 599, "startColumn": 28, "endLineNumber": 599, "endColumn": 42}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UploadAvatar already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:609:28", "source": "compiler", "startLineNumber": 604, "startColumn": 28, "endLineNumber": 604, "endColumn": 40}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.DeleteAccount already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:614:28", "source": "compiler", "startLineNumber": 609, "startColumn": 28, "endLineNumber": 609, "endColumn": 41}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CreateSite already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:619:28", "source": "compiler", "startLineNumber": 614, "startColumn": 28, "endLineNumber": 614, "endColumn": 38}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 615, "startColumn": 30, "endLineNumber": 615, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 615, "startColumn": 53, "endLineNumber": 615, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: sitemanager", "source": "compiler", "startLineNumber": 620, "startColumn": 17, "endLineNumber": 620, "endColumn": 28}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 620, "startColumn": 46, "endLineNumber": 620, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: sitemanager", "source": "compiler", "startLineNumber": 622, "startColumn": 18, "endLineNumber": 622, "endColumn": 29}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateSite already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:678:28", "source": "compiler", "startLineNumber": 673, "startColumn": 28, "endLineNumber": 673, "endColumn": 38}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 674, "startColumn": 30, "endLineNumber": 674, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 674, "startColumn": 53, "endLineNumber": 674, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: sitemanager", "source": "compiler", "startLineNumber": 684, "startColumn": 17, "endLineNumber": 684, "endColumn": 28}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 684, "startColumn": 46, "endLineNumber": 684, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: sitemanager", "source": "compiler", "startLineNumber": 686, "startColumn": 18, "endLineNumber": 686, "endColumn": 29}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.DeleteSite already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:746:28", "source": "compiler", "startLineNumber": 741, "startColumn": 28, "endLineNumber": 741, "endColumn": 38}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 742, "startColumn": 30, "endLineNumber": 742, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 742, "startColumn": 53, "endLineNumber": 742, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: sitemanager", "source": "compiler", "startLineNumber": 752, "startColumn": 17, "endLineNumber": 752, "endColumn": 28}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 752, "startColumn": 46, "endLineNumber": 752, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.PublishSite already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:771:28", "source": "compiler", "startLineNumber": 766, "startColumn": 28, "endLineNumber": 766, "endColumn": 39}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UnpublishSite already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:776:28", "source": "compiler", "startLineNumber": 771, "startColumn": 28, "endLineNumber": 771, "endColumn": 41}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CreatePage already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:781:28", "source": "compiler", "startLineNumber": 776, "startColumn": 28, "endLineNumber": 776, "endColumn": 38}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 777, "startColumn": 30, "endLineNumber": 777, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 777, "startColumn": 53, "endLineNumber": 777, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: pagemanager", "source": "compiler", "startLineNumber": 782, "startColumn": 17, "endLineNumber": 782, "endColumn": 28}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 782, "startColumn": 54, "endLineNumber": 782, "endColumn": 56}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: pagemanager", "source": "compiler", "startLineNumber": 788, "startColumn": 18, "endLineNumber": 788, "endColumn": 29}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdatePage already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:836:28", "source": "compiler", "startLineNumber": 831, "startColumn": 28, "endLineNumber": 831, "endColumn": 38}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 832, "startColumn": 30, "endLineNumber": 832, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 832, "startColumn": 53, "endLineNumber": 832, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: pagemanager", "source": "compiler", "startLineNumber": 837, "startColumn": 25, "endLineNumber": 837, "endColumn": 36}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 837, "startColumn": 62, "endLineNumber": 837, "endColumn": 64}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: pagemanager", "source": "compiler", "startLineNumber": 839, "startColumn": 18, "endLineNumber": 839, "endColumn": 29}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.DeletePage already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:906:28", "source": "compiler", "startLineNumber": 901, "startColumn": 28, "endLineNumber": 901, "endColumn": 38}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 902, "startColumn": 30, "endLineNumber": 902, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 902, "startColumn": 53, "endLineNumber": 902, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: pagemanager", "source": "compiler", "startLineNumber": 907, "startColumn": 25, "endLineNumber": 907, "endColumn": 36}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 907, "startColumn": 62, "endLineNumber": 907, "endColumn": 64}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.PublishPage already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:926:28", "source": "compiler", "startLineNumber": 921, "startColumn": 28, "endLineNumber": 921, "endColumn": 39}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 922, "startColumn": 30, "endLineNumber": 922, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 922, "startColumn": 53, "endLineNumber": 922, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: pagemanager", "source": "compiler", "startLineNumber": 927, "startColumn": 17, "endLineNumber": 927, "endColumn": 28}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 927, "startColumn": 54, "endLineNumber": 927, "endColumn": 56}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: pagemanager", "source": "compiler", "startLineNumber": 934, "startColumn": 80, "endLineNumber": 934, "endColumn": 91}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UnpublishPage already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:959:28", "source": "compiler", "startLineNumber": 954, "startColumn": 28, "endLineNumber": 954, "endColumn": 41}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateSiteSettings already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:964:28", "source": "compiler", "startLineNumber": 959, "startColumn": 28, "endLineNumber": 959, "endColumn": 46}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 960, "startColumn": 30, "endLineNumber": 960, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 960, "startColumn": 53, "endLineNumber": 960, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: sitemanager", "source": "compiler", "startLineNumber": 965, "startColumn": 17, "endLineNumber": 965, "endColumn": 28}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 965, "startColumn": 46, "endLineNumber": 965, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: sitemanager", "source": "compiler", "startLineNumber": 972, "startColumn": 18, "endLineNumber": 972, "endColumn": 29}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.SubmitExpertApplication already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1025:28", "source": "compiler", "startLineNumber": 1020, "startColumn": 28, "endLineNumber": 1020, "endColumn": 51}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateExpertProfile already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1030:28", "source": "compiler", "startLineNumber": 1025, "startColumn": 28, "endLineNumber": 1025, "endColumn": 47}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.AddPortfolioItem already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1035:28", "source": "compiler", "startLineNumber": 1030, "startColumn": 28, "endLineNumber": 1030, "endColumn": 44}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.RemovePortfolioItem already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1040:28", "source": "compiler", "startLineNumber": 1035, "startColumn": 28, "endLineNumber": 1035, "endColumn": 47}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.AddCertification already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1045:28", "source": "compiler", "startLineNumber": 1040, "startColumn": 28, "endLineNumber": 1040, "endColumn": 44}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.RemoveCertification already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1050:28", "source": "compiler", "startLineNumber": 1045, "startColumn": 28, "endLineNumber": 1045, "endColumn": 47}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CreateProject already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1055:28", "source": "compiler", "startLineNumber": 1050, "startColumn": 28, "endLineNumber": 1050, "endColumn": 41}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateProject already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1060:28", "source": "compiler", "startLineNumber": 1055, "startColumn": 28, "endLineNumber": 1055, "endColumn": 41}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.AcceptProject already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1065:28", "source": "compiler", "startLineNumber": 1060, "startColumn": 28, "endLineNumber": 1060, "endColumn": 41}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CompleteProject already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1070:28", "source": "compiler", "startLineNumber": 1065, "startColumn": 28, "endLineNumber": 1065, "endColumn": 43}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.AddProjectMessage already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1075:28", "source": "compiler", "startLineNumber": 1070, "startColumn": 28, "endLineNumber": 1070, "endColumn": 45}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CreateExpertReview already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1080:28", "source": "compiler", "startLineNumber": 1075, "startColumn": 28, "endLineNumber": 1075, "endColumn": 46}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateExpertReview already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1085:28", "source": "compiler", "startLineNumber": 1080, "startColumn": 28, "endLineNumber": 1080, "endColumn": 46}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CreateSupportTicket already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1090:28", "source": "compiler", "startLineNumber": 1085, "startColumn": 28, "endLineNumber": 1085, "endColumn": 47}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateSupportTicket already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1095:28", "source": "compiler", "startLineNumber": 1090, "startColumn": 28, "endLineNumber": 1090, "endColumn": 47}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.AddTicketMessage already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1100:28", "source": "compiler", "startLineNumber": 1095, "startColumn": 28, "endLineNumber": 1095, "endColumn": 44}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CloseSupportTicket already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1105:28", "source": "compiler", "startLineNumber": 1100, "startColumn": 28, "endLineNumber": 1100, "endColumn": 46}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.MarkNotificationRead already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1110:28", "source": "compiler", "startLineNumber": 1105, "startColumn": 28, "endLineNumber": 1105, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.MarkAllNotificationsRead already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1115:28", "source": "compiler", "startLineNumber": 1110, "startColumn": 28, "endLineNumber": 1110, "endColumn": 52}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.DeleteNotification already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1120:28", "source": "compiler", "startLineNumber": 1115, "startColumn": 28, "endLineNumber": 1115, "endColumn": 46}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CreateBusinessPlan already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1125:28", "source": "compiler", "startLineNumber": 1120, "startColumn": 28, "endLineNumber": 1120, "endColumn": 46}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateBusinessPlan already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1130:28", "source": "compiler", "startLineNumber": 1125, "startColumn": 28, "endLineNumber": 1125, "endColumn": 46}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateBusinessPlanSection already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1135:28", "source": "compiler", "startLineNumber": 1130, "startColumn": 28, "endLineNumber": 1130, "endColumn": 53}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CreateFinancialPlan already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1140:28", "source": "compiler", "startLineNumber": 1135, "startColumn": 28, "endLineNumber": 1135, "endColumn": 47}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateFinancialPlan already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1145:28", "source": "compiler", "startLineNumber": 1140, "startColumn": 28, "endLineNumber": 1140, "endColumn": 47}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CreateComplianceCheck already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1150:28", "source": "compiler", "startLineNumber": 1145, "startColumn": 28, "endLineNumber": 1145, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CreateTenant already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1155:28", "source": "compiler", "startLineNumber": 1150, "startColumn": 28, "endLineNumber": 1150, "endColumn": 40}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1152, "startColumn": 30, "endLineNumber": 1152, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1152, "startColumn": 53, "endLineNumber": 1152, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1153, "startColumn": 13, "endLineNumber": 1153, "endColumn": 17}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: database", "source": "compiler", "startLineNumber": 1157, "startColumn": 19, "endLineNumber": 1157, "endColumn": 27}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1157, "startColumn": 47, "endLineNumber": 1157, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateTenant already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1189:28", "source": "compiler", "startLineNumber": 1184, "startColumn": 28, "endLineNumber": 1184, "endColumn": 40}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1186, "startColumn": 30, "endLineNumber": 1186, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1186, "startColumn": 53, "endLineNumber": 1186, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1187, "startColumn": 13, "endLineNumber": 1187, "endColumn": 17}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: database", "source": "compiler", "startLineNumber": 1191, "startColumn": 19, "endLineNumber": 1191, "endColumn": 27}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1191, "startColumn": 47, "endLineNumber": 1191, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.DeleteTenant already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1252:28", "source": "compiler", "startLineNumber": 1247, "startColumn": 28, "endLineNumber": 1247, "endColumn": 40}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1249, "startColumn": 30, "endLineNumber": 1249, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1249, "startColumn": 53, "endLineNumber": 1249, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1250, "startColumn": 13, "endLineNumber": 1250, "endColumn": 17}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1255, "startColumn": 11, "endLineNumber": 1255, "endColumn": 15}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: database", "source": "compiler", "startLineNumber": 1259, "startColumn": 19, "endLineNumber": 1259, "endColumn": 27}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1259, "startColumn": 47, "endLineNumber": 1259, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CreateSubscriptionTier already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1274:28", "source": "compiler", "startLineNumber": 1269, "startColumn": 28, "endLineNumber": 1269, "endColumn": 50}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1271, "startColumn": 30, "endLineNumber": 1271, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1271, "startColumn": 53, "endLineNumber": 1271, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1272, "startColumn": 13, "endLineNumber": 1272, "endColumn": 17}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: subscription", "source": "compiler", "startLineNumber": 1276, "startColumn": 25, "endLineNumber": 1276, "endColumn": 37}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1276, "startColumn": 63, "endLineNumber": 1276, "endColumn": 65}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateSubscriptionTier already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1314:28", "source": "compiler", "startLineNumber": 1309, "startColumn": 28, "endLineNumber": 1309, "endColumn": 50}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.DeleteSubscriptionTier already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1319:28", "source": "compiler", "startLineNumber": 1314, "startColumn": 28, "endLineNumber": 1314, "endColumn": 50}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpgradeSubscription already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1324:28", "source": "compiler", "startLineNumber": 1319, "startColumn": 28, "endLineNumber": 1319, "endColumn": 47}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1320, "startColumn": 30, "endLineNumber": 1320, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1320, "startColumn": 53, "endLineNumber": 1320, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.SubscriptionService undefined (type *mutationResolver has no field or method SubscriptionService)", "source": "compiler", "startLineNumber": 1326, "startColumn": 11, "endLineNumber": 1326, "endColumn": 30}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.SubscriptionService undefined (type *mutationResolver has no field or method SubscriptionService)", "source": "compiler", "startLineNumber": 1332, "startColumn": 25, "endLineNumber": 1332, "endColumn": 44}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.DowngradeSubscription already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1355:28", "source": "compiler", "startLineNumber": 1350, "startColumn": 28, "endLineNumber": 1350, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CancelSubscription already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1360:28", "source": "compiler", "startLineNumber": 1355, "startColumn": 28, "endLineNumber": 1355, "endColumn": 46}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.TrackUsage already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1365:28", "source": "compiler", "startLineNumber": 1360, "startColumn": 28, "endLineNumber": 1360, "endColumn": 38}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1361, "startColumn": 30, "endLineNumber": 1361, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1361, "startColumn": 53, "endLineNumber": 1361, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: subscription", "source": "compiler", "startLineNumber": 1366, "startColumn": 25, "endLineNumber": 1366, "endColumn": 37}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1366, "startColumn": 63, "endLineNumber": 1366, "endColumn": 65}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.GenerateInvoice already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1381:28", "source": "compiler", "startLineNumber": 1376, "startColumn": 28, "endLineNumber": 1376, "endColumn": 43}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CompleteOnboardingStep already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1386:28", "source": "compiler", "startLineNumber": 1381, "startColumn": 28, "endLineNumber": 1381, "endColumn": 50}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1382, "startColumn": 30, "endLineNumber": 1382, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1382, "startColumn": 53, "endLineNumber": 1382, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: onboarding", "source": "compiler", "startLineNumber": 1387, "startColumn": 23, "endLineNumber": 1387, "endColumn": 33}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1387, "startColumn": 57, "endLineNumber": 1387, "endColumn": 59}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateUserProfile already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1437:28", "source": "compiler", "startLineNumber": 1432, "startColumn": 28, "endLineNumber": 1432, "endColumn": 45}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1433, "startColumn": 30, "endLineNumber": 1433, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1433, "startColumn": 53, "endLineNumber": 1433, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: onboarding", "source": "compiler", "startLineNumber": 1438, "startColumn": 23, "endLineNumber": 1438, "endColumn": 33}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1438, "startColumn": 57, "endLineNumber": 1438, "endColumn": 59}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: onboarding", "source": "compiler", "startLineNumber": 1440, "startColumn": 14, "endLineNumber": 1440, "endColumn": 24}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateUserSettings already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1493:28", "source": "compiler", "startLineNumber": 1488, "startColumn": 28, "endLineNumber": 1488, "endColumn": 46}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1489, "startColumn": 30, "endLineNumber": 1489, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1489, "startColumn": 53, "endLineNumber": 1489, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: onboarding", "source": "compiler", "startLineNumber": 1494, "startColumn": 23, "endLineNumber": 1494, "endColumn": 33}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *mutationResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1494, "startColumn": 57, "endLineNumber": 1494, "endColumn": 59}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.DuplicatePage already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1549:28", "source": "compiler", "startLineNumber": 1544, "startColumn": 28, "endLineNumber": 1544, "endColumn": 41}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.GetSiteSettings already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1554:28", "source": "compiler", "startLineNumber": 1549, "startColumn": 28, "endLineNumber": 1549, "endColumn": 43}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CompileSite already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1559:28", "source": "compiler", "startLineNumber": 1554, "startColumn": 28, "endLineNumber": 1554, "endColumn": 39}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CreateExpertApplication already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1564:28", "source": "compiler", "startLineNumber": 1559, "startColumn": 28, "endLineNumber": 1559, "endColumn": 51}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.ReviewExpertApplication already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1569:28", "source": "compiler", "startLineNumber": 1564, "startColumn": 28, "endLineNumber": 1564, "endColumn": 51}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CreateClientRequirement already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1574:28", "source": "compiler", "startLineNumber": 1569, "startColumn": 28, "endLineNumber": 1569, "endColumn": 51}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.FindExpertMatches already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1579:28", "source": "compiler", "startLineNumber": 1574, "startColumn": 28, "endLineNumber": 1574, "endColumn": 45}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.ContactExpert already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1584:28", "source": "compiler", "startLineNumber": 1579, "startColumn": 28, "endLineNumber": 1579, "endColumn": 41}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.RespondToMatch already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1589:28", "source": "compiler", "startLineNumber": 1584, "startColumn": 28, "endLineNumber": 1584, "endColumn": 42}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CreateProjectEngagement already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1594:28", "source": "compiler", "startLineNumber": 1589, "startColumn": 28, "endLineNumber": 1589, "endColumn": 51}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateEngagementStatus already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1599:28", "source": "compiler", "startLineNumber": 1594, "startColumn": 28, "endLineNumber": 1594, "endColumn": 50}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.AddProjectMilestone already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1604:28", "source": "compiler", "startLineNumber": 1599, "startColumn": 28, "endLineNumber": 1599, "endColumn": 47}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CompleteProjectMilestone already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1609:28", "source": "compiler", "startLineNumber": 1604, "startColumn": 28, "endLineNumber": 1604, "endColumn": 52}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.CreateConversation already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1614:28", "source": "compiler", "startLineNumber": 1609, "startColumn": 28, "endLineNumber": 1609, "endColumn": 46}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.SendMessage already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1619:28", "source": "compiler", "startLineNumber": 1614, "startColumn": 28, "endLineNumber": 1614, "endColumn": 39}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.MarkMessageAsRead already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1624:28", "source": "compiler", "startLineNumber": 1619, "startColumn": 28, "endLineNumber": 1619, "endColumn": 45}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.MarkConversationAsRead already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1629:28", "source": "compiler", "startLineNumber": 1624, "startColumn": 28, "endLineNumber": 1624, "endColumn": 50}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method mutationResolver.UpdateMilestoneProgress already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1634:28", "source": "compiler", "startLineNumber": 1629, "startColumn": 28, "endLineNumber": 1629, "endColumn": 51}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.Health already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1639:25", "source": "compiler", "startLineNumber": 1634, "startColumn": 25, "endLineNumber": 1634, "endColumn": 31}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.GetAddonsInReview already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1644:25", "source": "compiler", "startLineNumber": 1639, "startColumn": 25, "endLineNumber": 1639, "endColumn": 42}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1640, "startColumn": 17, "endLineNumber": 1640, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1645, "startColumn": 6, "endLineNumber": 1645, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1649, "startColumn": 20, "endLineNumber": 1649, "endColumn": 22}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.GetAddonTestSession already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1689:25", "source": "compiler", "startLineNumber": 1684, "startColumn": 25, "endLineNumber": 1684, "endColumn": 44}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.GetAddonTestHistory already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1694:25", "source": "compiler", "startLineNumber": 1689, "startColumn": 25, "endLineNumber": 1689, "endColumn": 44}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.Vehicles already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1699:25", "source": "compiler", "startLineNumber": 1694, "startColumn": 25, "endLineNumber": 1694, "endColumn": 33}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1695, "startColumn": 17, "endLineNumber": 1695, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1699, "startColumn": 11, "endLineNumber": 1699, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.VehicleByVin already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1708:25", "source": "compiler", "startLineNumber": 1703, "startColumn": 25, "endLineNumber": 1703, "endColumn": 37}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1704, "startColumn": 17, "endLineNumber": 1704, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1708, "startColumn": 11, "endLineNumber": 1708, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.WebAddons already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1717:25", "source": "compiler", "startLineNumber": 1712, "startColumn": 25, "endLineNumber": 1712, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1713, "startColumn": 11, "endLineNumber": 1713, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.PredefinedSnippets already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1722:25", "source": "compiler", "startLineNumber": 1717, "startColumn": 25, "endLineNumber": 1717, "endColumn": 43}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1718, "startColumn": 11, "endLineNumber": 1718, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.Component already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1727:25", "source": "compiler", "startLineNumber": 1722, "startColumn": 25, "endLineNumber": 1722, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1725, "startColumn": 20, "endLineNumber": 1725, "endColumn": 22}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1739, "startColumn": 22, "endLineNumber": 1739, "endColumn": 24}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.GetNavigation already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1760:25", "source": "compiler", "startLineNumber": 1755, "startColumn": 25, "endLineNumber": 1755, "endColumn": 38}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1756, "startColumn": 17, "endLineNumber": 1756, "endColumn": 21}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1761, "startColumn": 18, "endLineNumber": 1761, "endColumn": 20}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.Tenants already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1775:25", "source": "compiler", "startLineNumber": 1770, "startColumn": 25, "endLineNumber": 1770, "endColumn": 32}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1772, "startColumn": 30, "endLineNumber": 1772, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1772, "startColumn": 53, "endLineNumber": 1772, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1773, "startColumn": 13, "endLineNumber": 1773, "endColumn": 17}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: database", "source": "compiler", "startLineNumber": 1777, "startColumn": 19, "endLineNumber": 1777, "endColumn": 27}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1777, "startColumn": 47, "endLineNumber": 1777, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.Tenant already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1825:25", "source": "compiler", "startLineNumber": 1820, "startColumn": 25, "endLineNumber": 1820, "endColumn": 31}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1822, "startColumn": 30, "endLineNumber": 1822, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1822, "startColumn": 53, "endLineNumber": 1822, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1827, "startColumn": 6, "endLineNumber": 1827, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: database", "source": "compiler", "startLineNumber": 1831, "startColumn": 19, "endLineNumber": 1831, "endColumn": 27}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1831, "startColumn": 47, "endLineNumber": 1831, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.TenantStats already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1858:25", "source": "compiler", "startLineNumber": 1853, "startColumn": 25, "endLineNumber": 1853, "endColumn": 36}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1855, "startColumn": 30, "endLineNumber": 1855, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1855, "startColumn": 53, "endLineNumber": 1855, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1860, "startColumn": 6, "endLineNumber": 1860, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: database", "source": "compiler", "startLineNumber": 1864, "startColumn": 19, "endLineNumber": 1864, "endColumn": 27}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1864, "startColumn": 47, "endLineNumber": 1864, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.TenantLimits already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1884:25", "source": "compiler", "startLineNumber": 1879, "startColumn": 25, "endLineNumber": 1879, "endColumn": 37}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1881, "startColumn": 30, "endLineNumber": 1881, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1881, "startColumn": 53, "endLineNumber": 1881, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1886, "startColumn": 6, "endLineNumber": 1886, "endColumn": 10}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: database", "source": "compiler", "startLineNumber": 1890, "startColumn": 19, "endLineNumber": 1890, "endColumn": 27}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1890, "startColumn": 47, "endLineNumber": 1890, "endColumn": 49}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.SubscriptionTiers already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1908:25", "source": "compiler", "startLineNumber": 1903, "startColumn": 25, "endLineNumber": 1903, "endColumn": 42}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: subscription", "source": "compiler", "startLineNumber": 1904, "startColumn": 25, "endLineNumber": 1904, "endColumn": 37}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1904, "startColumn": 63, "endLineNumber": 1904, "endColumn": 65}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.SubscriptionTier already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1933:25", "source": "compiler", "startLineNumber": 1928, "startColumn": 25, "endLineNumber": 1928, "endColumn": 41}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.UserSubscription already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1938:25", "source": "compiler", "startLineNumber": 1933, "startColumn": 25, "endLineNumber": 1933, "endColumn": 41}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1934, "startColumn": 30, "endLineNumber": 1934, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1934, "startColumn": 53, "endLineNumber": 1934, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: subscription", "source": "compiler", "startLineNumber": 1939, "startColumn": 25, "endLineNumber": 1939, "endColumn": 37}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1939, "startColumn": 63, "endLineNumber": 1939, "endColumn": 65}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.UserSubscriptionHistory already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1970:25", "source": "compiler", "startLineNumber": 1965, "startColumn": 25, "endLineNumber": 1965, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.CurrentUsage already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1975:25", "source": "compiler", "startLineNumber": 1970, "startColumn": 25, "endLineNumber": 1970, "endColumn": 37}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1971, "startColumn": 30, "endLineNumber": 1971, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 1971, "startColumn": 53, "endLineNumber": 1971, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: subscription", "source": "compiler", "startLineNumber": 1976, "startColumn": 25, "endLineNumber": 1976, "endColumn": 37}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 1976, "startColumn": 63, "endLineNumber": 1976, "endColumn": 65}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.UsageHistory already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1991:25", "source": "compiler", "startLineNumber": 1986, "startColumn": 25, "endLineNumber": 1986, "endColumn": 37}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.UserInvoices already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:1996:25", "source": "compiler", "startLineNumber": 1991, "startColumn": 25, "endLineNumber": 1991, "endColumn": 37}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.Invoice already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2001:25", "source": "compiler", "startLineNumber": 1996, "startColumn": 25, "endLineNumber": 1996, "endColumn": 32}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.OnboardingProgress already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2006:25", "source": "compiler", "startLineNumber": 2001, "startColumn": 25, "endLineNumber": 2001, "endColumn": 43}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2002, "startColumn": 30, "endLineNumber": 2002, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2002, "startColumn": 53, "endLineNumber": 2002, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: onboarding", "source": "compiler", "startLineNumber": 2007, "startColumn": 23, "endLineNumber": 2007, "endColumn": 33}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2007, "startColumn": 57, "endLineNumber": 2007, "endColumn": 59}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.UserProfile already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2045:25", "source": "compiler", "startLineNumber": 2040, "startColumn": 25, "endLineNumber": 2040, "endColumn": 36}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2041, "startColumn": 30, "endLineNumber": 2041, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2041, "startColumn": 53, "endLineNumber": 2041, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2057, "startColumn": 11, "endLineNumber": 2057, "endColumn": 13}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.UserSettings already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2076:25", "source": "compiler", "startLineNumber": 2071, "startColumn": 25, "endLineNumber": 2071, "endColumn": 37}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2072, "startColumn": 30, "endLineNumber": 2072, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2072, "startColumn": 53, "endLineNumber": 2072, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: onboarding", "source": "compiler", "startLineNumber": 2077, "startColumn": 23, "endLineNumber": 2077, "endColumn": 33}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2077, "startColumn": 57, "endLineNumber": 2077, "endColumn": 59}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.DashboardStats already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2106:25", "source": "compiler", "startLineNumber": 2101, "startColumn": 25, "endLineNumber": 2101, "endColumn": 39}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2102, "startColumn": 30, "endLineNumber": 2102, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2102, "startColumn": 53, "endLineNumber": 2102, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: dashboard", "source": "compiler", "startLineNumber": 2107, "startColumn": 22, "endLineNumber": 2107, "endColumn": 31}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2107, "startColumn": 54, "endLineNumber": 2107, "endColumn": 56}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.MySites already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2143:25", "source": "compiler", "startLineNumber": 2138, "startColumn": 25, "endLineNumber": 2138, "endColumn": 32}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2139, "startColumn": 30, "endLineNumber": 2139, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2139, "startColumn": 53, "endLineNumber": 2139, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: sitemanager", "source": "compiler", "startLineNumber": 2144, "startColumn": 17, "endLineNumber": 2144, "endColumn": 28}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2144, "startColumn": 46, "endLineNumber": 2144, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.Site already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2184:25", "source": "compiler", "startLineNumber": 2179, "startColumn": 25, "endLineNumber": 2179, "endColumn": 29}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2180, "startColumn": 30, "endLineNumber": 2180, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2180, "startColumn": 53, "endLineNumber": 2180, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: sitemanager", "source": "compiler", "startLineNumber": 2190, "startColumn": 17, "endLineNumber": 2190, "endColumn": 28}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2190, "startColumn": 46, "endLineNumber": 2190, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.SitePages already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2263:25", "source": "compiler", "startLineNumber": 2258, "startColumn": 25, "endLineNumber": 2258, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2259, "startColumn": 30, "endLineNumber": 2259, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2259, "startColumn": 53, "endLineNumber": 2259, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: sitemanager", "source": "compiler", "startLineNumber": 2269, "startColumn": 17, "endLineNumber": 2269, "endColumn": 28}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2269, "startColumn": 46, "endLineNumber": 2269, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.Page already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2316:25", "source": "compiler", "startLineNumber": 2311, "startColumn": 25, "endLineNumber": 2311, "endColumn": 29}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2312, "startColumn": 30, "endLineNumber": 2312, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2312, "startColumn": 53, "endLineNumber": 2312, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: pagemanager", "source": "compiler", "startLineNumber": 2317, "startColumn": 25, "endLineNumber": 2317, "endColumn": 36}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2317, "startColumn": 62, "endLineNumber": 2317, "endColumn": 64}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.PagesByParent already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2359:25", "source": "compiler", "startLineNumber": 2354, "startColumn": 25, "endLineNumber": 2354, "endColumn": 38}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2355, "startColumn": 30, "endLineNumber": 2355, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2355, "startColumn": 53, "endLineNumber": 2355, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: pagemanager", "source": "compiler", "startLineNumber": 2365, "startColumn": 25, "endLineNumber": 2365, "endColumn": 36}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2365, "startColumn": 62, "endLineNumber": 2365, "endColumn": 64}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.SiteSettings already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2416:25", "source": "compiler", "startLineNumber": 2411, "startColumn": 25, "endLineNumber": 2411, "endColumn": 37}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2412, "startColumn": 30, "endLineNumber": 2412, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: auth", "source": "compiler", "startLineNumber": 2412, "startColumn": 53, "endLineNumber": 2412, "endColumn": 57}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: sitemanager", "source": "compiler", "startLineNumber": 2417, "startColumn": 17, "endLineNumber": 2417, "endColumn": 28}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "MissingFieldOrMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "MissingFieldOrMethod"}}, "severity": 8, "message": "r.<PERSON> undefined (type *queryResolver has no field or method DB)", "source": "compiler", "startLineNumber": 2417, "startColumn": 46, "endLineNumber": 2417, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.SitePublishingHistory already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2451:25", "source": "compiler", "startLineNumber": 2446, "startColumn": 25, "endLineNumber": 2446, "endColumn": 46}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.SiteVersions already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2456:25", "source": "compiler", "startLineNumber": 2451, "startColumn": 25, "endLineNumber": 2451, "endColumn": 37}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.ExpertApplications already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2461:25", "source": "compiler", "startLineNumber": 2456, "startColumn": 25, "endLineNumber": 2456, "endColumn": 43}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.ExpertApplication already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2466:25", "source": "compiler", "startLineNumber": 2461, "startColumn": 25, "endLineNumber": 2461, "endColumn": 42}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.MyExpertApplication already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2471:25", "source": "compiler", "startLineNumber": 2466, "startColumn": 25, "endLineNumber": 2466, "endColumn": 44}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.ClientRequirements already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2476:25", "source": "compiler", "startLineNumber": 2471, "startColumn": 25, "endLineNumber": 2471, "endColumn": 43}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.ClientRequirement already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2481:25", "source": "compiler", "startLineNumber": 2476, "startColumn": 25, "endLineNumber": 2476, "endColumn": 42}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.MyClientRequirements already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2486:25", "source": "compiler", "startLineNumber": 2481, "startColumn": 25, "endLineNumber": 2481, "endColumn": 45}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.ExpertMatches already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2491:25", "source": "compiler", "startLineNumber": 2486, "startColumn": 25, "endLineNumber": 2486, "endColumn": 38}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.MyMatches already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2496:25", "source": "compiler", "startLineNumber": 2491, "startColumn": 25, "endLineNumber": 2491, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.ProjectEngagements already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2501:25", "source": "compiler", "startLineNumber": 2496, "startColumn": 25, "endLineNumber": 2496, "endColumn": 43}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.ProjectEngagement already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2506:25", "source": "compiler", "startLineNumber": 2501, "startColumn": 25, "endLineNumber": 2501, "endColumn": 42}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.MyProjectEngagements already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2511:25", "source": "compiler", "startLineNumber": 2506, "startColumn": 25, "endLineNumber": 2506, "endColumn": 45}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.MyConversations already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2516:25", "source": "compiler", "startLineNumber": 2511, "startColumn": 25, "endLineNumber": 2511, "endColumn": 40}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.Conversation already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2521:25", "source": "compiler", "startLineNumber": 2516, "startColumn": 25, "endLineNumber": 2516, "endColumn": 37}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.ConversationMessages already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2526:25", "source": "compiler", "startLineNumber": 2521, "startColumn": 25, "endLineNumber": 2521, "endColumn": 45}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.ExternalAPIs already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2531:25", "source": "compiler", "startLineNumber": 2526, "startColumn": 25, "endLineNumber": 2526, "endColumn": 37}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.ExternalAPI already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2536:25", "source": "compiler", "startLineNumber": 2531, "startColumn": 25, "endLineNumber": 2531, "endColumn": 36}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.MyAPICredentials already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2541:25", "source": "compiler", "startLineNumber": 2536, "startColumn": 25, "endLineNumber": 2536, "endColumn": 41}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.APIConnections already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2546:25", "source": "compiler", "startLineNumber": 2541, "startColumn": 25, "endLineNumber": 2541, "endColumn": 39}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.Me already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2551:25", "source": "compiler", "startLineNumber": 2546, "startColumn": 25, "endLineNumber": 2546, "endColumn": 27}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.Notifications already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2556:25", "source": "compiler", "startLineNumber": 2551, "startColumn": 25, "endLineNumber": 2551, "endColumn": 38}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.SiteAnalytics already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2561:25", "source": "compiler", "startLineNumber": 2556, "startColumn": 25, "endLineNumber": 2556, "endColumn": 38}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.ExpertProfiles already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2566:25", "source": "compiler", "startLineNumber": 2561, "startColumn": 25, "endLineNumber": 2561, "endColumn": 39}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.ExpertProfile already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2571:25", "source": "compiler", "startLineNumber": 2566, "startColumn": 25, "endLineNumber": 2566, "endColumn": 38}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.MyExpertProfile already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2576:25", "source": "compiler", "startLineNumber": 2571, "startColumn": 25, "endLineNumber": 2571, "endColumn": 40}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.Projects already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2581:25", "source": "compiler", "startLineNumber": 2576, "startColumn": 25, "endLineNumber": 2576, "endColumn": 33}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.Project already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2586:25", "source": "compiler", "startLineNumber": 2581, "startColumn": 25, "endLineNumber": 2581, "endColumn": 32}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.MyTickets already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2591:25", "source": "compiler", "startLineNumber": 2586, "startColumn": 25, "endLineNumber": 2586, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.Ticket already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2596:25", "source": "compiler", "startLineNumber": 2591, "startColumn": 25, "endLineNumber": 2591, "endColumn": 31}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.BusinessPlan already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2601:25", "source": "compiler", "startLineNumber": 2596, "startColumn": 25, "endLineNumber": 2596, "endColumn": 37}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.FinancialPlan already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2606:25", "source": "compiler", "startLineNumber": 2601, "startColumn": 25, "endLineNumber": 2601, "endColumn": 38}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.CompetitorResearch already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2611:25", "source": "compiler", "startLineNumber": 2606, "startColumn": 25, "endLineNumber": 2606, "endColumn": 43}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.ComplianceCheck already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2616:25", "source": "compiler", "startLineNumber": 2611, "startColumn": 25, "endLineNumber": 2611, "endColumn": 40}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.AdminDashboardKPIs already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2621:25", "source": "compiler", "startLineNumber": 2616, "startColumn": 25, "endLineNumber": 2616, "endColumn": 43}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.AdminUsers already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2626:25", "source": "compiler", "startLineNumber": 2621, "startColumn": 25, "endLineNumber": 2621, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.AdminUser already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2631:25", "source": "compiler", "startLineNumber": 2626, "startColumn": 25, "endLineNumber": 2626, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.AdminUserStats already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2636:25", "source": "compiler", "startLineNumber": 2631, "startColumn": 25, "endLineNumber": 2631, "endColumn": 39}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.AdminExpertApplications already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2641:25", "source": "compiler", "startLineNumber": 2636, "startColumn": 25, "endLineNumber": 2636, "endColumn": 48}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.AdminExpertApplication already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2646:25", "source": "compiler", "startLineNumber": 2641, "startColumn": 25, "endLineNumber": 2641, "endColumn": 47}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.AdminExpertProfiles already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2651:25", "source": "compiler", "startLineNumber": 2646, "startColumn": 25, "endLineNumber": 2646, "endColumn": 44}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.AdminModerationQueue already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2656:25", "source": "compiler", "startLineNumber": 2651, "startColumn": 25, "endLineNumber": 2651, "endColumn": 45}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.AdminModerationStats already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2661:25", "source": "compiler", "startLineNumber": 2656, "startColumn": 25, "endLineNumber": 2656, "endColumn": 45}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.AdminAddonReviews already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2666:25", "source": "compiler", "startLineNumber": 2661, "startColumn": 25, "endLineNumber": 2661, "endColumn": 42}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.AdminAddonReview already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2671:25", "source": "compiler", "startLineNumber": 2666, "startColumn": 25, "endLineNumber": 2666, "endColumn": 41}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.AdminAddonReviewStats already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2676:25", "source": "compiler", "startLineNumber": 2671, "startColumn": 25, "endLineNumber": 2671, "endColumn": 46}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method queryResolver.SystemHealth already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2681:25", "source": "compiler", "startLineNumber": 2676, "startColumn": 25, "endLineNumber": 2676, "endColumn": 37}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method Resolver.Mutation already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2686:20", "source": "compiler", "startLineNumber": 2681, "startColumn": 20, "endLineNumber": 2681, "endColumn": 28}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateMethod", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateMethod"}}, "severity": 8, "message": "method Resolver.Query already declared at C:\\Users\\<USER>\\Desktop\\goVwPlatformAPI\\graph\\resolver.go:2689:20", "source": "compiler", "startLineNumber": 2684, "startColumn": 20, "endLineNumber": 2684, "endColumn": 25}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateDecl", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateDecl"}}, "severity": 8, "message": "mutationResolver redeclared in this block", "source": "compiler", "startLineNumber": 2686, "startColumn": 6, "endLineNumber": 2686, "endColumn": 22, "relatedInformation": [{"startLineNumber": 2691, "startColumn": 6, "endLineNumber": 2691, "endColumn": 22, "message": "other declaration of mutationResolver", "resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go"}]}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/schema.resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateDecl", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateDecl"}}, "severity": 8, "message": "queryResolver redeclared in this block", "source": "compiler", "startLineNumber": 2687, "startColumn": 6, "endLineNumber": 2687, "endColumn": 19, "relatedInformation": [{"startLineNumber": 2692, "startColumn": 6, "endLineNumber": 2692, "endColumn": 19, "message": "other declaration of queryResolver", "resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go"}]}]