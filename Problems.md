[{"resource": "/c:/Users/<USER>/Desktop/goVwPlatformAPI/cmd/api/main.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: generated", "source": "compiler", "startLineNumber": 1285, "startColumn": 21, "endLineNumber": 1285, "endColumn": 30}, {"resource": "/c:/Users/<USER>/Desktop/goVwPlatformAPI/cmd/api/main.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: generated", "source": "compiler", "startLineNumber": 1285, "startColumn": 51, "endLineNumber": 1285, "endColumn": 60}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: generated", "source": "compiler", "startLineNumber": 1053, "startColumn": 31, "endLineNumber": 1053, "endColumn": 40}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "UndeclaredName", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "UndeclaredName"}}, "severity": 8, "message": "undefined: generated", "source": "compiler", "startLineNumber": 1056, "startColumn": 28, "endLineNumber": 1056, "endColumn": 37}]