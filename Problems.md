[{"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateDecl", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateDecl"}}, "severity": 8, "message": "duplicate method GenerateSiteAPIKey", "source": "compiler", "startLineNumber": 1934, "startColumn": 2, "endLineNumber": 1934, "endColumn": 20, "relatedInformation": [{"startLineNumber": 2029, "startColumn": 2, "endLineNumber": 2029, "endColumn": 20, "message": "other declaration of method GenerateSiteAPIKey", "resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go"}]}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "DuplicateDecl", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "DuplicateDecl"}}, "severity": 8, "message": "duplicate method GenerateSiteAPIKey (see details)", "source": "compiler", "startLineNumber": 2029, "startColumn": 2, "endLineNumber": 2029, "endColumn": 20, "relatedInformation": [{"startLineNumber": 1934, "startColumn": 2, "endLineNumber": 1934, "endColumn": 20, "message": "", "resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/generated/generated.go"}]}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use site.PageCount (variable of type int) as int32 value in struct literal", "source": "compiler", "startLineNumber": 682, "startColumn": 17, "endLineNumber": 682, "endColumn": 31}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use site.PageCount (variable of type int) as int32 value in struct literal", "source": "compiler", "startLineNumber": 750, "startColumn": 17, "endLineNumber": 750, "endColumn": 31}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use input.Position (variable of type *int32) as *int value in struct literal", "source": "compiler", "startLineNumber": 860, "startColumn": 20, "endLineNumber": 860, "endColumn": 34}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use page.Position (variable of type int) as int32 value in struct literal", "source": "compiler", "startLineNumber": 896, "startColumn": 20, "endLineNumber": 896, "endColumn": 33}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use page.ContentVersion (variable of type int) as int32 value in struct literal", "source": "compiler", "startLineNumber": 910, "startColumn": 20, "endLineNumber": 910, "endColumn": 39}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use tenant.MaxUsers (variable of type int) as int32 value in struct literal", "source": "compiler", "startLineNumber": 1191, "startColumn": 14, "endLineNumber": 1191, "endColumn": 29}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use tenant.MaxSites (variable of type int) as int32 value in struct literal", "source": "compiler", "startLineNumber": 1192, "startColumn": 14, "endLineNumber": 1192, "endColumn": 29}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use tenant.MaxUsers (variable of type int) as int32 value in struct literal", "source": "compiler", "startLineNumber": 1254, "startColumn": 14, "endLineNumber": 1254, "endColumn": 29}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use tenant.MaxSites (variable of type int) as int32 value in struct literal", "source": "compiler", "startLineNumber": 1255, "startColumn": 14, "endLineNumber": 1255, "endColumn": 29}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use progressStep.Order (variable of type int) as int32 value in struct literal", "source": "compiler", "startLineNumber": 1429, "startColumn": 17, "endLineNumber": 1429, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use progress.CurrentStep (variable of type int) as int32 value in struct literal", "source": "compiler", "startLineNumber": 1438, "startColumn": 19, "endLineNumber": 1438, "endColumn": 39}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use progress.TotalSteps (variable of type int) as int32 value in struct literal", "source": "compiler", "startLineNumber": 1439, "startColumn": 19, "endLineNumber": 1439, "endColumn": 38}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use progress.CompletedSteps (variable of type int) as int32 value in struct literal", "source": "compiler", "startLineNumber": 1440, "startColumn": 19, "endLineNumber": 1440, "endColumn": 42}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use tenant.MaxUsers (variable of type int) as int32 value in struct literal", "source": "compiler", "startLineNumber": 1824, "startColumn": 15, "endLineNumber": 1824, "endColumn": 30}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use tenant.MaxSites (variable of type int) as int32 value in struct literal", "source": "compiler", "startLineNumber": 1825, "startColumn": 15, "endLineNumber": 1825, "endColumn": 30}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use tenant.MaxUsers (variable of type int) as int32 value in struct literal", "source": "compiler", "startLineNumber": 1860, "startColumn": 14, "endLineNumber": 1860, "endColumn": 29}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use tenant.MaxSites (variable of type int) as int32 value in struct literal", "source": "compiler", "startLineNumber": 1861, "startColumn": 14, "endLineNumber": 1861, "endColumn": 29}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use stats[\"user_count\"].(int) (comma, ok expression of type int) as int32 value in struct literal", "source": "compiler", "startLineNumber": 1887, "startColumn": 15, "endLineNumber": 1887, "endColumn": 40}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use stats[\"site_count\"].(int) (comma, ok expression of type int) as int32 value in struct literal", "source": "compiler", "startLineNumber": 1888, "startColumn": 15, "endLineNumber": 1888, "endColumn": 40}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use stats[\"page_count\"].(int) (comma, ok expression of type int) as int32 value in struct literal", "source": "compiler", "startLineNumber": 1889, "startColumn": 15, "endLineNumber": 1889, "endColumn": 40}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use stats[\"addon_count\"].(int) (comma, ok expression of type int) as int32 value in struct literal", "source": "compiler", "startLineNumber": 1890, "startColumn": 15, "endLineNumber": 1890, "endColumn": 41}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use progressStep.Order (variable of type int) as int32 value in struct literal", "source": "compiler", "startLineNumber": 2037, "startColumn": 17, "endLineNumber": 2037, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use progress.CurrentStep (variable of type int) as int32 value in struct literal", "source": "compiler", "startLineNumber": 2046, "startColumn": 19, "endLineNumber": 2046, "endColumn": 39}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use progress.TotalSteps (variable of type int) as int32 value in struct literal", "source": "compiler", "startLineNumber": 2047, "startColumn": 19, "endLineNumber": 2047, "endColumn": 38}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use progress.CompletedSteps (variable of type int) as int32 value in struct literal", "source": "compiler", "startLineNumber": 2048, "startColumn": 19, "endLineNumber": 2048, "endColumn": 42}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use stats.TotalSites (variable of type int) as int32 value in struct literal", "source": "compiler", "startLineNumber": 2141, "startColumn": 21, "endLineNumber": 2141, "endColumn": 37}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use stats.TotalPages (variable of type int) as int32 value in struct literal", "source": "compiler", "startLineNumber": 2142, "startColumn": 21, "endLineNumber": 2142, "endColumn": 37}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use stats.TotalAddons (variable of type int) as int32 value in struct literal", "source": "compiler", "startLineNumber": 2143, "startColumn": 21, "endLineNumber": 2143, "endColumn": 38}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use stats.APICallsUsed (variable of type int) as int32 value in struct literal", "source": "compiler", "startLineNumber": 2148, "startColumn": 21, "endLineNumber": 2148, "endColumn": 39}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use site.PageCount (variable of type int) as int32 value in struct literal", "source": "compiler", "startLineNumber": 2185, "startColumn": 18, "endLineNumber": 2185, "endColumn": 32}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use sp.Position (variable of type int) as int32 value in struct literal", "source": "compiler", "startLineNumber": 2233, "startColumn": 16, "endLineNumber": 2233, "endColumn": 27}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use sp.Page.Position (variable of type int) as int32 value in struct literal", "source": "compiler", "startLineNumber": 2244, "startColumn": 16, "endLineNumber": 2244, "endColumn": 32}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use site.PageCount (variable of type int) as int32 value in struct literal", "source": "compiler", "startLineNumber": 2267, "startColumn": 17, "endLineNumber": 2267, "endColumn": 31}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use sp.Position (variable of type int) as int32 value in struct literal", "source": "compiler", "startLineNumber": 2302, "startColumn": 16, "endLineNumber": 2302, "endColumn": 27}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use sp.Page.Position (variable of type int) as int32 value in struct literal", "source": "compiler", "startLineNumber": 2313, "startColumn": 16, "endLineNumber": 2313, "endColumn": 32}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use page.Position (variable of type int) as int32 value in struct literal", "source": "compiler", "startLineNumber": 2349, "startColumn": 20, "endLineNumber": 2349, "endColumn": 33}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use page.ContentVersion (variable of type int) as int32 value in struct literal", "source": "compiler", "startLineNumber": 2363, "startColumn": 20, "endLineNumber": 2363, "endColumn": 39}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use page.Position (variable of type int) as int32 value in struct literal", "source": "compiler", "startLineNumber": 2401, "startColumn": 22, "endLineNumber": 2401, "endColumn": 35}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use page.ContentVersion (variable of type int) as int32 value in struct literal", "source": "compiler", "startLineNumber": 2415, "startColumn": 22, "endLineNumber": 2415, "endColumn": 41}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "InvalidIfaceAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "InvalidIfaceAssign"}}, "severity": 8, "message": "cannot use &mutationResolver{…} (value of type *mutationResolver) as generated.MutationResolver value in return statement: *mutationResolver does not implement generated.MutationResolver (wrong type for method GenerateSiteAPIKey)\n\t\thave GenerateSiteAP<PERSON>Key(context.Context, string, int) (*model.<PERSON>K<PERSON>, error)\n\t\twant GenerateSiteAPIKey(context.Context, string, int32) (*model.APIKey, error)", "source": "compiler", "startLineNumber": 2697, "startColumn": 67, "endLineNumber": 2697, "endColumn": 87}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/graph/resolver.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "InvalidIfaceAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "InvalidIfaceAssign"}}, "severity": 8, "message": "cannot use &queryResolver{…} (value of type *queryResolver) as generated.QueryResolver value in return statement: *queryResolver does not implement generated.QueryResolver (wrong type for method AdminAddonReviews)\n\t\thave AdminAddonReviews(context.Context, *model.AddonReviewFiltersInput, *int, *int) (*model.AddonReviewListResponse, error)\n\t\twant AdminAddonReviews(context.Context, *model.AddonReviewFiltersInput, *int32, *int32) (*model.AddonReviewListResponse, error)", "source": "compiler", "startLineNumber": 2700, "startColumn": 61, "endLineNumber": 2700, "endColumn": 78}, {"resource": "/C:/Users/<USER>/Desktop/goVwPlatformAPI/internal/resolvers/business_tools_resolvers.go", "owner": "_generated_diagnostic_collection_name_#2", "code": {"value": "IncompatibleAssign", "target": {"$mid": 1, "path": "/golang.org/x/tools/internal/typesinternal", "scheme": "https", "authority": "pkg.go.dev", "fragment": "IncompatibleAssign"}}, "severity": 8, "message": "cannot use section.Order (variable of type int) as int32 value in struct literal", "source": "compiler", "startLineNumber": 268, "startColumn": 13, "endLineNumber": 268, "endColumn": 26}]